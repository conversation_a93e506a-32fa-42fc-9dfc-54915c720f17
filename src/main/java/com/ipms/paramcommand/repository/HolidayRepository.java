package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.Holiday;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface HolidayRepository extends CrudRepository<Holiday, Long> {
    Optional<Holiday> findFirstByDate(LocalDate date);
    List<Holiday> findByDateBetween (LocalDate start, LocalDate end);
}
