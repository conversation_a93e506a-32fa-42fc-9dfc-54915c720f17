package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.IssueTypeDto;
import com.ipms.paramcommand.dto.IssueTypeFilterRequest;
import com.ipms.paramcommand.dto.IssueTypePageDto;
import com.ipms.paramcommand.service.IssueTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;

@RequiredArgsConstructor
@RestController
@RequestMapping("issue-type")
public class IssueTypeController {
    private final IssueTypeService service;

    @PostMapping
    public BaseResponse<IssueTypeDto> save(@Valid @RequestBody IssueTypeDto dto) {
        return BaseResponse.<IssueTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<IssueTypeDto> update(@PathVariable Long id,
                                             @Valid @RequestBody IssueTypeDto dto) {
        return BaseResponse.<IssueTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.update(dto, id))
                .build();
    }

    @GetMapping("/{key}")
    public BaseResponse<IssueTypeDto> getByKey(@PathVariable String key) {
        return BaseResponse.<IssueTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByKey(key))
                .build();
    }

    @GetMapping("/with-date/{key}")
    public BaseResponse<IssueTypeDto> getByKeyWithDates(@PathVariable String key,
                                                        @RequestParam(required = false, name = "date")
                                                        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime date) {
        return BaseResponse.<IssueTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByKeyWithDates(key, date))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<IssueTypePageDto> getAll(IssueTypeFilterRequest request,
                                                   @PathVariable int page,
                                                   @PathVariable int size) {
        return BaseResponse.<IssueTypePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(request, page, size))
                .build();
    }
}
