package com.ipms.activity.repository;

import com.ipms.activity.model.ComponentCourt;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ComponentCourtRepository extends CrudRepository<ComponentCourt, Long>, JpaSpecificationExecutor<ComponentCourt> {
    List<ComponentCourt> findByActivityId(Long activityId);
    List<ComponentCourt> findByActivityIdIn(List<Long> activityIds);
    ComponentCourt findFirstByDocketNo(String docketNo);
}
