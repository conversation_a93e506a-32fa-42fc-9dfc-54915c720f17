package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.ActivityStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ActivityStatusRepository extends PagingAndSortingRepository<ActivityStatus, Long> {
    Page<ActivityStatus> findByActivityTypeId(Long typeId, Pageable pageable);
}
