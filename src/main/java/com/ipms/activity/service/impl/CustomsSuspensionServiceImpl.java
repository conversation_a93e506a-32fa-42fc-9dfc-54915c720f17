package com.ipms.activity.service.impl;

import com.ipms.activity.dto.ActivityDto;
import com.ipms.activity.dto.CustomsSuspensionsReceiverDto;
import com.ipms.activity.dto.CustomsSuspensionsSenderDto;
import com.ipms.activity.dto.CustomsSuspensionsShippingDto;
import com.ipms.activity.model.*;
import com.ipms.activity.repository.CustomSuspensionsReceiverRepository;
import com.ipms.activity.repository.CustomSuspensionsSenderRepository;
import com.ipms.activity.repository.CustomSuspensionsShippingRepository;
import com.ipms.activity.service.CustomsSuspensionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Service
public class CustomsSuspensionServiceImpl implements CustomsSuspensionService {
    private final CustomSuspensionsSenderRepository senderRepository;
    private final CustomSuspensionsReceiverRepository receiverRepository;
    private final CustomSuspensionsShippingRepository shippingRepository;

    @Override
    public CustomsSuspension setCustomSuspensionToUpdateActivity(ActivityDto dto, Activity activity) {
        var customsSuspension = activity.getCustomsSuspension();
        if (Objects.nonNull(dto.getCustomsSuspension()) && Objects.nonNull(dto.getCustomsSuspension().getSenders())){
            List<CustomsSuspensionsSenderDto> senderDtos = dto.getCustomsSuspension().getSenders();
            List<CustomsSuspensionsSender> senders = new ArrayList<>();
            senderDtos.forEach(senderDto -> senders.add(toSenderEntity(senderDto, activity.getCustomsSuspension())));
            customsSuspension.setSenders(senders);
        }

        if (Objects.nonNull(dto.getCustomsSuspension()) && Objects.nonNull(dto.getCustomsSuspension().getReceivers())){
            List<CustomsSuspensionsReceiverDto> receiverDtos = dto.getCustomsSuspension().getReceivers();
            List<CustomsSuspensionsReceiver> receivers = new ArrayList<>();
            receiverDtos.forEach(receiverDto -> receivers
                    .add(toReceiverEntity(receiverDto, activity.getCustomsSuspension())));
            customsSuspension.setReceivers(receivers);
        }

        if(Objects.nonNull(dto.getCustomsSuspension()) && Objects.nonNull(dto.getCustomsSuspension().getShippings())){
            List<CustomsSuspensionsShippingDto> shippingDtos = dto.getCustomsSuspension().getShippings();
            List<CustomsSuspensionsShipping> shippings = new ArrayList<>();
            shippingDtos.forEach(shippingDto -> shippings
                    .add(toShippingEntity(shippingDto, activity.getCustomsSuspension())));
            customsSuspension.setShippings(shippings);
        }

        return customsSuspension;

    }

    @Override
    public CustomsSuspension setCustomSuspensionToSaveActivity(ActivityDto dto, Activity activity) {
        var customsSuspension = activity.getCustomsSuspension();
        if (Objects.nonNull(dto.getCustomsSuspension().getSenders())){
            List<CustomsSuspensionsSenderDto> senderDtos = dto.getCustomsSuspension().getSenders();
            List<CustomsSuspensionsSender> senders = new ArrayList<>();
            senderDtos.forEach(senderDto -> {
                if (Objects.nonNull(senderDto.getId()) && senderRepository.existsById(senderDto.getId())) {
                    senders.add(senderRepository.findById(senderDto.getId()).orElseThrow(EntityNotFoundException::new));
                }
                else {
                    senders.add(toSenderEntity(senderDto, activity.getCustomsSuspension()));
                }
            });
            customsSuspension.setSenders(senders);
        }

        if (Objects.nonNull(dto.getCustomsSuspension().getReceivers())){
            List<CustomsSuspensionsReceiverDto> receiverDtos = dto.getCustomsSuspension().getReceivers();
            List<CustomsSuspensionsReceiver> receivers = new ArrayList<>();
            receiverDtos.forEach(receiverDto -> {
                if (Objects.nonNull(receiverDto.getId()) && receiverRepository.existsById(receiverDto.getId())){
                    receivers.add(receiverRepository
                            .findById(receiverDto.getId()).orElseThrow(EntityNotFoundException::new));
                } else {
                    receivers
                            .add(toReceiverEntity(receiverDto, activity.getCustomsSuspension()));
                }
                customsSuspension.setReceivers(receivers);
            });
        }

        if(Objects.nonNull(dto.getCustomsSuspension().getShippings())){
            List<CustomsSuspensionsShippingDto> shippingDtos = dto.getCustomsSuspension().getShippings();
            List<CustomsSuspensionsShipping> shippings = new ArrayList<>();
            shippingDtos.forEach(shippingDto -> {
                if (Objects.nonNull(shippingDto.getId()) && shippingRepository.existsById(shippingDto.getId())){
                    shippings.add(shippingRepository
                            .findById(shippingDto.getId()).orElseThrow(EntityNotFoundException::new));
                } else {
                    shippings
                            .add(toShippingEntity(shippingDto, activity.getCustomsSuspension()));
                }
                customsSuspension.setShippings(shippings);
            });
        }

        customsSuspension.setActivity(activity);
        return customsSuspension;
    }

    private CustomsSuspensionsSender toSenderEntity(CustomsSuspensionsSenderDto dto, CustomsSuspension customsSuspension) {
        if (Objects.nonNull(dto) && Objects.nonNull(dto.getId())) {
            var sender = senderRepository.findById(dto.getId()).orElseThrow(EntityNotFoundException::new);
            List<CustomsSuspension> customsSuspensions = sender.getCustomsSuspensions();
            customsSuspensions.add(customsSuspension);
            sender.setCustomsSuspensions(customsSuspensions);
            return sender;
        } else if (Objects.nonNull(dto)) {
            var sender = new CustomsSuspensionsSender();
            sender.setName(dto.getName());
            List<CustomsSuspension> customsSuspensions = new ArrayList<>();
            customsSuspensions.add(customsSuspension);
            sender.setCustomsSuspensions(customsSuspensions);
            return sender;
        } else return null;
    }

    private CustomsSuspensionsReceiver toReceiverEntity(CustomsSuspensionsReceiverDto dto, CustomsSuspension customsSuspension) {
        if (Objects.nonNull(dto) && Objects.nonNull(dto.getId())) {
            var receiver = receiverRepository.findById(dto.getId()).orElseThrow(EntityNotFoundException::new);
            List<CustomsSuspension> customsSuspensions = receiver.getCustomsSuspensions();
            customsSuspensions.add(customsSuspension);
            receiver.setCustomsSuspensions(customsSuspensions);
            return receiver;
        } else if (Objects.nonNull(dto)) {
            var receiver = new CustomsSuspensionsReceiver();
            receiver.setName(dto.getName());
            List<CustomsSuspension> customsSuspensions = new ArrayList<>();
            customsSuspensions.add(customsSuspension);
            receiver.setCustomsSuspensions(customsSuspensions);
            return receiver;
        } else return null;
    }

    private CustomsSuspensionsShipping toShippingEntity(CustomsSuspensionsShippingDto dto, CustomsSuspension customsSuspension) {
        if (Objects.nonNull(dto) && Objects.nonNull(dto.getId())) {
            var shipping = shippingRepository.findById(dto.getId()).orElseThrow(EntityNotFoundException::new);
            List<CustomsSuspension> customsSuspensions = shipping.getCustomsSuspensions();
            customsSuspensions.add(customsSuspension);
            shipping.setCustomsSuspensions(customsSuspensions);
            return shipping;
        } else if (Objects.nonNull(dto)) {
            var shipping = new CustomsSuspensionsShipping();
            shipping.setName(dto.getName());
            List<CustomsSuspension> customsSuspensions = new ArrayList<>();
            customsSuspensions.add(customsSuspension);
            shipping.setCustomsSuspensions(customsSuspensions);
            return shipping;
        } else return null;
    }
}
