package com.ipms.activity.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "customs_recordal_renewal")
@Entity
@Where(clause = "is_deleted='0'")
public class CustomsRecordalRenewal extends VersionedEntity {
    @Column
    private Long activityId;

    @Column
    private LocalDate renewalInstructionDate;

    @Column
    private LocalDate renewalDate;

    @Column
    private LocalDate nextRenewalDate;
}