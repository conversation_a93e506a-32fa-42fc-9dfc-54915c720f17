package com.ipms.document.specification;

import com.ipms.document.enums.DocumentType;
import com.ipms.document.model.Attachment;
import com.ipms.document.model.Document;
import com.ipms.document.repository.DocumentRepository;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-document/application.yml")
public class DocumentSpecificationIntegrationTest {
    @Autowired
    private DocumentRepository repository;

    @Before
    public void givenDocuments() {
        Document document = Document.builder()
                .type(DocumentType.AGREEMENT)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("aexample.txt")
                                .fileUniqueName("a123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .experts(Arrays.asList(1L, 2L, 3L))
                .build();
        repository.save(document);
    }

    @After
    public void deleteAll() {
        repository.deleteAll();
    }

    @Test
    public void whenFindAll_thenReturnDocumentPage() {
        Page<Document> documentPage = repository.findAll(DocumentSpecification.builder()
                 .firmId(1L)
                 .expertId(1L)
                 .types(List.of(DocumentType.AGREEMENT.name()))
                .build(), PageRequest.of(0, 10));
        Predicate<Page<Document>> predicate = page -> page.hasContent() && page.getTotalElements() == 1
                && page.getTotalPages() == 1
                && page.getContent().get(0).getType().equals(DocumentType.AGREEMENT);

        AssertionsForClassTypes.assertThat(documentPage)
                .matches(predicate, "list size must be 1 and type AGREEMENT");
    }
}
