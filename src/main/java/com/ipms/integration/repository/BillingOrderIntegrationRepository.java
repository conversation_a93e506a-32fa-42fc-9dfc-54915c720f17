package com.ipms.integration.repository;

import com.ipms.integration.enums.TransferStatus;
import com.ipms.integration.model.BillingOrderIntegration;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface BillingOrderIntegrationRepository extends JpaRepository<BillingOrderIntegration, Long> {
    List<BillingOrderIntegration> findByUpdatedAtLessThanEqualAndStatusIn(LocalDateTime time, List<TransferStatus> statusList);
    List<BillingOrderIntegration> findByStatusIn(List<TransferStatus> statusList);

    Optional<BillingOrderIntegration> findByOrderId(Long orderId);
}
