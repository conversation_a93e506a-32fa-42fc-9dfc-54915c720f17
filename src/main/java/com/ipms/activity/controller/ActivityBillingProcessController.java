package com.ipms.activity.controller;

import com.ipms.activity.dto.ActivityDto;
import com.ipms.activity.dto.ManuelBillingProcessRequest;
import com.ipms.activity.service.ActivityBillingProcessService;
import com.ipms.core.entity.Revision;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequiredArgsConstructor
public class ActivityBillingProcessController {
    private final ActivityBillingProcessService service;

    @PostMapping("/start-billing-process/{processDate}")
    public BaseResponse<Boolean> startBillingProcessByBillingDay(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate processDate) {
        service.startBillingProcessByBillingDay(processDate);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @PostMapping("/start-billing-process-by-billing-amount")
    public BaseResponse<Boolean> startBillingProcessByBillingAmount() {
        service.startBillingProcessByBillingAmount();
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @PostMapping("/start-billing-process-by-status/{processDate}")
    public BaseResponse<Boolean> startBillingProcessByStatus(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate processDate) {
        service.startBillingProcessByStatus(processDate);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @PostMapping("/manuel-billing-process")
    public BaseResponse<Boolean> startManuelBillingProcess(@RequestBody ManuelBillingProcessRequest manuelBillingProcessRequest) {
        service.startManuelBillingProcess(manuelBillingProcessRequest.getActivityId());
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @PutMapping("/approve-billing/{issueId}")
    public BaseResponse<Void> approveBilling(@PathVariable Long issueId) {
        service.approveBilling(issueId);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @PutMapping("/undo-billing/{issueId}")
    public BaseResponse<Void> undoBilling(@PathVariable Long issueId) {
        service.undoBillingApproval(issueId);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/billing-cart-activities/{assignee}")
    public BaseResponse<List<ActivityDto>> getActivitiesForBillingCart(@PathVariable String assignee) {
        var activityDtos = service.getActivitiesForBillingCart(assignee);
        return BaseResponse.<List<ActivityDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(activityDtos)
                .build();
    }

    @PutMapping("/order-created-status/{activityId}")
    public BaseResponse<Void> setOrderCreatedStatus(@PathVariable Long activityId) {
        service.setOrderCreatedStatus(activityId);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @PutMapping("/cancel-billing-order/{activityId}")
    public BaseResponse<Void> cancelBillingOrder(@PathVariable Long activityId) {
        service.cancelBillingOrder(activityId);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/billing/revisions/{activityId}")
    public BaseResponse<List<Revision>> billingRevisions(@PathVariable Long activityId) {
        var revisions = service.getBillingRevisions(activityId);
        return BaseResponse.<List<Revision>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(revisions)
                .build();
    }

    @GetMapping("/check-billing-cancellable/{id}")
    public BaseResponse<Boolean> checkBillingCancellable(@PathVariable Long id) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.checkBillingCancellable(id))
                .build();
    }

    @GetMapping("/has-billable-record/{id}")
    public BaseResponse<Boolean> hasBillableRecord(@PathVariable Long id) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.hasBillableRecord(id))
                .build();
    }

    @GetMapping("/is-billing-order-cancellation-safe/{orderNo}")
    public BaseResponse<Boolean> isBillingOrderCancellationSafe(@PathVariable String orderNo) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.isBillingOrderCancellationSafe(orderNo))
                .build();
    }

    @PutMapping("/cancel-billing/{activityId}/{issueId}/{fillEmptyExpenseDates}")
    public BaseResponse<Void> cancelBilling(@PathVariable Long activityId, @PathVariable Long issueId,
                                            @PathVariable boolean fillEmptyExpenseDates) {
        service.cancelBilling(activityId, issueId, fillEmptyExpenseDates);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
