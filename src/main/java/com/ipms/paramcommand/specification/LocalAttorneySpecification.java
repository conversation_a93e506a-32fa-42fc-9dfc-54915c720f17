package com.ipms.paramcommand.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.paramcommand.model.LocalAttorney;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class LocalAttorneySpecification extends AbstractSpecification<LocalAttorney> {
    private final List<String> cities;
    private final String nameOrSurname;

    @Override
    public Predicate toPredicate(Root<LocalAttorney> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Optional.ofNullable(cities)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("city").in(cities))));
        Optional.ofNullable(nameOrSurname)
                .ifPresent(value -> {
                    var fullName = cb.lower(cb.concat(cb.concat(root.get("name"), " "), root.get("surname")));
                    predicate.getExpressions().add(cb.and(cb.like(fullName, "%" + value.toLowerCase() + "%")));
                });
        return predicate;
    }
}
