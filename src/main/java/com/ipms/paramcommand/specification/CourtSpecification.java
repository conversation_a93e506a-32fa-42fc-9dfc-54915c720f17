package com.ipms.paramcommand.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.paramcommand.enums.CourtKind;
import com.ipms.paramcommand.enums.CourtType;
import com.ipms.paramcommand.model.Court;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class CourtSpecification extends AbstractSpecification<Court> {
    private final List<String> types;
    private final List<String> kinds;
    private final List<String> cities;
    private final List<String> judges;
    @Override
    public Predicate toPredicate(Root<Court> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Optional.ofNullable(cities)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("city").in(cities))));
        Optional.ofNullable(types)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("type").in(types.stream().map(CourtType::valueOf).toList()))));
        Optional.ofNullable(kinds)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("kind").in(kinds.stream().map(CourtKind::valueOf).toList()))));
        Optional.ofNullable(judges)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("judges").in(judges))));
        predicate.getExpressions().add(cb.equal(root.get("isDeleted"), Boolean.FALSE));
        return predicate;
    }
}
