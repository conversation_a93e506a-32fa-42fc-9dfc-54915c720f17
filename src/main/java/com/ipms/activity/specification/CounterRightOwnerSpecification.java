package com.ipms.activity.specification;

import com.ipms.activity.model.CounterRightOwner;
import com.ipms.activity.model.CounterRightOwnerCard;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.*;
import java.util.Optional;

@SuperBuilder
public class CounterRightOwnerSpecification extends AbstractSpecification<CounterRightOwner> {
    private Long activityId;
    @Override
    public Predicate toPredicate(Root<CounterRightOwner> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Join<CounterRightOwner, CounterRightOwnerCard> cardJoin = root.join("counterRightOwnerCard", JoinType.INNER);

        Optional.ofNullable(activityId)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(cardJoin.get("activityId"), value)));
        return predicate;
    }
}
