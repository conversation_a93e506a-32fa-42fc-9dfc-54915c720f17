package com.ipms.document.service;

import com.ipms.config.storage.service.StorageService;
import com.ipms.document.dto.SampleAttachmentDto;
import com.ipms.document.enums.SampleCollectionPlace;
import com.ipms.document.enums.SampleLocation;
import com.ipms.document.enums.SampleType;
import com.ipms.document.mapper.SampleAttachmentMapper;
import com.ipms.document.mapper.SampleAttachmentMapperImpl;
import com.ipms.document.model.Sample;
import com.ipms.document.model.SampleAttachment;
import com.ipms.document.repository.SampleAttachmentRepository;
import com.ipms.document.service.impl.SampleAttachmentServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;

@RunWith(SpringRunner.class)
public class SampleAttachmentServiceTest {

    @MockBean
    private SampleAttachmentService service;

    @Mock
    private SampleAttachmentRepository repository;

    @Mock
    private StorageService storageService;

    @Mock
    private SampleService sampleService;

    private final SampleAttachmentMapper mapper = new SampleAttachmentMapperImpl();

    @Before
    public void setUp() {
        service = new SampleAttachmentServiceImpl(repository, mapper, storageService, sampleService);
    }

    @Test
    public void whenSave_thenReturnEvidenceAttachmentDto() {
        var attachment = SampleAttachment.builder()
                .id(1L)
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var attachmentDto = SampleAttachmentDto.builder()
                .id(1L)
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var sample = Sample.builder()
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .attachments(List.of(attachment))
                .name("test-name")
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();

        Mockito.when(sampleService.getSampleById(Mockito.anyLong()))
                .thenReturn(sample);
        Mockito.when(repository.save(Mockito.any(SampleAttachment.class)))
                .thenReturn(attachment);

        assertThat(service.save(attachmentDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenDelete_thenThrowDoesNotThrowAnyException() {
        var attachment = SampleAttachment.builder()
                .id(1L)
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(attachment));

        assertThatCode(() -> service.delete(1L, 0L))
                .doesNotThrowAnyException();
    }
}
