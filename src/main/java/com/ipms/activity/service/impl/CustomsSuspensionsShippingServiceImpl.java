package com.ipms.activity.service.impl;

import com.ipms.activity.dto.CustomsSuspensionsShippingDto;
import com.ipms.activity.mapper.CustomsSuspensionShippingMapper;
import com.ipms.activity.repository.CustomSuspensionsShippingRepository;
import com.ipms.activity.service.CustomsSuspensionsShippingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class CustomsSuspensionsShippingServiceImpl implements CustomsSuspensionsShippingService {
    private final CustomSuspensionsShippingRepository repository;
    private final CustomsSuspensionShippingMapper mapper;

    @Override
    public List<CustomsSuspensionsShippingDto> findAllByName(String name) {
        var shippings = repository.findAllByNameContainingIgnoreCase(name);
        List<CustomsSuspensionsShippingDto> dtos = new ArrayList<>();
        shippings.forEach(receiver -> dtos.add(mapper.toDto(receiver)));
        return dtos;
    }
}
