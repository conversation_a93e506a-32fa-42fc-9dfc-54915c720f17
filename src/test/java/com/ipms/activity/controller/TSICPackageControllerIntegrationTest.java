package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.activity.dto.TSICPackageDto;
import com.ipms.activity.service.TSICPackageService;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class TSICPackageControllerIntegrationTest {

    @Autowired
    protected MockMvc mvc;

    @MockBean
    private TSICPackageService service;

    ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    @Test
    @SneakyThrows
    public void givenActivityId_whenGetAllByActivityId_thenReturnBaseResponse() {
        when(service.getAllByActivityId(anyLong()))
                .thenReturn(List.of(createPackageDto(), createPackageDto()));

        mvc.perform(get("/tsic-package/by-activity-id/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    @SneakyThrows
    public void givenTsicPackageDto_whenSave_thenReturnBaseResponse() {
        when(service.save(anyLong(), any(TSICPackageDto.class))).thenReturn(createPackageDto());

        mvc.perform(post("/tsic-package/1")
                        .content(objectMapper.writeValueAsString(createPackageDto()))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    @SneakyThrows
    public void givenTsicPackageDto_whenUpdate_thenReturnBaseResponse() {
        when(service.update(anyLong(), any(TSICPackageDto.class))).thenReturn(createPackageDto());

        mvc.perform(put("/tsic-package/1")
                        .content(objectMapper.writeValueAsString(createPackageDto()))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    @SneakyThrows
    public void givenIdAndVersion_whenDelete_thenReturnBaseResponse() {
        service.delete(anyLong(), anyLong());

        mvc.perform(delete("/tsic-package/1/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    private static TSICPackageDto createPackageDto() {
        return TSICPackageDto.builder()
                .id(1L)
                .version(0L)
                .startDate(LocalDate.now())
                .endDate(LocalDate.now())
                .includedCharge(BigDecimal.TEN)
                .fixedFee(true)
                .invoiced(true)
                .build();
    }

    @Test
    public void givenActivityId_whenGetActivePackage_thenReturnBaseResponse() throws Exception {
        when(service.getActivePackageByActivityId(anyLong())).thenReturn(createPackageDto());

        mvc.perform(get("/tsic-package/active?activityId=1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(service, times(1)).getActivePackageByActivityId(1L);
    }

}