package com.ipms.activity.enums;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum CounterRightOwnerCardType {
    COUNTER_RIGHT_OWNER_CARD(Values.COUNTER_RIGHT_OWNER_CARD),
    COUNTER_PARTY(Values.COUNTER_PARTY);

    private final String value;

    public static class Values {
        public static final String COUNTER_RIGHT_OWNER_CARD = "COUNTER_RIGHT_OWNER_CARD";
        public static final String COUNTER_PARTY = "COUNTER_PARTY";
    }
}
