package com.ipms.activity.service.impl;

import com.ipms.activity.dto.LawyerDto;
import com.ipms.activity.exception.LawyerNotFoundException;
import com.ipms.activity.mapper.LawyerMapper;
import com.ipms.activity.model.Lawyer;
import com.ipms.activity.repository.LawyerRepository;
import com.ipms.activity.service.AgencyService;
import com.ipms.activity.service.CounterRightOwnerCardService;
import com.ipms.activity.service.LawyerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@RequiredArgsConstructor
@Service
public class LawyerServiceImpl implements LawyerService {

    private final LawyerRepository repository;
    private final LawyerMapper mapper;
    private final AgencyService agencyService;
    private final CounterRightOwnerCardService counterRightOwnerCardService;

    @Override
    public LawyerDto addToCounterRightOwnerCard(LawyerDto dto, Long counterRightOwnerCardId) {
        var counterRightOwnerCard = counterRightOwnerCardService.getCounterRightOwnerCardById(counterRightOwnerCardId);
        var lawyer = mapper.toLawyer(dto);
        Optional.ofNullable(dto.getAgency()).ifPresent(agencyDto -> {
            var agency = agencyService.save(agencyDto);
            lawyer.setAgencies(Set.of(agency));
        });
        lawyer.setCounterRightOwnerCard(counterRightOwnerCard);
        var saved = repository.save(lawyer);
        return mapper.toLawyerDto(saved);
    }

    @Override
    public LawyerDto update(LawyerDto dto, Long id) {
        var lawyer = mapper.toLawyerFromDto(dto, getById(id).toBuilder().build());
        Optional.ofNullable(dto.getAgency()).ifPresent(agencyDto -> {
            var agency = agencyService.save(agencyDto);
            lawyer.setAgencies(Set.of(agency));
        });
        var saved = repository.save(lawyer);
        return mapper.toLawyerDto(saved);
    }

    @Override
    public void delete(Long id, Long version) {
        var lawyer = getById(id).toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(lawyer);
    }

    @Override
    public List<LawyerDto> getByCounterRightOwnerCardId(Long id) {
        return repository.findByCounterRightOwnerCardId(id)
                .stream()
                .map(mapper::toLawyerDto)
                .toList();
    }

    private Lawyer getById(Long id) {
        return repository.findById(id)
                .orElseThrow(LawyerNotFoundException::new);
    }
}
