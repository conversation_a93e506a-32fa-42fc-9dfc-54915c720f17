package com.ipms.activity.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import java.util.Set;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "agency")
@Entity
@Where(clause = "is_deleted='0'")
public class Agency extends VersionedEntity {

    @Column
    private String name;

    @Column
    private String email;

    @Column
    private String city;

    @Column
    private String country;

    @ManyToMany(mappedBy = "agencies")
    private Set<Lawyer> lawyers;
}
