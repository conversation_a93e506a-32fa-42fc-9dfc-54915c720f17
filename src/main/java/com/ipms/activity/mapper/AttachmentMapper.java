package com.ipms.activity.mapper;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.model.*;
import com.ipms.config.storage.model.StorageFile;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface AttachmentMapper {

    StorageFile toStorageFile(AttachmentDto attachmentDto);

    AttachmentDto toAttachmentDto(RefundedAttachment attachment);
    AttachmentDto toAttachmentDto(ReleasedAttachment attachment);
    AttachmentDto toAttachmentDto(ReleasedAttachment attachment, String fileSignedUrl);
    AttachmentDto toAttachmentDto(RefundedAttachment attachment, String fileSignedUrl);
    AttachmentDto toAttachmentDto(PIMinutesAttachment attachment);
    AttachmentDto toAttachmentDto(PIMinutesAttachment attachment, String fileSignedUrl);
    AttachmentDto toAttachmentDto(ProformaDebitLetterAttachment attachment);
    AttachmentDto toAttachmentDto(ProformaDebitLetterAttachment attachment, String fileSignedUrl);
    AttachmentDto toAttachmentDto(CashierReceiptAttachment attachment);
    AttachmentDto toAttachmentDto(CashierReceiptAttachment attachment, String fileSignedUrl);
    AttachmentDto toAttachmentDto(LetterOfGuaranteeAttachment attachment);
    AttachmentDto toAttachmentDto(LetterOfGuaranteeAttachment attachment, String fileSignedUrl);
    AttachmentDto toAttachmentDto(ReceiptAttachment attachment);
    AttachmentDto toAttachmentDto(ReceiptAttachment attachment, String fileSignedUrl);
    ReleasedAttachment toReleasedAttachment(AttachmentDto dto);
    ReleasedAttachment toReleasedAttachmentFromDto(AttachmentDto dto, @MappingTarget ReleasedAttachment releasedAttachment);
    RefundedAttachment toRefundedAttachment(AttachmentDto dto);
    RefundedAttachment toRefundedAttachmentFromDto(AttachmentDto dto, @MappingTarget RefundedAttachment refundedAttachment);
    PIMinutesAttachment toPIMinutesAttachment(AttachmentDto dto);
    PIMinutesAttachment toPIMinutesAttachmentFromDto(AttachmentDto dto, @MappingTarget PIMinutesAttachment piMinutesAttachment);
    ProformaDebitLetterAttachment toProformaDebitLetterAttachment(AttachmentDto dto);
    ProformaDebitLetterAttachment toProformaDebitLetterAttachmentFromDto(AttachmentDto dto,
                                                                         @MappingTarget
                                                                         ProformaDebitLetterAttachment
                                                                                 proformaDebitLetterAttachment);
    CashierReceiptAttachment toCashierReceiptAttachment(AttachmentDto dto);
    CashierReceiptAttachment toCashierReceiptAttachmentFromDto(AttachmentDto dto, @MappingTarget CashierReceiptAttachment
            cashierReceiptAttachment);
    LetterOfGuaranteeAttachment toLetterOfGuaranteeAttachment(AttachmentDto dto);
    LetterOfGuaranteeAttachment toLetterOfGuaranteeAttachmentFromDto(AttachmentDto dto, @MappingTarget
    LetterOfGuaranteeAttachment letterOfGuaranteeAttachment);
    ReceiptAttachment toReceiptAttachment(AttachmentDto dto);
    ReceiptAttachment toReceiptAttachmentFromDto(AttachmentDto dto, @MappingTarget ReceiptAttachment receiptAttachment);
}