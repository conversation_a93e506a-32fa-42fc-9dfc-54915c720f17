package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.ParameterDto;
import com.ipms.paramcommand.dto.groups.UpdateGroup;
import com.ipms.paramcommand.service.ParameterService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@Tag(name = "parameter", description = "This endpoints contains parameter APIs")
public class ParameterController {

    private final ParameterService service;

    @PostMapping
    public BaseResponse<ParameterDto> save(@Valid @RequestBody ParameterDto dto) {
        return BaseResponse.<ParameterDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<ParameterDto> update(@RequestBody @Validated(UpdateGroup.class) ParameterDto parameterDto,
                                             @PathVariable Long id){
        return BaseResponse.<ParameterDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.update(parameterDto, id))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/{key}")
    public BaseResponse<ParameterDto> getByKey(@PathVariable String key) {
        return BaseResponse.<ParameterDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByKey(key))
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/")
    public BaseResponse<List<ParameterDto>> getAll() {
        return BaseResponse.<List<ParameterDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll())
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
