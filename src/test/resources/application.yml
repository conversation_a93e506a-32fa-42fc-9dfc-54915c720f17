eureka:
  client:
    enabled: false
server:
  servlet:
    context-path: /api/billing
keycloak:
  realm: ipms-default
  auth-server-url: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth
  ssl-required: external
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn

spring:
  application:
    name: ipms-billing
  cloud:
    config:
      enabled: false
  datasource:
    url: jdbc:h2:file:~/test
    username: sa
    password:
    driverClassName: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create
    show-sql: true
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth/realms/ipms-default/protocol/openid-connect/token

azure:
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=ipmstestsa;AccountKey=****************************************************************************************;
    container-name: ipmstestcontainer
    sas-host-url: https://ipmstestsa.blob.core.windows.net/ipmstestcontainer/

kafka:
  server: localhost:29092
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  billing-order-integration-saved-topic: billingOrderIntegrationSavedTopic
  billing-order-integration-update-topic: billingOrderIntegrationUpdateTopic
  invoice-fetched-topic: invoiceFetchedTopic
  billing-order-cancelled-topic: billingOrderCancelledTopic
  billing-approved-topic: billingApprovedTopic
  jde-price-topic: jdePriceTopic
  risk-impact-update-topic: riskImpactUpdateTopic
  billing-order-integration-success-topic: billingOrderIntegrationSuccessTopic
  billing-order-integration-error-topic: billingOrderIntegrationErrorTopic
  invoice-updated-topic: invoiceUpdatedTopic
  timesheet-changed-topic: timesheetChangedTopic

param:
  default-currency: DEFAULT_CURRENCY
  auto-billing-amount: AUTO_BILLING_AMOUNT