package com.ipms.document.annotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

public class StringListSizeValidator implements ConstraintValidator<StringListSize, List<String>> {
    private int max;

    @Override
    public void initialize(StringListSize constraintAnnotation) {
        this.max = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(List<String> value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        for (String str : value) {
            if (str != null && str.length() > max) {
                return false;
            }
        }

        return true;
    }
}
