package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.LocalAttorneyDto;
import com.ipms.paramcommand.dto.LocalAttorneyFilterRequest;
import com.ipms.paramcommand.dto.LocalAttorneyPageDto;
import com.ipms.paramcommand.service.impl.LocalAttorneyServiceImpl;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("local-attorney")
@Tag(name = "local-attorney", description = "This endpoints contains local-attorney APIs")
public class LocalAttorneyController {
    private final LocalAttorneyServiceImpl service;

    @PostMapping
    public BaseResponse<LocalAttorneyDto> save(@Valid @RequestBody LocalAttorneyDto dto) {
        return BaseResponse.<LocalAttorneyDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<LocalAttorneyDto> getById(@PathVariable Long id) {
        return BaseResponse.<LocalAttorneyDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<LocalAttorneyPageDto> getAll(LocalAttorneyFilterRequest filterRequest, @PathVariable int page, @PathVariable int size) {
        return BaseResponse.<LocalAttorneyPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAll(filterRequest, page, size))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<LocalAttorneyDto>> getByIds(@PathVariable List<Long> ids) {
        return BaseResponse.<List<LocalAttorneyDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.findByIdIn(ids))
                .build();
    }
}
