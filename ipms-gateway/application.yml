app:
  cors:
    allowedOrigin: "*"
    allowedHeaders: "*"
    allowedMethods: "GET, PUT, POST, DELETE, OPTIONS, PATCH, HEAD"
    maxAge: "7200"

server:
  port: 8090
spring:
  application:
    name: ipms-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true

management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health,info,metrics"
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessState:
      enabled: true
    readinessState:
      enabled: true

logging:
  level:
    reactor:
      core:
        publisher:
          Operators: OFF