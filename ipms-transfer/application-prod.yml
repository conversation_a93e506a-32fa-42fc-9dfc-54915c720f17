eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:prod-ipms-transfer}.prod-ipms-transfer
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  transfer-topic: dataTransferTopic
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic

spring:
  datasource:
    url: *********************************************************************************
    username: ${db_username}
    password: ${db_password}
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ${keycloak_ipms_backend_client_secret}
        provider:
          keycloak:
            token-uri: http://keycloak.keycloak/auth/realms/ipms-production/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web
  url: https://edderipms.com/internal-module/

keycloak:
  realm: ipms-production
  auth-server-url: http://keycloak.keycloak/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ${keycloak_ipms_backend_client_secret}


legacy:
  uri: http://**************:8046/
  user: acc_deris_prod
  password: tOP9*10\@.tuf%ZDe?

transfer:
  minute:
    activity: 10
    firm: 10
    issue: 10
    matter: 10
    timesheet: 10
    document: 10
    disbursement: 10
    expense: 10
    deadline: 10