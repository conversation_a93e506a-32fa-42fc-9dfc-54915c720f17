package com.ipms.activity.service;

import com.ipms.activity.dto.CustomsRecordalRenewalDto;
import com.ipms.activity.mapper.CustomsRecordalRenewalMapper;
import com.ipms.activity.mapper.CustomsRecordalRenewalMapperImpl;
import com.ipms.activity.model.CustomsRecordalRenewal;
import com.ipms.activity.repository.CustomsRecordalRenewalRepository;
import com.ipms.activity.service.impl.CustomsRecordalRenewalServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
public class CustomsRecordalRenewalServiceTest {

    @MockBean
    private CustomsRecordalRenewalService service;

    @Mock
    private CustomsRecordalRenewalRepository repository;

    private final CustomsRecordalRenewalMapper mapper = new CustomsRecordalRenewalMapperImpl();

    @Before
    public void setUp() {
        service = new CustomsRecordalRenewalServiceImpl(repository, mapper);
    }

    @Test
    public void givenCustomsRecordalRenewalDto_whenSave_thenReturnRecordalRenewalDto() {
        ArgumentCaptor<CustomsRecordalRenewal> savedRenewalCaptor = ArgumentCaptor.forClass(CustomsRecordalRenewal.class);
        Mockito.when(repository.save(savedRenewalCaptor.capture()))
                .thenReturn(getCustomsRecordalRenewal());

        assertEquals(getSavedCustomsRecordalRenewalDto(),service.save(getCustomsRecordalRenewalDto()));
    }

    @Test
    public void givenCustomsRecordalRenewalDtoAndId_whenUpdate_thenReturnRecordalRenewalDto() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.ofNullable(getCustomsRecordalRenewal()));

        ArgumentCaptor<CustomsRecordalRenewal> savedRenewalCaptor = ArgumentCaptor.forClass(CustomsRecordalRenewal.class);
        Mockito.when(repository.save(savedRenewalCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));

        assertEquals(getUpdatedCustomsRecordalRenewalDto(),service.update(getUpdatedCustomsRecordalRenewalDto(), 1L));
    }

    @Test
    public void givenId_whenDelete_thenReturnCustomsRecordalRenewalAsDeletedTrue() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(getCustomsRecordalRenewal()));

        ArgumentCaptor<CustomsRecordalRenewal> savedRenewalCaptor = ArgumentCaptor.forClass(CustomsRecordalRenewal.class);
        Mockito.when(repository.save(savedRenewalCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));

        service.delete(1L, 1L);
        Assertions.assertTrue(savedRenewalCaptor.getValue().isDeleted());
    }

    @Test
    public void givenId_whenGetById_thenReturnRecordalRenewalDto() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.ofNullable(getCustomsRecordalRenewal()));
       assertEquals(getSavedCustomsRecordalRenewalDto(), service.getById(1L));
    }

    @Test
    public void givenActivityIdAndPageAndSize_whenGetAllByActivity_thenReturnRecordalRenewalPageDto() {
        Page<CustomsRecordalRenewal> page = new PageImpl<>(List.of(getCustomsRecordalRenewal()), Pageable.ofSize(1), 1);
        Mockito.when(repository.findAllByActivityId(Mockito.anyLong(),Mockito.any(Pageable.class)))
                .thenReturn(page);
        var pageDto = service.getAllByActivityId(1L, 2, 3);
        assertThat(pageDto.getTotalElements())
                .isEqualTo(1L);
    }

    private CustomsRecordalRenewalDto getSavedCustomsRecordalRenewalDto() {
        return CustomsRecordalRenewalDto.builder()
                .id(1L)
                .activityId(1L)
                .renewalInstructionDate(LocalDate.of(2023, 1, 1))
                .renewalDate(LocalDate.of(2023, 1, 1))
                .nextRenewalDate(LocalDate.of(2023, 1, 1))
                .build();
    }

    private CustomsRecordalRenewalDto getUpdatedCustomsRecordalRenewalDto() {
        return CustomsRecordalRenewalDto.builder()
                .id(1L)
                .activityId(1L)
                .renewalInstructionDate(LocalDate.of(2023, 1, 2))
                .renewalDate(LocalDate.of(2023, 1, 2))
                .nextRenewalDate(LocalDate.of(2023, 1, 2))
                .build();
    }

    private CustomsRecordalRenewalDto getCustomsRecordalRenewalDto() {
        return CustomsRecordalRenewalDto.builder()
                .activityId(1L)
                .renewalInstructionDate(LocalDate.of(2023, 1, 1))
                .renewalDate(LocalDate.of(2023, 1, 1))
                .nextRenewalDate(LocalDate.of(2023, 1, 1))
                .build();
    }

    private CustomsRecordalRenewal getCustomsRecordalRenewal() {
        return CustomsRecordalRenewal.builder()
                .id(1L)
                .activityId(1L)
                .renewalInstructionDate(LocalDate.of(2023, 1, 1))
                .renewalDate(LocalDate.of(2023, 1, 1))
                .nextRenewalDate(LocalDate.of(2023, 1, 1))
                .build();
    }
}