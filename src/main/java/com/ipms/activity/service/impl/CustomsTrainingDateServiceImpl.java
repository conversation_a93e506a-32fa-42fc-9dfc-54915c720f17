package com.ipms.activity.service.impl;

import com.ipms.activity.dto.ActivityId;
import com.ipms.activity.dto.CustomsTrainingDateDto;
import com.ipms.activity.dto.CustomsTrainingDateFilterRequest;
import com.ipms.activity.dto.CustomsTrainingDatePageDto;
import com.ipms.activity.exception.CustomsTrainingDateNotFoundException;
import com.ipms.activity.mapper.CustomsTrainingDateMapper;
import com.ipms.activity.model.CustomsTrainingDate;
import com.ipms.activity.repository.CustomsTrainingDatesRepository;
import com.ipms.activity.service.CustomsTrainingDateService;
import com.ipms.activity.specification.CustomsTrainingDateSpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class CustomsTrainingDateServiceImpl implements CustomsTrainingDateService {

    private final CustomsTrainingDatesRepository repository;
    private final CustomsTrainingDateMapper mapper;

    @Override
    public CustomsTrainingDateDto save(CustomsTrainingDateDto dto) {
        var saved = repository.save(mapper.toCustomsTrainingDate(dto));
        return mapper.toCustomsTrainingDatesDto(saved);
    }

    @Override
    public CustomsTrainingDateDto update(CustomsTrainingDateDto dto, Long id) {
        var customsTrainingDate = getCustomsTrainingDatesById(id).toBuilder()
                .build();
        var updated = mapper.toCustomsTrainingDatesFromDto(dto, customsTrainingDate);
        var saved = repository.save(updated);
        return mapper.toCustomsTrainingDatesDto(saved);
    }

    @Override
    public void delete(Long id) {
        var customsTrainingDate = getCustomsTrainingDatesById(id)
                .toBuilder()
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(customsTrainingDate);
    }

    @Override
    public CustomsTrainingDateDto getById(Long id) {
        var customsTrainingDate = getCustomsTrainingDatesById(id);
        return mapper.toCustomsTrainingDatesDto(customsTrainingDate);
    }

    @Override
    public CustomsTrainingDatePageDto getAll(CustomsTrainingDateFilterRequest filterRequest, int page, int size) {
        var customsDatePage = repository.findAll(mapSpecification(filterRequest), PageRequest.of(page, size));
        return CustomsTrainingDatePageDto.builder()
                .customsTrainingDateDtos(customsDatePage.getContent()
                        .stream()
                        .map(mapper::toCustomsTrainingDatesDto)
                        .toList())
                .totalPages(customsDatePage.getTotalPages())
                .totalElements(customsDatePage.getTotalElements())
                .build();
    }

    private CustomsTrainingDateSpecification mapSpecification(CustomsTrainingDateFilterRequest filterRequest) {
        return CustomsTrainingDateSpecification.builder()
                .activityId(filterRequest.getActivityId())
                .regionalCustoms(filterRequest.getRegionalCustoms())
                .cities(filterRequest.getCities())
                .build();
    }

    private CustomsTrainingDate getCustomsTrainingDatesById(Long id) {
        return repository.findById(id)
                .orElseThrow(CustomsTrainingDateNotFoundException::new);
    }

    @Override
    public List<ActivityId> getByCustoms(Long customsId) {
        return repository.findByRegionalCustoms(customsId).stream().toList();
    }
}
