eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:prod-ipms-user}.prod-ipms-user
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

spring:
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ${keycloak_ipms_backend_client_secret}
        provider:
          keycloak:
            token-uri: http://keycloak.keycloak/auth/realms/ipms-production/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web
  keycloak-web-client-secret: ${keycloak_web_client_secret}
  keycloak-master-client-secret: ${keycloak_master_client_secret}
  keycloak-master-realm: master
  keycloak-master-clientId: ${keycloak_master_client_id}
  keycloak-master-clientNo: api-production

keycloak:
  realm: ipms-production
  auth-server-url: http://keycloak.keycloak/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ${keycloak_ipms_backend_client_secret}