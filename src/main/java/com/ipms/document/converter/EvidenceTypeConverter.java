package com.ipms.document.converter;

import com.ipms.document.enums.EvidenceType;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class EvidenceTypeConverter implements AttributeConverter<EvidenceType, String> {

    @Override
    public String convertToDatabaseColumn(EvidenceType evidenceType) {
        return evidenceType == null ? null : evidenceType.getValue();
    }

    @Override
    public EvidenceType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : EvidenceType.getEnum(s);
    }
}
