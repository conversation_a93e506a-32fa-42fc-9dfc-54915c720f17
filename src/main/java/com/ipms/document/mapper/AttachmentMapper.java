package com.ipms.document.mapper;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.document.dto.AttachmentDto;
import com.ipms.document.model.Attachment;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AttachmentMapper {
    Attachment toAttachment(AttachmentDto attachmentDto);
    AttachmentDto toAttachmentDto(Attachment attachment);
    StorageFile toStorageFile(AttachmentDto attachmentDto);
}
