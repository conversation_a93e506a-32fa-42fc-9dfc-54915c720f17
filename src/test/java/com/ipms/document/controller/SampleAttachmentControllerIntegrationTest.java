package com.ipms.document.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.document.dto.SampleAttachmentDto;
import com.ipms.document.service.SampleAttachmentService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-document/application.yml")
public class SampleAttachmentControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private SampleAttachmentService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var attachmentDto = SampleAttachmentDto.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .sampleId(1L)
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();

        Mockito.when(service.save(Mockito.any(SampleAttachmentDto.class)))
                .thenReturn(attachmentDto);

        mvc.perform(post("/sample-attachment/")
                        .content(objectMapper.writeValueAsString(attachmentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());

        mvc.perform(delete("/sample-attachment/1/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
