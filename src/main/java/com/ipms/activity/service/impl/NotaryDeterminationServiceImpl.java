package com.ipms.activity.service.impl;

import com.ipms.activity.dto.NotaryDeterminationDto;
import com.ipms.activity.mapper.NotaryDeterminationMapper;
import com.ipms.activity.model.NotaryDetermination;
import com.ipms.activity.repository.NotaryDeterminationRepository;
import com.ipms.activity.service.NotaryDeterminationService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class NotaryDeterminationServiceImpl implements NotaryDeterminationService {

    private final NotaryDeterminationRepository repository;
    private final NotaryDeterminationMapper mapper;

    public NotaryDeterminationServiceImpl(NotaryDeterminationRepository repository, NotaryDeterminationMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @Override
    public List<NotaryDeterminationDto> getByActivityId(Long activityId) {
        List<NotaryDeterminationDto> dtos = new ArrayList<>();
        List<NotaryDetermination> notaryDeterminations = repository.getAllByActivity_Id(activityId);
        if (Objects.nonNull(notaryDeterminations) && !notaryDeterminations.isEmpty()){
            notaryDeterminations.forEach(notaryDetermination ->
                    dtos.add(mapper.toNotaryDeterminationDto(notaryDetermination)));
        }
        return dtos;
    }

    @Override
    public NotaryDeterminationDto getById(Long id) {
        var notaryDetermination = repository.findById(id);
        return notaryDetermination.map(mapper::toNotaryDeterminationDto).orElse(null);
    }
}
