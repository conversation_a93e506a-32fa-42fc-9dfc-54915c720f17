package com.ipms.activity.service;

import com.ipms.activity.dto.ActivityDto;
import com.ipms.core.entity.Revision;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

public interface ActivityBillingProcessService {

    void startBillingProcessByBillingDay(LocalDate processDate);

    void startBillingProcessByBillingAmount();

    void startBillingProcessByStatus(LocalDate processDate);

    void startManuelBillingProcess(Long activityId);

    void approveBilling(Long issueId);

    List<ActivityDto> getActivitiesForBillingCart(String assignee);

    void setOrderCreatedStatus(Long activityId);

    void undoBillingApproval(Long issueId);

    void processOrderIntegrationSuccess(String orderNumber);

    void processOrderIntegrationError(String orderNumber);

    void cancelBillingOrder(Long activityId);

    void processInvoiceUpdated(Set<Long> expenseIds);

    void consumeBillingIssueClosedTopic(Long issueId);

    List<Revision> getBillingRevisions(Long activityId);

    Boolean checkBillingCancellable(Long id);

    boolean isBillingOrderCancellationSafe(String orderNo);

    void cancelBilling(Long activityId, Long issueId, boolean fillEmptyExpenseDates);

    boolean hasBillableRecord(Long id);
}
