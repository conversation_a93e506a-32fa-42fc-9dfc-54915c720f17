package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ipms.core.entity.VersionedEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;

import java.time.LocalDate;

@Getter
@Setter
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = false)
@MappedSuperclass
public abstract class BulletinRelatedActivity extends VersionedEntity {

    @Column
    private String bulletinNumber;

    @Column
    private LocalDate bulletinPublicationDate;

    @Column
    private LocalDate decisionDate;

    @Column
    private LocalDate notificationDate;

    @OneToOne(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinColumn(name = "activity_id", referencedColumnName = "id")
    @JsonBackReference
    @NotAudited
    private Activity activity;
}
