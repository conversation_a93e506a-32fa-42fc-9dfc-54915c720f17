package com.ipms.activity.client;

import com.ipms.activity.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.LocalDateTime;

@FeignClient(name = "ipms-paramcommand")
public interface ParamCommandClient {

    @GetMapping("/api/paramcommand/holiday/is-working-day/{date}")
    BooleanResponse isWorkingDay(@PathVariable(name = "date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date);

    @GetMapping("/api/paramcommand/holiday/holidays")
    HolidayResponse getHolidayList();

    @GetMapping("/api/paramcommand/issue-type/with-date/{key}")
    IssueTypeParameterResponse getIssueTypeWithDates(@PathVariable String key,
                                                     @RequestParam(name = "date")
                                                     @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime date);

    @GetMapping("/api/paramcommand/activity-due-date/by-status/{matterType}/{activity}/{status}")
    ActivityTypeDueDateResponse getLatestByMatterTypeActivityAndStatus(@PathVariable String matterType,
                                                                       @PathVariable String activity,
                                                                       @PathVariable String status);

    @GetMapping("/api/paramcommand/periodic-operation/country/{countryId}")
    PeriodicOperationResponse getPeriodicOperationByCountry(@PathVariable Long countryId);

    @GetMapping("/api/paramcommand/periodic-operation/ip-office/{ipOfficeId}")
    PeriodicOperationResponse getPeriodicOperationByIpOffice(@PathVariable Long ipOfficeId);
}
