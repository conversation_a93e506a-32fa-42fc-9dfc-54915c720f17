package com.ipms.integration.dto;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
public class BillingOrderDetailDto {
    private Long id;
    private Long version;
    @NotEmpty
    private String expenseCode;
    @NotNull
    private Long quantity;
    private BigDecimal unitPrice;
    private BigDecimal discountRate;
}
