package com.ipms.activity.enums;

import java.util.Arrays;

public enum CriminalActionStage {
    INQUEST("1"),
    PROSECUTION("2"),
    DECISION("3"),
    APPEAL_REGIONAL_COURT("4"),
    APPEAL_SUPREME_COURT("5"),
    CONCILIATION("6"),
    FINALIZED("7");

    private final String value;
    CriminalActionStage(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static CriminalActionStage getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(value));
    }
}
