package com.ipms.activity.controller;

import com.ipms.activity.dto.CustomsRecordalRenewalDto;
import com.ipms.activity.dto.CustomsRecordalRenewalPageDto;
import com.ipms.activity.service.CustomsRecordalRenewalService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@RequiredArgsConstructor
@RestController
@RequestMapping("/customs-recordal/renewal")
public class CustomsRecordalRenewalController {
    private final CustomsRecordalRenewalService service;

    @PostMapping
    public BaseResponse<CustomsRecordalRenewalDto> save(@Valid @RequestBody CustomsRecordalRenewalDto renewalDto) {
        var renewal = service.save(renewalDto);
        return BaseResponse.<CustomsRecordalRenewalDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(renewal)
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<CustomsRecordalRenewalDto> update(@Valid @RequestBody CustomsRecordalRenewalDto renewalDto, @PathVariable Long id) {
        var renewal = service.update(renewalDto, id);
        return BaseResponse.<CustomsRecordalRenewalDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(renewal)
                .build();
    }

    @GetMapping("{id}")
    public BaseResponse<CustomsRecordalRenewalDto> getAllByActivityId(@PathVariable Long id) {
        return BaseResponse.<CustomsRecordalRenewalDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

    @GetMapping("{activityId}/{page}/{size}")
    public BaseResponse<CustomsRecordalRenewalPageDto> getAllByActivityId(@PathVariable Long activityId,
                                                                         @PathVariable int page,
                                                                         @PathVariable int size) {
        return BaseResponse.<CustomsRecordalRenewalPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByActivityId(activityId, page, size))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}