package com.ipms.activity.controller.attachments;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.dto.GuaranteeAttachmentDto;
import com.ipms.activity.service.LetterOfGuaranteeAttachmentService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/letter-of-guarantees")
public class LetterOfGuaranteeAttachmentsController {
    private final LetterOfGuaranteeAttachmentService service;

    @PostMapping("/save")
    public BaseResponse<AttachmentDto> save(@RequestBody @Valid GuaranteeAttachmentDto dto) {
        return BaseResponse.<AttachmentDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/signed-url/{id}")
    public BaseResponse<String> signedUrl(@PathVariable Long id) {
        return BaseResponse.<String>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getSignedUrl(id))
                .build();
    }
    @GetMapping("/get-signed-urls/{ids}")
    public BaseResponse<List<String>> getAllByGuaranteeIds(@PathVariable List<Long> ids){
        return BaseResponse.<List<String>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getSignedUrlsByGuaranteeIds(ids))
                .build();
    }

    @GetMapping("delete/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
