package com.ipms.activity.service;

import com.ipms.activity.dto.CounterPartyDto;
import com.ipms.activity.model.CounterParty;

import java.util.List;

public interface CounterPartyService {
    CounterPartyDto save(CounterPartyDto dto);

    List<CounterPartyDto> saveBulk(List<CounterPartyDto> dtos);

    CounterPartyDto update(Long id, CounterPartyDto dto);

    List<CounterPartyDto> getAllByActivityId(Long activityId);

    void delete(Long id, Long version);

    CounterPartyDto getById(Long id);

    CounterParty getCounterPartyById(Long id);
}
