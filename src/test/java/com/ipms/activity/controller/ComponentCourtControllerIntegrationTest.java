package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.activity.dto.ComponentCourtDto;
import com.ipms.activity.service.ComponentCourtService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class ComponentCourtControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private ComponentCourtService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var componentCourtDto = ComponentCourtDto.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(service.save(Mockito.any(ComponentCourtDto.class)))
                .thenReturn(componentCourtDto);
        mvc.perform(post("/component-court")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(componentCourtDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {
        var componentCourtDto = ComponentCourtDto.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(service.update(Mockito.any(ComponentCourtDto.class), Mockito.anyLong()))
                .thenReturn(componentCourtDto);
        mvc.perform(put("/component-court/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(componentCourtDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());
        mvc.perform(delete("/component-court/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByActivityId_thenReturnBaseResponse() throws Exception {
        var componentCourtDto = ComponentCourtDto.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(service.getByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(componentCourtDto));
        mvc.perform(get("/component-court/activity/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByActivities_thenReturnBaseResponse() throws Exception {
        var componentCourtDto = ComponentCourtDto.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(service.getByActivityIds(Mockito.anyList()))
                .thenReturn(List.of(componentCourtDto));
        mvc.perform(get("/component-court/activities/1,2,3")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
