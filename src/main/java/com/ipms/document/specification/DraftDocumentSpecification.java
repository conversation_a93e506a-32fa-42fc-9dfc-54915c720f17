package com.ipms.document.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.document.model.DraftDocument;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class DraftDocumentSpecification extends AbstractSpecification<DraftDocument> {
    private final String assignee;
    private final Boolean isDocumentIdNull;
    private final List<String> assignees;
    @Override
    public Predicate toPredicate(Root<DraftDocument> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(assignee)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("assignee"), value)));

        Optional.ofNullable(assignees)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("assignee").in(assignees))));

        Optional.ofNullable(isDocumentIdNull)
                .ifPresent(value ->  {
                    if (value) {
                        predicate.getExpressions().add(cb.isNull(root.get("documentId")));
                    } else {
                        predicate.getExpressions().add(cb.isNotNull(root.get("documentId")));
                    }
                });
        return predicate;
    }

}
