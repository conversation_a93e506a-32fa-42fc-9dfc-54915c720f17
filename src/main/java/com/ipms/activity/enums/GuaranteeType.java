package com.ipms.activity.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum GuaranteeType {
    CASH("01"),
    LETTER("02");

    private final String value;

    GuaranteeType(String value) {
        this.value = value;
    }

    public static GuaranteeType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
