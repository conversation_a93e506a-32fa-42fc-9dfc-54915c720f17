package com.ipms.activity.converter;

import com.ipms.activity.enums.PartyProvidingGuarantee;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Objects;

@Converter(autoApply = true)
public class PartyProvidingGuaranteeConverter implements AttributeConverter<PartyProvidingGuarantee, String> {
    @Override
    public String convertToDatabaseColumn(PartyProvidingGuarantee partyProvidingGuarantee) {
        return Objects.isNull(partyProvidingGuarantee) ? null : partyProvidingGuarantee.getValue();
    }

    @Override
    public PartyProvidingGuarantee convertToEntityAttribute(String s) {
        return Objects.isNull(s) || s.isEmpty() ? null : PartyProvidingGuarantee.getEnum(s);
    }
}
