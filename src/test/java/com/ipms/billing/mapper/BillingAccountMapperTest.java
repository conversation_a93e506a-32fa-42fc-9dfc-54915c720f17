package com.ipms.billing.mapper;

import com.ipms.billing.dto.BillingAccountDto;
import com.ipms.billing.enums.BillingDay;
import com.ipms.billing.enums.RiskManagement;
import com.ipms.billing.model.BillingAccount;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class BillingAccountMapperTest {
    private final BillingAccountMapper mapper = Mappers.getMapper(BillingAccountMapper.class);

    private BillingAccount billingAccount;
    private BillingAccountDto billingAccountUpdateDto;
    private BillingAccountDto billingAccountDto;

    @BeforeEach
    void setUp() {
        billingAccount = BillingAccount.builder()
                .id(1L)
                .version(0L)
                .createdAt(LocalDateTime.parse("2024-01-01T00:00:00"))
                .createdBy("test")
                .accountName("accountName-1")
                .accountNo("accountNo-1")
                .issuerId(1L)
                .address("address-1")
                .discountRate(BigDecimal.ONE)
                .currency("USD")
                .riskManagement(RiskManagement.NO_RISK)
                .description("description-1")
                .issueIds(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .billingDay(BillingDay.DAY_1)
                .endOfBillingPeriod(BillingDay.END_OF_MONTH)
                .willBeSentToResponsible(true)
                .sendToEmails(new ArrayList<>(Arrays.asList("<EMAIL>", "<EMAIL>")))
                .copyToEmails(new ArrayList<>(Arrays.asList("<EMAIL>", "<EMAIL>")))
                .willOriginalInvoiceBeSent(true)
                .originalInvoiceRecipient("Name Surname or Department")
                .billingSystem("Tymetrix 360")
                .attachmentsToBeSent("Timespents, disbursements")
                .note("Lorem ipsum dolor sit amet")
                .build();

        billingAccountUpdateDto = BillingAccountDto.builder()
                .id(2L)
                .version(1L)
                .createdAt(LocalDateTime.parse("2024-01-02T00:00:00"))
                .createdBy("test-2")
                .accountName("accountName-2")
                .accountNo("accountNo-2")
                .issuerId(2L)
                .address("address-2")
                .discountRate(BigDecimal.TEN)
                .currency("EUR")
                .riskManagement("HIGH_RISK")
                .description("description-2")
                .issueIds(List.of(4L, 5L, 6L))
                .billingDay("DAY_2")
                .endOfBillingPeriod("DAY_15")
                .willBeSentToResponsible(false)
                .sendToEmails(List.of("<EMAIL>"))
                .copyToEmails(List.of("<EMAIL>"))
                .willOriginalInvoiceBeSent(false)
                .originalInvoiceRecipient("Name Surname or Department update")
                .billingSystem("update billing system")
                .attachmentsToBeSent("Timespents, disbursements, expenses")
                .note("Lorem ipsum dolor sit amet update")
                .build();

        billingAccountDto = BillingAccountDto.builder()
                .id(1L)
                .version(0L)
                .accountName("accountName-1")
                .accountNo("accountNo-1")
                .issuerId(1L)
                .address("address-1")
                .discountRate(BigDecimal.ONE)
                .currency("USD")
                .riskManagement("NO_RISK")
                .description("description-1")
                .issueIds(List.of(1L, 2L, 3L))
                .billingDay("DAY_1")
                .endOfBillingPeriod("END_OF_MONTH")
                .willBeSentToResponsible(true)
                .sendToEmails(List.of("<EMAIL>", "<EMAIL>"))
                .copyToEmails(List.of("<EMAIL>", "<EMAIL>"))
                .willOriginalInvoiceBeSent(true)
                .originalInvoiceRecipient("Name Surname or Department")
                .billingSystem("Tymetrix 360")
                .attachmentsToBeSent("Timespents, disbursements")
                .note("Lorem ipsum dolor sit amet")
                .build();
    }

    @Test
    void toBillingAccountDto() {
        var mappedBillingAccountDto = mapper.toBillingAccountDto(billingAccount);

        assertEquals(billingAccount.getId(), mappedBillingAccountDto.getId());
        assertEquals(billingAccount.getVersion(), mappedBillingAccountDto.getVersion());
        assertEquals(billingAccount.getCreatedAt(), mappedBillingAccountDto.getCreatedAt());
        assertEquals(billingAccount.getCreatedBy(), mappedBillingAccountDto.getCreatedBy());
        assertEquals(billingAccount.getAccountName(), mappedBillingAccountDto.getAccountName());
        assertEquals(billingAccount.getAccountNo(), mappedBillingAccountDto.getAccountNo());
        assertEquals(billingAccount.getIssuerId(), mappedBillingAccountDto.getIssuerId());
        assertEquals(billingAccount.getAddress(), mappedBillingAccountDto.getAddress());
        assertEquals(billingAccount.getDiscountRate(), mappedBillingAccountDto.getDiscountRate());
        assertEquals(billingAccount.getCurrency(), mappedBillingAccountDto.getCurrency());
        assertEquals(billingAccount.getRiskManagement().name(), mappedBillingAccountDto.getRiskManagement());
        assertEquals(billingAccount.getDescription(), mappedBillingAccountDto.getDescription());
        assertEquals(billingAccount.getIssueIds(), mappedBillingAccountDto.getIssueIds());
        assertEquals(billingAccount.getBillingDay().name(), mappedBillingAccountDto.getBillingDay());
        assertEquals(billingAccount.getEndOfBillingPeriod().name(), mappedBillingAccountDto.getEndOfBillingPeriod());
        assertEquals(billingAccount.getWillBeSentToResponsible(), mappedBillingAccountDto.getWillBeSentToResponsible());
        assertEquals(billingAccount.getSendToEmails(), mappedBillingAccountDto.getSendToEmails());
        assertEquals(billingAccount.getCopyToEmails(), mappedBillingAccountDto.getCopyToEmails());
        assertEquals(billingAccount.getWillOriginalInvoiceBeSent(), mappedBillingAccountDto.getWillOriginalInvoiceBeSent());
        assertEquals(billingAccount.getOriginalInvoiceRecipient(), mappedBillingAccountDto.getOriginalInvoiceRecipient());
        assertEquals(billingAccount.getBillingSystem(), mappedBillingAccountDto.getBillingSystem());
        assertEquals(billingAccount.getAttachmentsToBeSent(), mappedBillingAccountDto.getAttachmentsToBeSent());
        assertEquals(billingAccount.getNote(), mappedBillingAccountDto.getNote());
    }

    @Test
    void toBillingAccount() {
        var mappedBillingAccount = mapper.toBillingAccount(billingAccountDto);

        assertNull(mappedBillingAccount.getId());
        assertNull(mappedBillingAccount.getVersion());
        assertEquals(billingAccountDto.getAccountName(), mappedBillingAccount.getAccountName());
        assertEquals(billingAccountDto.getAccountNo(), mappedBillingAccount.getAccountNo());
        assertEquals(billingAccountDto.getIssuerId(), mappedBillingAccount.getIssuerId());
        assertEquals(billingAccountDto.getAddress(), mappedBillingAccount.getAddress());
        assertEquals(billingAccountDto.getDiscountRate(), mappedBillingAccount.getDiscountRate());
        assertEquals(billingAccountDto.getCurrency(), mappedBillingAccount.getCurrency());
        assertEquals(billingAccountDto.getRiskManagement(), mappedBillingAccount.getRiskManagement().name());
        assertEquals(billingAccountDto.getDescription(), mappedBillingAccount.getDescription());
        assertEquals(billingAccountDto.getIssueIds(), mappedBillingAccount.getIssueIds());
        assertEquals(billingAccountDto.getBillingDay(), mappedBillingAccount.getBillingDay().name());
        assertEquals(billingAccountDto.getEndOfBillingPeriod(), mappedBillingAccount.getEndOfBillingPeriod().name());
        assertEquals(billingAccountDto.getWillBeSentToResponsible(), mappedBillingAccount.getWillBeSentToResponsible());
        assertEquals(billingAccountDto.getSendToEmails(), mappedBillingAccount.getSendToEmails());
        assertEquals(billingAccountDto.getCopyToEmails(), mappedBillingAccount.getCopyToEmails());
        assertEquals(billingAccountDto.getWillOriginalInvoiceBeSent(), mappedBillingAccount.getWillOriginalInvoiceBeSent());
        assertEquals(billingAccountDto.getOriginalInvoiceRecipient(), mappedBillingAccount.getOriginalInvoiceRecipient());
        assertEquals(billingAccountDto.getBillingSystem(), mappedBillingAccount.getBillingSystem());
        assertEquals(billingAccountDto.getAttachmentsToBeSent(), mappedBillingAccount.getAttachmentsToBeSent());
        assertEquals(billingAccountDto.getNote(), mappedBillingAccount.getNote());
    }

    @Test
    void toBillingAccountFromDto() {
        var mappedBillingAccount = mapper.toBillingAccountFromDto(billingAccountUpdateDto, billingAccount);

        assertNotEquals(billingAccountUpdateDto.getId(), mappedBillingAccount.getId());
        assertNotEquals(billingAccountUpdateDto.getCreatedAt(), mappedBillingAccount.getCreatedAt());
        assertNotEquals(billingAccountUpdateDto.getCreatedBy(), mappedBillingAccount.getCreatedBy());
        assertEquals(billingAccountUpdateDto.getVersion(), mappedBillingAccount.getVersion());
        assertEquals(billingAccountUpdateDto.getAccountName(), mappedBillingAccount.getAccountName());
        assertEquals(billingAccountUpdateDto.getAccountNo(), mappedBillingAccount.getAccountNo());
        assertEquals(billingAccountUpdateDto.getIssuerId(), mappedBillingAccount.getIssuerId());
        assertEquals(billingAccountUpdateDto.getAddress(), mappedBillingAccount.getAddress());
        assertEquals(billingAccountUpdateDto.getDiscountRate(), mappedBillingAccount.getDiscountRate());
        assertEquals(billingAccountUpdateDto.getCurrency(), mappedBillingAccount.getCurrency());
        assertEquals(billingAccountUpdateDto.getRiskManagement(), mappedBillingAccount.getRiskManagement().name());
        assertEquals(billingAccountUpdateDto.getDescription(), mappedBillingAccount.getDescription());
        assertEquals(billingAccountUpdateDto.getIssueIds(), mappedBillingAccount.getIssueIds());
        assertEquals(billingAccountUpdateDto.getBillingDay(), mappedBillingAccount.getBillingDay().name());
        assertEquals(billingAccountUpdateDto.getEndOfBillingPeriod(), mappedBillingAccount.getEndOfBillingPeriod().name());
        assertEquals(billingAccountUpdateDto.getWillBeSentToResponsible(), mappedBillingAccount.getWillBeSentToResponsible());
        assertEquals(billingAccountUpdateDto.getSendToEmails(), mappedBillingAccount.getSendToEmails());
        assertEquals(billingAccountUpdateDto.getCopyToEmails(), mappedBillingAccount.getCopyToEmails());
        assertEquals(billingAccountUpdateDto.getWillOriginalInvoiceBeSent(), mappedBillingAccount.getWillOriginalInvoiceBeSent());
        assertEquals(billingAccountUpdateDto.getOriginalInvoiceRecipient(), mappedBillingAccount.getOriginalInvoiceRecipient());
        assertEquals(billingAccountUpdateDto.getBillingSystem(), mappedBillingAccount.getBillingSystem());
        assertEquals(billingAccountUpdateDto.getAttachmentsToBeSent(), mappedBillingAccount.getAttachmentsToBeSent());
        assertEquals(billingAccountUpdateDto.getNote(), mappedBillingAccount.getNote());
    }
}