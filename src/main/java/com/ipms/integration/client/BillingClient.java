package com.ipms.integration.client;

import com.ipms.integration.dto.BillingOrderResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name = "ipms-billing", path = "/api/billing")
public interface BillingClient {

    @GetMapping("/billing-orders/ids/{ids}")
    BillingOrderResponse getBillingOrdersByIds(@PathVariable List<Long> ids);
}
