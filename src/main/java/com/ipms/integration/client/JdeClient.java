package com.ipms.integration.client;

import com.ipms.integration.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "jde-client", url = "${jde.url}")
public interface JdeClient {

    @GetMapping("/ExchangeRate")
    ExchangeRateResponse fetchExchangeRates(@SpringQueryMap ExchangeRateRequestParam exchangeRateRequestParam);

    @GetMapping("/CustomerInformation")
    CustomerInformationResponse getCustomerInformation(@SpringQueryMap CustomerInformationRequestParam requestParam);

    @PostMapping("/Orders")
    JdeOrderResponse sendOrders(@RequestBody JdeOrderRequest orderRequest);

    @GetMapping("/Orders")
    JdeOrderGetResponse fetchOrders(@RequestParam String mainCompany, @RequestParam String orderNumber);

    @GetMapping("/OrderValidate")
    OrderValidateResponse validateOrder(@RequestParam String mainCompany,
                                        @RequestParam String erpOrderNumber,
                                        @RequestParam String customerOrSupplierNumber);

    @GetMapping("/Invoices")
    JdeInvoiceResponse fetchInvoices(@SpringQueryMap InvoiceRequestParam invoiceRequestParam);
}
