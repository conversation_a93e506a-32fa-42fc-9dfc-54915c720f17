eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/registry/eureka/
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${instanceId:${random.value}}
    preferIpAddress: true

spring:
  datasource:
    driverClassName: org.postgresql.Driver
    url: *****************************************
    username: postgres
    password: postgres
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth/realms/ipms-default/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-default
  auth-server-url: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn

azure:
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=ipmstestsa;AccountKey=****************************************************************************************;
    container-name: ipmstestcontainer
    sas-host-url: https://ipmstestsa.blob.core.windows.net/ipmstestcontainer/

kafka:
  server: localhost:29092
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  issue-linked-topic: issueLinkedTopic
  document-updated-topic: documentUpdatedTopic
  firm-associated-topic: firmAssociatedTopic
  billing-order-integration-success-topic: billingOrderIntegrationSuccessTopic
  billing-order-integration-error-topic: billingOrderIntegrationErrorTopic
  invoice-updated-topic: invoiceUpdatedTopic
  billing-approved-topic: billingApprovedTopic
  invoice-is-ready-topic: invoiceIsReadyTopic
  risk-impact-update-topic: riskImpactUpdateTopic
  billing-issue-closed-topic: billingIssueClosedTopic
  close-issue-topic: closeIssueTopic
  timesheet-changed-topic: timesheetChangedTopic
  matter-update-topic: matterUpdateTopic
  matter-application-date-changed-topic: matterApplicationDateChangedTopic

deris:
  url: https://online.deris.com.tr