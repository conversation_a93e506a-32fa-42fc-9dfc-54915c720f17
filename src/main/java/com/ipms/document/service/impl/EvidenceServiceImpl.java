package com.ipms.document.service.impl;

import com.ipms.config.storage.service.StorageService;
import com.ipms.document.client.ActivityClient;
import com.ipms.document.dto.*;
import com.ipms.document.exception.EvidenceCannotRemoveException;
import com.ipms.document.exception.EvidenceNotFoundException;
import com.ipms.document.mapper.EvidenceMapper;
import com.ipms.document.model.Evidence;
import com.ipms.document.repository.EvidenceRepository;
import com.ipms.document.service.EvidenceService;
import com.ipms.document.specification.EvidenceSpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class EvidenceServiceImpl implements EvidenceService {

    private final EvidenceRepository repository;
    private final EvidenceMapper mapper;
    private final ActivityClient activityClient;
    private final StorageService storageService;

    @Override
    public EvidenceDto save(EvidenceCreateDto evidenceCreateDto) {
        var evidence = mapper.toEvidence(evidenceCreateDto);
        var attachment = mapper.toEvidenceAttachment(evidenceCreateDto);
        attachment.setEvidence(evidence);
        evidence.setAttachments(List.of(attachment));
        var saved = repository.save(evidence);
        activityClient.addEvidence(evidenceCreateDto.getActivityId(), saved.getId());
        return toEvidenceDto(saved);
    }

    @Override
    public EvidenceDto useEvidence(EvidenceCreateDto evidenceCreateDto) {
        var evidence = mapper.toEvidence(evidenceCreateDto);
        var attachments = evidenceCreateDto.getAttachments()
                .stream()
                .map(mapper::toEvidenceAttachment)
                .toList();
        attachments.forEach(attachment->attachment.setEvidence(evidence));
        evidence.setAttachments(attachments);
        var saved = repository.save(evidence);
        activityClient.addEvidence(evidenceCreateDto.getActivityId(), saved.getId());
        return toEvidenceDto(saved);
    }

    @Override
    public EvidenceDto save(EvidenceDto dto) {
        var evidence = mapper.toEvidence(dto);
        var saved = repository.save(evidence);
        activityClient.addEvidence(dto.getActivityId(), saved.getId());
        var evidenceDto = mapper.toDto(saved);
        evidenceDto.setActivityId(dto.getActivityId());
        return evidenceDto;
    }

    @Override
    public EvidenceDto update(EvidenceDto evidenceDto, Long id) {
        var evidence = mapper.toEvidenceFromDto(evidenceDto, getEvidenceById(id).toBuilder().build());
        var saved = repository.save(evidence);
        return toEvidenceDto(saved);
    }

    @Override
    public Evidence getEvidenceById(Long id) {
        return repository.findById(id).orElseThrow(EvidenceNotFoundException::new);
    }

    @Override
    public List<EvidenceDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids)
                .stream()
                .map(this::toEvidenceDto)
                .toList();
    }

    @Override
    public EvidenceDto getById(Long id) {
        var evidence = getEvidenceById(id);
        return toEvidenceDto(evidence);
    }

    @Override
    public void delete(Long id, Long version) {
        activityClient.checkEvidenceLinked(id)
                .linked()
                .ifPresent(payload -> {throw new EvidenceCannotRemoveException();});
        var evidence = getEvidenceById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .version(version)
                .build();
        repository.save(evidence);
    }

    @Override
    public EvidenceDto getByFileUniqueName(String fileUniqueName) {
        var evidence = repository.findFirstByAttachmentsFileUniqueName(fileUniqueName)
                .orElseThrow(EvidenceNotFoundException::new);
        return toEvidenceDto(evidence);
    }

    @Override
    public List<EvidenceDto> getByFileUniqueName(List<String> fileUniqueNames) {
        return repository.findByAttachmentsFileUniqueNameIn(fileUniqueNames)
                .stream()
                .map(this::toEvidenceDto)
                .toList();
    }

    @Override
    public EvidencePageDto getAll(EvidenceFilterRequest request, int page, int size) {
        Page<Evidence> evidencePage = repository.findAll(EvidenceSpecification.builder()
                        .ids(request.getIds())
                        .firmId(request.getFirmId())
                        .evidenceTypes(request.getEvidenceTypes())
                        .mediaTypes(request.getMediaTypes())
                        .build(), PageRequest.of(page, size));

        return EvidencePageDto.builder()
                .evidences(evidencePage.getContent()
                        .stream()
                        .map(this::toEvidenceDto)
                        .toList())
                .totalElements(evidencePage.getTotalElements())
                .totalPages(evidencePage.getTotalPages())
                .build();
    }

    private EvidenceDto toEvidenceDto(Evidence evidence) {
        var evidenceDto = mapper.toDto(evidence);
        evidenceDto.getAttachments().forEach(attachmentDto -> {
            var url = storageService.generateSas(mapper.toStorageFile(attachmentDto));
            attachmentDto.setFileSignedUrl(url);
        });
        evidenceDto.setHasMailAttachment(hasMailAttachment(evidenceDto.getAttachments()));
        return evidenceDto;
    }

    private boolean hasMailAttachment(List<EvidenceAttachmentDto> attachments) {
        return attachments
                .stream()
                .anyMatch(attachmentDto -> attachmentDto.getFileUniqueName().startsWith("a"));
    }
}
