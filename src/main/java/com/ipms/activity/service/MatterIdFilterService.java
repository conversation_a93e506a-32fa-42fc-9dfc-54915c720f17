package com.ipms.activity.service;

import com.ipms.activity.dto.ActivityFilterRequest;
import com.ipms.activity.dto.MatterIdAndActivityStatus;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.OverTimeChoice;
import com.ipms.activity.model.Activity;
import com.ipms.activity.repository.ActivityRepository;
import com.ipms.activity.specification.ActivitySpecification;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MatterIdFilterService {
    private final ActivityRepository repository;

    public List<Long> filterMatterIdsByTypesAndStatuses(ActivityFilterRequest filterRequest) {
        var requestedMatterIds = filterRequest.getMatterIds();
        var requestedTypes = filterRequest.getTypes();
        var requestedStatuses = filterRequest.getStatuses();

        var matterActivities = repository.findAllByMatterIdIn(requestedMatterIds).stream()
                .filter(activity -> requestedTypes.isEmpty() || requestedTypes.contains(activity.type().name()))
                .filter(activity -> requestedStatuses.isEmpty() || requestedStatuses.contains(activity.status().name()))
                .toList();

        var activityIds = matterActivities.stream()
                .map(MatterIdAndActivityStatus::id)
                .distinct()
                .toList();

        if (filterRequest.getOverTimeChoice() != null) {
            var matterIdsFilteredByOverTime = filterByOverTimeCondition(filterRequest.getOverTimeChoice(), activityIds);
            matterActivities = matterActivities.stream()
                    .filter(matterIdAndActivityStatus -> matterIdsFilteredByOverTime.contains(matterIdAndActivityStatus.matterId()))
                    .toList();
        }

        var inactiveMatters = getInactiveMatters(matterActivities);

        return matterActivities.stream()
                .map(MatterIdAndActivityStatus::matterId)
                .filter(matterId -> !inactiveMatters.contains(matterId))
                .distinct()
                .toList();
    }

    @NotNull
    private List<Long> getInactiveMatters(List<MatterIdAndActivityStatus> matterActivities) {
        var matterToStatusesMap = matterActivities.stream()
                .collect(Collectors.groupingBy(
                        MatterIdAndActivityStatus::matterId,
                        Collectors.mapping(MatterIdAndActivityStatus::status, Collectors.toList())));

        List<Long> inactiveMatters = new ArrayList<>();
        BiConsumer<Long, List<ActivityStatus>> setInactiveMatters = (matterId, statusList) -> {
            if (ActivityStatus.hasNoActive(statusList)) {
                inactiveMatters.add(matterId);
            }
        };
        matterToStatusesMap.forEach(setInactiveMatters);
        return inactiveMatters;
    }

    @NotNull
    private Set<Long> filterByOverTimeCondition(OverTimeChoice overTimeChoice, List<Long> activityIds) {
        var activitySpecification = ActivitySpecification.builder()
                .ids(activityIds)
                .overTimeChoice(overTimeChoice)
                .build();
        return repository.findAll(activitySpecification).stream()
                .map(Activity::getMatterId)
                .collect(Collectors.toSet());
    }
}
