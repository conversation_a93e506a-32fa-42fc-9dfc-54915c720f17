package com.ipms.billing.controller;

import com.ipms.billing.enums.PaymentStatus;
import com.ipms.billing.service.PaymentService;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.billing.dto.*;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.math.BigDecimal;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/application.yml")
public class PaymentControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private PaymentService service;

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void whenGetAll_thenReturnBaseResponse() throws Exception {
        var paymentPageDto = PaymentPageDto.builder()
                .totalPages(2)
                .totalElements(10L)
                .payments(List.of(PaymentDto.builder().id(1L).build()))
                .build();

        Mockito.when(service.getAll(
                Mockito.any(PaymentFilterRequest.class),
                Mockito.anyInt(),
                Mockito.anyInt()
        )).thenReturn(paymentPageDto);

        mvc.perform(get("/payment/0/10?sortField=createdAt&sortDirection=desc")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetPaymentGroupedList_thenReturnBaseResponse() throws Exception {
        var paymentDto1 = PaymentDto.builder()
                .id(1L)
                .accrualNumber("ACC-123")
                .build();

        var paymentDto2 = PaymentDto.builder()
                .id(2L)
                .accrualNumber("ACC-123")
                .build();

        var paymentGroup = PaymentGroupDto.builder()
                .accrualNumber("ACC-123")
                .totalExpense(BigDecimal.valueOf(300.00))
                .payments(List.of(paymentDto1, paymentDto2))
                .build();

        var paymentGroupPageDto = PaymentGroupPageDto.builder()
                .totalPages(1)
                .totalElements(1L)
                .paymentGroups(List.of(paymentGroup))
                .build();

        Mockito.when(service.getAllGrouped(
                Mockito.any(PaymentFilterRequest.class),
                Mockito.anyInt(),
                Mockito.anyInt()
        )).thenReturn(paymentGroupPageDto);

        mvc.perform(get("/payment/grouped/0/10?sortField=createdAt&sortDirection=desc")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByName_thenReturnBaseResponse() throws Exception {
        var paymentDto = PaymentDto.builder()
                        .id(1L)
                        .accrualNumber("ACC123")
                        .status(PaymentStatus.PENDING_PAYMENT)
                        .build();

        Mockito.when(service.getByAccrualNumber(Mockito.anyString()))
                .thenReturn(paymentDto);

        mvc.perform(get("/payment/ACC123")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var paymentDto = PaymentDto.builder()
                .id(1L)
                .accrualNumber("ACC123")
                .status(PaymentStatus.PENDING_PAYMENT)
                .build();

        Mockito.when(service.save(Mockito.any(PaymentDto.class)))
                .thenReturn(paymentDto);

        mvc.perform(post("/payment")
                        .content(objectMapper.writeValueAsString(paymentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {
        var paymentDto = PaymentDto.builder()
                .id(1L)
                .accrualNumber("ACC123")
                .status(PaymentStatus.PENDING_PAYMENT)
                .build();

        Mockito.when(service.update(Mockito.any(PaymentDto.class), Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(paymentDto);

        mvc.perform(put("/payment/1/1")
                        .content(objectMapper.writeValueAsString(paymentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDeleteUnapprovedPayments_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).deleteUnapprovedPayments();

        mvc.perform(delete("/payment/auto-delete-unapproved")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        Mockito.verify(service, Mockito.times(1)).deleteUnapprovedPayments();
    }

    @Test
    public void whenBulkUpdatePaymentStatus_thenReturnSuccessResponse() throws Exception {
        var request = BulkPaymentStatusUpdateRequest.builder()
                .paymentIds(List.of(1L, 2L, 3L))
                .newStatus(PaymentStatus.PAID)
                .build();

        Mockito.doNothing().when(service).bulkUpdateStatus(
                request.getPaymentIds(),
                request.getNewStatus()
        );

        mvc.perform(put("/payment/bulk-update-status")
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        Mockito.verify(service, Mockito.times(1))
                .bulkUpdateStatus(request.getPaymentIds(), request.getNewStatus());
    }
}
