package com.ipms.document.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.document.dto.*;
import com.ipms.document.service.SampleService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/sample")
@Tag(name = "sample", description = "This endpoint contains sample APIs")
public class SampleController {

    private final SampleService service;

    @PostMapping
    public BaseResponse<SampleDto> save(@Valid @RequestBody SampleManualDto sampleManualDto) {
        var dto = service.save(sampleManualDto);
        return BaseResponse.<SampleDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<SampleDto> update(@PathVariable Long id, @Valid @RequestBody SampleDto sampleDto) {
        var dto = service.update(sampleDto, id);
        return BaseResponse.<SampleDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<SampleDto> get(@PathVariable Long id) {
        var dto = service.getById(id);
        return BaseResponse.<SampleDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<SampleDto>> getByIds(@PathVariable List<Long> ids) {
        return BaseResponse.<List<SampleDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIdIn(ids))
                .build();
    }

    @GetMapping("/firm/{firmId}/{page}/{size}")
    public BaseResponse<SamplePageDto> getByFirm(SampleFilterRequest filterRequest,
                                                 @PathVariable int page,
                                                 @PathVariable int size) {
        return BaseResponse.<SamplePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest, page, size))
                .build();
    }

    @GetMapping("/samples/{page}/{size}")
    public BaseResponse<SamplePageDto> getAll(SampleFilterRequest filterRequest,
                                              @PathVariable int page,
                                              @PathVariable int size) {
        return BaseResponse.<SamplePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest, page, size))
                .build();
    }
}
