package com.ipms.integration.dto;

import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@Data
@Builder
public class ExchangeRateDto {
    private String fromCurrencyCode;
    private String toCurrencyCode;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate currencyDate;
    private String exchangeRate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate dateofUpdate;
}
