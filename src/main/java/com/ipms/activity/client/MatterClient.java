package com.ipms.activity.client;

import com.ipms.activity.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "ipms-matter")
public interface MatterClient {
    @GetMapping("/api/matter/matters?firms={firmId}")
    MattersResponse getByFirmId(@PathVariable Long firmId);

    @GetMapping("/api/matter/matters")
    MattersResponse getMatterList(@SpringQueryMap MatterFilterRequest filterRequest);

    @GetMapping("/api/matter/matter-ids")
    MatterIdsResponse getMatterIdsList(@SpringQueryMap MatterFilterRequest filterRequest);

    @GetMapping("/api/matter/ids/for-timesheet-approval")
    MatterIdsResponse getMatterIdListForSystemApproval();

    @GetMapping("/api/matter/search")
    MattersResponse getList(@RequestParam String key);

    @GetMapping("/api/matter/matter-ids/{responsible}")
    MatterIdsResponse getMatterIdsList(@PathVariable String responsible);

    @GetMapping("/api/matter/{id}")
    MatterResponse getById(@PathVariable Long id);

    @PostMapping("/api/matter/basis-trademarks/import")
    BasisTrademarkResponse saveImportedBasisTrademark(@RequestBody BasisTrademarkDto dto);
}
