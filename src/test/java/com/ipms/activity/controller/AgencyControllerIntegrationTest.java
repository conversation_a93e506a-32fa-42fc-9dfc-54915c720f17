package com.ipms.activity.controller;

import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.service.AgencyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class AgencyControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private AgencyService service;

    @Test
    public void givenName_whenGetByName_thenReturnBaseResponse() throws Exception {
        var agency = AgencyDto.builder().id(1L).name("test-name").build();
        Mockito.when(service.getByName(Mockito.anyString()))
                .thenReturn(List.of(agency));
        mvc.perform(get("/agency/search?name=test")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
