package com.ipms.document.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.document.dto.*;
import com.ipms.document.service.EvidenceService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import java.util.List;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-document/application.yml")
public class EvidenceControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private EvidenceService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(service.save(Mockito.any(EvidenceDto.class)))
                .thenReturn(evidenceDto);

        mvc.perform(post("/evidence")
                        .content(objectMapper.writeValueAsString(evidenceDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenSaveWithAttachment_thenReturnBaseResponse() throws Exception {
        var evidenceCreateDto = EvidenceCreateDto.builder()
                .activityId(1L)
                .contentType("image/png")
                .fileName("image001.png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .evidenceType("FINANCIAL_DATA")
                .mediaType("PDF")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(service.save(Mockito.any(EvidenceCreateDto.class)))
                .thenReturn(evidenceDto);

        mvc.perform(post("/evidence/with-attachment")
                        .content(objectMapper.writeValueAsString(evidenceCreateDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUseEvidence_thenReturnBaseResponse() throws Exception {
        var attachment = EvidenceAttachmentDto.builder()
                .fileName("image001.png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .contentType("image/png")
                .build();
        var evidenceCreateDto = EvidenceCreateDto.builder()
                .activityId(1L)
                .evidenceType("FINANCIAL_DATA")
                .mediaType("PDF")
                .firms(List.of(1L))
                .name("test-name")
                .attachments(List.of(attachment))
                .description("test-description")
                .build();
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(service.save(Mockito.any(EvidenceCreateDto.class)))
                .thenReturn(evidenceDto);

        mvc.perform(post("/evidence/use-evidence")
                        .content(objectMapper.writeValueAsString(evidenceCreateDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .version(0L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(service.update(Mockito.any(EvidenceDto.class), Mockito.anyLong()))
                .thenReturn(evidenceDto);

        mvc.perform(put("/evidence/1")
                        .content(objectMapper.writeValueAsString(evidenceDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGet_thenReturnBaseResponse() throws Exception {
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(service.getById(Mockito.anyLong()))
                .thenReturn(evidenceDto);

        mvc.perform(get("/evidence/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());

        mvc.perform(delete("/evidence/1/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByIds_thenReturnBaseResponse() throws Exception {
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(service.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(evidenceDto));

        mvc.perform(get("/evidence/ids/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByName_thenReturnBaseResponse() throws Exception {
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(service.getByFileUniqueName(Mockito.anyString()))
                .thenReturn(evidenceDto);

        mvc.perform(get("/evidence/file/test-name")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByNames_thenReturnBaseResponse() throws Exception {
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(service.getByFileUniqueName(Mockito.anyList()))
                .thenReturn(List.of(evidenceDto));

        mvc.perform(get("/evidence/files/test-name")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetAll_thenReturnBaseResponse() throws Exception {
        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .activityId(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        var evidencePageDto = EvidencePageDto.builder()
                .evidences(List.of(evidenceDto))
                .totalElements(10)
                .totalPages(2L)
                .build();
        Mockito.when(service.getAll(Mockito.any(EvidenceFilterRequest.class), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(evidencePageDto);

        mvc.perform(get("/evidence/evidences/0/10")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
