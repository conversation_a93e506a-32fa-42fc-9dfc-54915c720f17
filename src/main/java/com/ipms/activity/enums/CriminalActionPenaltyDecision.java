package com.ipms.activity.enums;

import java.util.Arrays;

public enum CriminalActionPenaltyDecision {
    DEFERMANT_OF_THE_ANNOUNCEMENT_OF_THE_VERDICT("1"),
    IMPRISONMENT("2"),
    JUDICIAL_FINE("3"),
    POSTPONEMENT_OF_CONVICTION("4");

    private final String value;

    CriminalActionPenaltyDecision(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static CriminalActionPenaltyDecision getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(value));
    }
}
