package com.ipms.activity.service.impl;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.dto.PIAttachmentDto;
import com.ipms.activity.exception.AttachmentNotFoundException;
import com.ipms.activity.mapper.AttachmentMapper;
import com.ipms.activity.model.PreliminaryInjunction;
import com.ipms.activity.model.RefundedAttachment;
import com.ipms.activity.repository.RefundedAttachmentRepository;
import com.ipms.activity.service.RefundedAttachmentService;
import com.ipms.activity.util.ActivityUtils;
import com.ipms.config.storage.service.StorageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class RefundedAttachmentServiceImpl extends AbstractAttachmentService implements RefundedAttachmentService {
    private final RefundedAttachmentRepository repository;

    public RefundedAttachmentServiceImpl(StorageService storageService, AttachmentMapper mapper, RefundedAttachmentRepository repository) {
        super(storageService, mapper);
        this.repository = repository;
    }

    @Override
    public String getSignedUrl(Long id) {
        var attachmentDto = getById(id);
        return getSignedUrl(attachmentDto);
    }

    public AttachmentDto getById(Long id) {
        return mapper.toAttachmentDto(getAttachmentById(id));
    }

    @Override
    public AttachmentDto save(PIAttachmentDto piAttachmentDto) {
        piAttachmentDto.setFileUniqueName(ActivityUtils.prepareAttachmentName(piAttachmentDto.getFileName()));
        piAttachmentDto.setFileSignedUrl(getSignedUrl(piAttachmentDto));
        var attachment =  mapper.toRefundedAttachment(piAttachmentDto);
        var save = repository.save(attachment);
        return mapper.toAttachmentDto(save, piAttachmentDto.getFileSignedUrl());
    }

    @Override
    public void delete(Long id) {
        var attachment = getAttachmentById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(attachment);
    }

    private RefundedAttachment getAttachmentById(Long id) {
        return repository.findById(id)
                .orElseThrow(AttachmentNotFoundException::new);
    }

    @Override
    public void saveAttachments(List<AttachmentDto> updatedAttachments, PreliminaryInjunction preliminaryInjunction) {
        if (updatedAttachments != null) {
            var existingAttachments = preliminaryInjunction.getRefundedAttachments();
            deleteAttachments(updatedAttachments, existingAttachments);
            updatedAttachments.forEach(attachmentDto -> {
                if (attachmentDto.getId() != null) {
                    updateExistingAttachment(preliminaryInjunction, attachmentDto);
                } else {
                    var attachment = mapper.toRefundedAttachment(attachmentDto);
                    attachment.setPreliminaryInjunction(preliminaryInjunction);
                    existingAttachments.add(attachment);
                }
            });
        }
    }

    public void updateExistingAttachment(PreliminaryInjunction preliminaryInjunction,
                                     AttachmentDto attachmentDto) {
        var existingAttachment = preliminaryInjunction.getRefundedAttachments().stream()
                .filter(attachment -> attachment.getId().equals(attachmentDto.getId()))
                .findFirst()
                .orElse(repository.findById(attachmentDto.getId()).orElseThrow(AttachmentNotFoundException::new));
        existingAttachment.setPreliminaryInjunction(preliminaryInjunction);
        var attachment = mapper.toRefundedAttachmentFromDto(attachmentDto, existingAttachment);
        preliminaryInjunction.getRefundedAttachments().add(existingAttachment);
        repository.save(attachment);
    }

    private void deleteAttachments(List<AttachmentDto> attachmentDto,
                                   List<RefundedAttachment> existingAttachments) {
        var attachmentToBeDeleted = getAttachmentsToBeDeleted(attachmentDto, existingAttachments);
        attachmentToBeDeleted.forEach(attachment -> repository.save(attachment.toBuilder().isDeleted(Boolean.TRUE).build()));
        existingAttachments.removeAll(attachmentToBeDeleted);
    }

    private List<RefundedAttachment> getAttachmentsToBeDeleted(List<AttachmentDto> updatedAttachments, List<RefundedAttachment> existingAttachment) {
        return existingAttachment.stream()
                .filter(attachment -> updatedAttachments.stream()
                        .filter(attachmentDto -> attachmentDto.getId() != null)
                        .noneMatch(attachmentDto -> Objects.equals(attachmentDto.getId(), attachment.getId())))
                .toList();
    }

    @Override
    public void setFileSignedUrls(List<AttachmentDto> attachmentDtos) {
        attachmentDtos.forEach(this::setSignedUrl);
    }
}
