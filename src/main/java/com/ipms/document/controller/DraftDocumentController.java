package com.ipms.document.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.document.dto.DraftDocumentDto;
import com.ipms.document.dto.DraftDocumentFilterRequest;
import com.ipms.document.dto.DraftDocumentPageDto;
import com.ipms.document.service.DraftDocumentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("draft-documents")
public class DraftDocumentController {
    private final DraftDocumentService service;

    @GetMapping("/{page}/{size}")
    public BaseResponse<DraftDocumentPageDto> getAll(@PathVariable int page,
                                                     @PathVariable int size, DraftDocumentFilterRequest request) {
        return BaseResponse.<DraftDocumentPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(request, page, size))
                .build();
    }

    @PutMapping("/assign-to-paralegal/{id}")
    public BaseResponse<DraftDocumentDto> assignToParalegal(@PathVariable Long id, @RequestBody DraftDocumentDto dto) {
        return BaseResponse.<DraftDocumentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.assignToParalegal(id, dto))
                .build();
    }

    @PutMapping("/link-to-document/{id}")
    public BaseResponse<DraftDocumentDto> linkToDocument(@PathVariable Long id, @RequestBody DraftDocumentDto dto) {
        return BaseResponse.<DraftDocumentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.linkToDocument(id, dto))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version){
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
