package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.ExecutionOffice;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExecutionOfficeRepository extends PagingAndSortingRepository<ExecutionOffice, Long>, JpaSpecificationExecutor<ExecutionOffice> {
    List<ExecutionOffice> findByCity(String city);
}
