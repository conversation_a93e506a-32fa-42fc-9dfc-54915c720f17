package com.ipms.document.service;

import com.ipms.config.storage.service.StorageService;
import com.ipms.document.dto.EvidenceAttachmentDto;
import com.ipms.document.enums.EvidenceType;
import com.ipms.document.enums.MediaType;
import com.ipms.document.mapper.EvidenceAttachmentMapper;
import com.ipms.document.mapper.EvidenceAttachmentMapperImpl;
import com.ipms.document.model.Evidence;
import com.ipms.document.model.EvidenceAttachment;
import com.ipms.document.repository.EvidenceAttachmentRepository;
import com.ipms.document.service.impl.EvidenceAttachmentServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;

@RunWith(SpringRunner.class)
public class EvidenceAttachmentServiceTest {

    @MockBean
    private EvidenceAttachmentService service;

    @Mock
    private EvidenceAttachmentRepository repository;

    @Mock
    private StorageService storageService;

    @Mock
    private EvidenceService evidenceService;

    private final EvidenceAttachmentMapper mapper = new EvidenceAttachmentMapperImpl();

    @Before
    public void setUp() {
        service = new EvidenceAttachmentServiceImpl(repository, storageService, evidenceService, mapper);
    }

    @Test
    public void whenSave_thenReturnEvidenceAttachmentDto() {
        var attachment = EvidenceAttachment.builder()
                .id(1L)
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var attachmentDto = EvidenceAttachmentDto.builder()
                .id(1L)
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();

        Mockito.when(evidenceService.getEvidenceById(Mockito.anyLong()))
                .thenReturn(evidence);
        Mockito.when(repository.save(Mockito.any(EvidenceAttachment.class)))
                .thenReturn(attachment);

        assertThat(service.save(attachmentDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenDelete_thenThrowDoesNotThrowAnyException() {
        var attachment = EvidenceAttachment.builder()
                .id(1L)
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(attachment));

        assertThatCode(() -> service.delete(1L, 0L))
                .doesNotThrowAnyException();
    }
}
