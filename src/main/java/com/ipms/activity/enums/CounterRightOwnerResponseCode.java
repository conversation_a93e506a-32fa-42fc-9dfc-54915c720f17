package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum CounterRightOwnerResponseCode implements Code {

    COUNTER_RIGHT_OWNER_NOT_FOUND(1000, "error.code.counter_right_owner.not_found"),
    COUNTER_RIGHT_OWNER_ALREADY_EXIST(1001, "error.code.counter_right_owner.already_exist");

    private final Integer code;
    private final String messageKey;

    CounterRightOwnerResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
