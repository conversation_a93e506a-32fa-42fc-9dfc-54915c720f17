package com.ipms.activity.model;


import com.ipms.activity.enums.CounterRightOwnerCardType;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.List;


@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type")
@DiscriminatorValue(CounterRightOwnerCardType.Values.COUNTER_RIGHT_OWNER_CARD)
@Table(name = "activity_counter_right_owner")
@SQLDelete(sql = "UPDATE activity_counter_right_owner SET is_deleted=1 WHERE id = ?")
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class CounterRightOwnerCard extends VersionedEntity {

    @Column
    @NotNull
    private Long activityId;

    @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @NotNull
    private CounterRightOwner counterRightOwner;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "counterRightOwnerCard")
    @NotAudited
    private List<Lawyer> lawyerList;

    @Column(insertable = false, updatable = false)
    @Enumerated(value = EnumType.STRING)
    private CounterRightOwnerCardType type;
}
