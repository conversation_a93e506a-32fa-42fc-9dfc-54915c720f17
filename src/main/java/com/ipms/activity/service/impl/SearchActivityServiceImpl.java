package com.ipms.activity.service.impl;

import com.ipms.activity.dto.SearchActivityFilterRequest;
import com.ipms.activity.model.SearchActivity;
import com.ipms.activity.repository.SearchActivityRepository;
import com.ipms.activity.specification.SearchActivitySpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class SearchActivityServiceImpl {
    private final SearchActivityRepository repository;

    public List<SearchActivity> getAll(SearchActivityFilterRequest filterRequest) {
        return repository.findAll(SearchActivitySpecification.builder()
                .ids(filterRequest.getIds())
                .types(filterRequest.getTypes())
                .statuses(filterRequest.getStatuses())
                .matterIds(filterRequest.getMatterIds())
                .firmIds(filterRequest.getFirmIds())
                .agentReference(filterRequest.getAgentReference())
                .instructionDates(filterRequest.getInstructionDates())
                .cdLetterFilter(filterRequest.getCdLetterFilter())
                .courtActionFilter(filterRequest.getCourtActionFilter())
                .criminalActionFilter(filterRequest.getCriminalActionFilter())
                .customsDecisionNumber(filterRequest.getCustomsDecisionNumbers())
                .useInvestigationCompletionDate(filterRequest.getUseInvestigationCompletionDate())
                .build());
    }

    public Page<SearchActivity> getAll(SearchActivityFilterRequest filterRequest, int page, int size) {
        return repository.findAll(SearchActivitySpecification.builder()
                .ids(filterRequest.getIds())
                .types(filterRequest.getTypes())
                .statuses(filterRequest.getStatuses())
                .matterIds(filterRequest.getMatterIds())
                .firmIds(filterRequest.getFirmIds())
                .agentReference(filterRequest.getAgentReference())
                .instructionDates(filterRequest.getInstructionDates())
                .cdLetterFilter(filterRequest.getCdLetterFilter())
                .courtActionFilter(filterRequest.getCourtActionFilter())
                .criminalActionFilter(filterRequest.getCriminalActionFilter())
                .customsDecisionNumber(filterRequest.getCustomsDecisionNumbers())
                .useInvestigationCompletionDate(filterRequest.getUseInvestigationCompletionDate())
                .sortField(filterRequest.getSortField())
                .sortDirection(filterRequest.getSortDirection())
                .build(), PageRequest.of(page, size));
    }
}
