package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "investigation_firm")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class InvestigationFirm extends VersionedEntity {
    @Column
    private String name;

    @ElementCollection
    @CollectionTable(name = "investigation_firm_emails", joinColumns = @JoinColumn(name = "activity_id", referencedColumnName = "id"))
    @Column(name = "email")
    private List<String> emails;

    @ElementCollection
    @CollectionTable(name = "investigation_firm_phones", joinColumns = @JoinColumn(name = "activity_id", referencedColumnName = "id"))
    @Column(name = "phone")
    private List<String> phones;

    @ElementCollection
    @CollectionTable(name = "investigation_firm_faxes", joinColumns = @JoinColumn(name = "activity_id", referencedColumnName = "id"))
    @Column(name = "fax")
    private List<String> faxes;

    @JsonBackReference
    @ManyToMany(mappedBy = "investigationFirms", cascade = CascadeType.ALL)
    private Set<UseInvestigation> useInvestigations;
}
