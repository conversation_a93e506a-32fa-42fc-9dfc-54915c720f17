eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:test-ipms-user}.test-ipms-user
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

spring:
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-default/protocol/openid-connect/token


ipms:
  keycloak-web-client: ipms-web
  keycloak-web-client-secret: tus7f0z0EjYrpB9NvxMSqWj5k5sSkoqJ
  keycloak-master-client-secret: AaE6sd0egKod17wD7GBcSpKUMgJbF1cJ
  keycloak-master-realm: master
  keycloak-master-clientId: 68bbeeb8-eef9-446b-928e-353b78874c72
  keycloak-master-clientNo: api-test

keycloak:
  realm: ipms-default
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn