package com.ipms.activity.mapper;

import com.ipms.activity.dto.CounterPartyDto;
import com.ipms.activity.model.CounterParty;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", uses = {LawyerMapper.class, AgencyMapper.class}, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CounterPartyMapper {
    @Mapping(expression = "java(com.ipms.activity.util.ActivityUtils.maskIdentityNumber(suspectDto.getIdentityNumber()))", target = "suspect.identityNumber")
    CounterParty toCounterParty(CounterPartyDto counterPartyDto);
    @Mapping(target = "counterRightOwner", ignore = true)
    @Mapping(expression = "java(com.ipms.activity.util.ActivityUtils.maskIdentityNumber(suspectDto.getIdentityNumber()))", target = "suspect.identityNumber")
    CounterParty toCounterParty(CounterPartyDto counterPartyDto, @MappingTarget CounterParty counterParty);
    CounterPartyDto toCounterPartyDto(CounterParty counterParty);
}
