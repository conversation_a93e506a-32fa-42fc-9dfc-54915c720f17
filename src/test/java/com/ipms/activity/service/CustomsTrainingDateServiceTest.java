package com.ipms.activity.service;

import com.ipms.activity.dto.CustomsTrainingDateDto;
import com.ipms.activity.exception.CustomsTrainingDateNotFoundException;
import com.ipms.activity.mapper.CustomsTrainingDateMapper;
import com.ipms.activity.mapper.CustomsTrainingDateMapperImpl;
import com.ipms.activity.model.CustomsTrainingDate;
import com.ipms.activity.repository.CustomsTrainingDatesRepository;
import com.ipms.activity.service.impl.CustomsTrainingDateServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
class CustomsTrainingDateServiceTest {

    @MockBean
    CustomsTrainingDateService service;

    @Mock
    CustomsTrainingDatesRepository repository;

    private final CustomsTrainingDateMapper mapper = new CustomsTrainingDateMapperImpl();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        service = new CustomsTrainingDateServiceImpl(repository, mapper);
    }

    @Test
    void save_success() {
        CustomsTrainingDateDto inputDto = generateDummyCustomsTrainingDateDto();
        CustomsTrainingDate customsTrainingDate = mapper.toCustomsTrainingDate(inputDto);
        when(repository.save(customsTrainingDate)).thenReturn(customsTrainingDate);

        CustomsTrainingDateDto result = service.save(inputDto);

        assertNotNull(result);
        assertEquals(inputDto, result);
    }

    @Test
    void save_failure() {
        CustomsTrainingDateDto inputDto = generateDummyCustomsTrainingDateDto();
        CustomsTrainingDate customsTrainingDate = mapper.toCustomsTrainingDate(inputDto);
        when(repository.save(customsTrainingDate)).thenReturn(null);

        CustomsTrainingDateDto result = service.save(inputDto);

        assertNull(result);
    }

    @Test
    void update_success() {
        Long id = 1L;
        CustomsTrainingDateDto inputDto = generateDummyCustomsTrainingDateDto();
        Optional<CustomsTrainingDate> optionalCustomsTrainingDate = Optional.of(mapper.toCustomsTrainingDate(inputDto));
        when(repository.findById(id)).thenReturn(optionalCustomsTrainingDate);
        when(repository.save(any(CustomsTrainingDate.class))).thenReturn(optionalCustomsTrainingDate.get());

        CustomsTrainingDateDto result = service.update(inputDto, id);

        assertNotNull(result);
        assertEquals(inputDto, result);
    }

    @Test
    void update_failure() {
        Long id = 1L;
        CustomsTrainingDateDto inputDto = generateDummyCustomsTrainingDateDto();

        assertThrows(CustomsTrainingDateNotFoundException.class, () -> service.update(inputDto, id));
    }

    @Test
    void delete_success() {
        Long id = 1L;
        CustomsTrainingDate existingCustomsTrainingDate = generateDummyCustomsTrainingDate();
        Optional<CustomsTrainingDate> optionalCustomsTrainingDate = Optional.of(existingCustomsTrainingDate);
        ArgumentCaptor<CustomsTrainingDate> savedCustomsTrainingDate = ArgumentCaptor.forClass(CustomsTrainingDate.class);
        when(repository.findById(id)).thenReturn(optionalCustomsTrainingDate);
        when(repository.save(savedCustomsTrainingDate.capture())).thenReturn(generateDummyCustomsTrainingDate());

        assertDoesNotThrow(() -> service.delete(id));
        verify(repository, times(1)).save(any(CustomsTrainingDate.class));
        assertTrue(savedCustomsTrainingDate.getValue().isDeleted());
    }

    @Test
    void delete_failure_recordNotFound() {
        Long id = 1L;

        assertThrows(CustomsTrainingDateNotFoundException.class, () -> service.delete(id));
    }

    @Test
    void getCustomsTrainingDateById_success() {
        Long id = 1L;
        CustomsTrainingDate existingCustomsTrainingDate = generateDummyCustomsTrainingDate();
        Optional<CustomsTrainingDate> optionalCustomsTrainingDate = Optional.of(existingCustomsTrainingDate);
        when(repository.findById(id)).thenReturn(optionalCustomsTrainingDate);

        CustomsTrainingDateDto result = service.getById(id);

        assertNotNull(result);
        assertEquals(mapper.toCustomsTrainingDatesDto(existingCustomsTrainingDate), result);
    }

    @Test
    void getCustomsTrainingDateById_failure() {
        Long id = 1L;

        assertThrows(CustomsTrainingDateNotFoundException.class, () -> service.getById(id));
    }

    private CustomsTrainingDateDto generateDummyCustomsTrainingDateDto() {
        return CustomsTrainingDateDto.builder()
                .id(1L)
                .trainingInstructionDate(LocalDate.of(2023, 10, 18))
                .regionalCustoms(123L)
                .city("456")
                .onlineTraining(true)
                .trainingDate(LocalDate.of(2023, 11, 1))
                .activityId(789L)
                .build();
    }

    private CustomsTrainingDate generateDummyCustomsTrainingDate() {
        return CustomsTrainingDate.builder()
                .id(1L)
                .trainingInstructionDate(LocalDate.of(2023, 10, 18))
                .regionalCustoms(123L)
                .city("456")
                .onlineTraining(true)
                .trainingDate(LocalDate.of(2023, 11, 1))
                .activityId(789L)
                .build();
    }
}

