package com.ipms.activity.mapper;

import com.ipms.activity.dto.CustomsTrainingDateDto;
import com.ipms.activity.model.CustomsTrainingDate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface CustomsTrainingDateMapper {

    CustomsTrainingDate toCustomsTrainingDate(CustomsTrainingDateDto customsTrainingDateDto);

    CustomsTrainingDateDto toCustomsTrainingDatesDto(CustomsTrainingDate customsTrainingDate);

    CustomsTrainingDate toCustomsTrainingDatesFromDto(CustomsTrainingDateDto dto, @MappingTarget CustomsTrainingDate customsTrainingDate);
}
