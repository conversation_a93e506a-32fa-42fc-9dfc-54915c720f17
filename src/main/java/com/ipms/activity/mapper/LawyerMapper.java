package com.ipms.activity.mapper;

import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.dto.LawyerDto;
import com.ipms.activity.model.Agency;
import com.ipms.activity.model.Lawyer;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Set;

@Mapper(componentModel = "spring")
public interface LawyerMapper {
    Lawyer toLawyer(LawyerDto dto);

    @Mapping(target = "agency", source = "agencies", qualifiedByName = "setAgency")
    LawyerDto toLawyerDto(Lawyer lawyer);
    Lawyer toLawyerFromDto(LawyerDto dto, @MappingTarget Lawyer lawyer);

    @Named("setAgency")
    static AgencyDto setAgency(Set<Agency> agencies) {
        return (agencies==null || agencies.isEmpty()) ? null : Mappers.getMapper(AgencyMapper.class).toAgencyDto(agencies.iterator().next());
    }
}
