package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ipms.core.entity.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.ResultCheckStyle;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.*;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "criminal_action_product")
@Entity
@Where(clause = "is_deleted='0'")
@SQLDelete(sql = "UPDATE criminal_action_product SET is_deleted=1 WHERE id = ?", check = ResultCheckStyle.COUNT)
@Audited
public class CriminalActionProduct extends BaseEntity {

    @Column
    private Integer quantity;

    @Column
    private String type;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonBackReference
    private CriminalAction criminalAction;
}
