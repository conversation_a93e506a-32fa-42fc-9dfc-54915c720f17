eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname:  ${HOSTNAME:prod-ipms-paramquery}.prod-ipms-paramquery
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  parameter-create-topic: parameterCreateTopic
  parameter-update-topic: parameterUpdateTopic
  parameter-delete-topic: parameterDeleteTopic
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic

spring:
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ${keycloak_ipms_backend_client_secret}
        provider:
          keycloak:
            token-uri: http://keycloak.keycloak/auth/realms/ipms-production/protocol/openid-connect/token
  cache:
    type: redis
    redis:
      time-to-live: 21600000
  redis:
    database: 0
    password: ${redis_password}
    sentinel:
      master: mymaster
      password: ${redis_password}
      nodes:
        - redis-node-0.redis-headless.redis.svc.cluster.local:26379
        - redis-node-1.redis-headless.redis.svc.cluster.local:26379
        - redis-node-2.redis-headless.redis.svc.cluster.local:26379
    lettuce:
      shutdown-timeout: 200ms

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-production
  auth-server-url: http://keycloak.keycloak/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ${keycloak_ipms_backend_client_secret}