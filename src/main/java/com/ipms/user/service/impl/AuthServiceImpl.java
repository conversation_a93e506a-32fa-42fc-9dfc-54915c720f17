package com.ipms.user.service.impl;

import com.ipms.config.security.utils.SecurityUtils;
import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.user.dto.auth.AuthDto;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;
import com.ipms.user.exception.AuthenticationFailedException;
import com.ipms.user.exception.InvalidAccessTokenException;
import com.ipms.user.exception.UserAnyRoleException;
import com.ipms.user.exception.UserNotFoundException;
import com.ipms.user.mapper.AuthDtoMapper;
import com.ipms.user.service.AuthService;
import com.ipms.user.service.keycloak.KeycloakAuthService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.TokenVerifier;
import org.keycloak.representations.AccessToken;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class AuthServiceImpl implements AuthService {


    private final KeycloakAuthService keycloakTokenService;

    private final AuthDtoMapper authDtoMapper;

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${ipms.keycloak-master-clientId}")
    private String clientId;

    @Value("${ipms.keycloak-master-clientNo}")
    private String clientNo;

    private static final String CLIENT_ID_REQUIRED = "clientId cannot be null.";

    @Override
    public AuthDto getAccessToken(String username, String password, String clientId, String otp) {
        ObjectUtils.requireNonNull(username, "username cannot be null.");
        ObjectUtils.requireNonNull(password, "password cannot be null.");
        ObjectUtils.requireNonNull(clientId, CLIENT_ID_REQUIRED);

        var tokenDto = keycloakTokenService.getAccessToken(username, password, realm, clientId, otp);
        if (tokenDto == null) {
            throw new AuthenticationFailedException();
        }

        return authDtoMapper.keycloakTokenDtoToAuthDto(tokenDto);
    }

    @Override
    public AuthDto getAccessToken() {
        var tokenDto = keycloakTokenService.getAccessToken(clientNo);
        if (tokenDto == null) {
            throw new AuthenticationFailedException();
        }
        return authDtoMapper.keycloakTokenDtoToAuthDto(tokenDto);
    }

    @Override
    public List<UserInfoDto> getUserList(String accessToken) {
        return keycloakTokenService.getUserList(accessToken, realm);
    }

    @Override
    public List<RoleDto> getRoleList(String accessToken) {
        return keycloakTokenService.getRoleList(realm, clientId, accessToken);
    }

    @Override
    public List<UserInfoDto> getUserListByRole(String accessToken, List<String> roles) {
        var userList = getUserListWithRole(accessToken);
        return userList
                .stream()
                .filter(userInfoDto -> roles.stream().anyMatch(role -> userInfoDto.getRoles().contains(role)))
                .toList();
    }

    @Override
    public List<UserInfoDto> getUsersByRole(String accessToken, String role) {
        return keycloakTokenService.getUserListByRole(realm, clientId, role, accessToken);
    }

    @Override
    public List<UserInfoDto> getUserListWithRole(String accessToken) {
        var token = getAccessToken();
        var userList = getUserList(token.getAccessToken()).stream()
                .filter(ObjectUtils.distinctByKey(UserInfoDto::getUsername)).toList();
        Map<String, UserInfoDto> userMap = getUserInfoDtoMap(userList);
        var roleList = getRoleList(accessToken);
        for(RoleDto role : roleList){
            var userListByRole = getUsersByRole(accessToken, role.getName());
            userListByRole.forEach(user-> {
                var userInfo = userMap.get(user.getUsername());
                if(userInfo != null){
                    userInfo.getRoles().add(role.getName());
                }
            });
        }
        return userList;
    }

    @Override
    public UserInfoDto getByEmail(String email, String accessToken) {
        Predicate<UserInfoDto> emailFilter = user->user.getEmail().equals(email);
        return findUser(emailFilter, accessToken);

    }

    @Override
    public UserInfoDto getByUsername(String username, String accessToken) {
        Predicate<UserInfoDto> usernameFilter = user->user.getUsername().equals(username);
        return findUser(usernameFilter, accessToken);
    }

    @Override
    public UserInfoDto getExpenseCodesByUsername(String username, String accessToken, List<String> expenseCodes) {

        return keycloakTokenService.filterExpenseCode(accessToken, realm, username, expenseCodes);


    }


    private UserInfoDto findUser(Predicate<UserInfoDto> predicate, String accessToken) {
        var users = getUserListWithRole(accessToken);
        return  users.stream()
                .filter(predicate)
                .findFirst()
                .orElseThrow(UserNotFoundException::new);
    }

    private Map<String, UserInfoDto> getUserInfoDtoMap(List<UserInfoDto> userList) {
        return userList.stream().collect(Collectors.toMap(UserInfoDto::getUsername, Function.identity()));
    }

    @Override
    public AuthDto getRefreshToken(String refreshToken, String clientId) {
        ObjectUtils.requireNonNull(refreshToken, "refreshToken cannot be null.");
        ObjectUtils.requireNonNull(clientId, CLIENT_ID_REQUIRED);

        var tokenDto = keycloakTokenService.getRefreshToken(refreshToken, realm, clientId);
        return authDtoMapper.keycloakTokenDtoToAuthDto(tokenDto);
    }

    @Override
    public void logout(String accessToken, String refreshToken, String clientId) {
        ObjectUtils.requireNonNull(accessToken, "accessToken cannot be null.");
        ObjectUtils.requireNonNull(refreshToken, "refreshToken cannot be null.");
        ObjectUtils.requireNonNull(clientId, CLIENT_ID_REQUIRED);

        keycloakTokenService.logout(accessToken, refreshToken, realm, clientId);
    }

    @Override
    @SneakyThrows
    public UserInfoDto getUserInfo(String clientId) {
        var currentAccessToken = SecurityUtils.getCurrentAccessToken()
                .orElseThrow(() -> new InvalidAccessTokenException("Access token is not found."));

        var permissionToken = keycloakTokenService.getPermissionToken(currentAccessToken, realm, clientId);

        var accessToken = TokenVerifier.create(permissionToken.getAccessToken(), AccessToken.class)
                .getToken();

        var userInfoDto = authDtoMapper.toUserInfoDto(accessToken);

        var roles = Optional.ofNullable(accessToken.getResourceAccess(clientId))
                .orElseThrow(UserAnyRoleException::new)
                .getRoles();

        userInfoDto.setRoles(roles.stream().toList());
        userInfoDto.setPermissions(accessToken.getAuthorization()
                .getPermissions()
                .stream()
                .toList());
        return userInfoDto;
    }
}

