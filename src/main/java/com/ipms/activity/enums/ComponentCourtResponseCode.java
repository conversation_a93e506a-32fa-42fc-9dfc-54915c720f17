package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum ComponentCourtResponseCode implements Code {

    COMPONENT_COURT_NOT_FOUND(1000, "error.code.activity.not_found");

    private final Integer code;
    private final String messageKey;

    ComponentCourtResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
