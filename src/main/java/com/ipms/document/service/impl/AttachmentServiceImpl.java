package com.ipms.document.service.impl;

import com.ipms.config.storage.service.StorageService;
import com.ipms.config.storage.exception.StorageFileNotFoundException;
import com.ipms.document.dto.AttachmentDto;
import com.ipms.document.mapper.AttachmentMapper;
import com.ipms.document.model.Attachment;
import com.ipms.document.repository.AttachmentRepository;
import com.ipms.document.service.AttachmentService;
import com.ipms.document.service.DocumentService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

import java.util.UUID;

@RequiredArgsConstructor
@Service
public class AttachmentServiceImpl implements AttachmentService {
    private final AttachmentRepository repository;
    private final StorageService storageService;
    private final DocumentService documentService;
    private final AttachmentMapper mapper;
    @Override
    public AttachmentDto save(AttachmentDto attachmentDto) {
        var document = documentService.getDocumentById(attachmentDto.getDocumentId());
        var attachment = mapper.toAttachment(attachmentDto);
        attachment.setDocument(document);
        attachment.setFileUniqueName(prepareDocumentName(attachment.getFileName()));
        var saved = repository.save(attachment);
        return toAttachmentDto(saved);
    }

    @Override
    public void delete(Long id, Long version) {
        var attachment = getAttachmentById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .version(version)
                .build();
        repository.save(attachment);
    }

    public static String prepareDocumentName(String fileName) {
        return  "d" + UUID.randomUUID()+"."+ FilenameUtils.getExtension(fileName);
    }

    private Attachment getAttachmentById(Long id) {
        return repository.findById(id).orElseThrow(StorageFileNotFoundException::new);
    }

    private AttachmentDto toAttachmentDto(Attachment attachment) {
        var attachmentDto = mapper.toAttachmentDto(attachment);
        var url = storageService.generateSas(mapper.toStorageFile(attachmentDto));
        attachmentDto.setFileSignedUrl(url);
        return attachmentDto;
    }
}
