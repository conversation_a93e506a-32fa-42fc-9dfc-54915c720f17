package com.ipms.activity.service.impl;

import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.exception.AgencyNotFoundException;
import com.ipms.activity.mapper.AgencyMapper;
import com.ipms.activity.model.Agency;
import com.ipms.activity.repository.AgencyRepository;
import com.ipms.activity.service.AgencyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class AgencyServiceImpl implements AgencyService {

    private final AgencyRepository repository;
    private final AgencyMapper mapper;

    @Override
    public Agency getById(Long id) {
        return repository.findById(id).orElseThrow(AgencyNotFoundException::new);
    }

    @Override
    public List<AgencyDto> getByName(String name) {
        return repository.findByNameContainsOrderByName(name)
                .stream()
                .map(mapper::toAgencyDto)
                .toList();
    }

    @Override
    public Agency save(AgencyDto agencyDto) {
        Agency agency;
        if (agencyDto.getId()==null) {
            agency = mapper.toAgency(agencyDto);
        } else {
            agency = mapper.toAgencyFromDto(agencyDto, getById(agencyDto.getId()));
        }
        return repository.save(agency);
    }
}
