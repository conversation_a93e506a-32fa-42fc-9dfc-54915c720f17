package com.ipms.activity.mapper;

import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.model.Agency;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface AgencyMapper {
    AgencyDto toAgencyDto(Agency agency);

    Agency toAgency(AgencyDto agencyDto);
    @Mapping(target = "id", ignore = true)
    Agency toAgencyFromDto(AgencyDto dto, @MappingTarget Agency agency);
}
