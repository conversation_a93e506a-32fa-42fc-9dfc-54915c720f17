package com.ipms.user.service.keycloak;

import com.ipms.user.dto.keycloak.*;
import com.ipms.user.dto.role.UserInfoDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "keycloak", url = "${keycloak.auth-server-url}")
public interface KeycloakClient {

    @PostMapping(path = "/realms/{realm}/protocol/openid-connect/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    KeycloakTokenDto getToken(@RequestBody KeycloakAuthRequestDto reqeust, @PathVariable("realm") String realm);

    @PostMapping(path = "/realms/{realm}/protocol/openid-connect/token", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    KeycloakTokenDto getPermissionToken(@RequestBody KeycloakAuthRequestDto request,
                                        @PathVariable("realm") String realm,
                                        @RequestHeader("Authorization") String bearerToken);


    @PostMapping(path = "/realms/{realm}/protocol/openid-connect/logout", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    void logout(@RequestBody KeycloakLogoutRequestDto reqeust,
                            @PathVariable("realm") String realm,
                            @RequestHeader("Authorization") String bearerToken);

    @GetMapping(path = "/admin/realms/{realm}/users?enabled=true&first=0&max=9999999", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    List<UserInfoDto> getUserList(@PathVariable("realm") String realm,
                                  @RequestHeader("Authorization") String bearerToken);

    @GetMapping(path = "/admin/realms/{realm}/clients/{clientId}/roles", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    List<RoleDto> getRoleList(@PathVariable("realm") String realm,
                              @PathVariable("clientId") String clientId,
                              @RequestHeader("Authorization") String bearerToken);

    @GetMapping(path = "/admin/realms/{realm}/clients/{clientId}/roles/{role}/users", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    List<UserInfoDto> getUserListByRole(@PathVariable("realm") String realm,
                                        @PathVariable("clientId") String clientId,
                                        @PathVariable("role") String role,
                                        @RequestHeader("Authorization") String bearerToken);
}
