package com.ipms.activity.mapper;

import com.ipms.activity.dto.TSICPackageDto;
import com.ipms.activity.model.TSICPackage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface TSICPackageMapper {

    TSICPackage toTSICPackage(TSICPackageDto tsicPackageDto);
    TSICPackageDto toTSICPackageDto(TSICPackage tsicPackage);
    @Mapping(target = "id", ignore = true)
    void toTSICPackageFromDto(TSICPackageDto dto, @MappingTarget TSICPackage tsicPackage);
}
