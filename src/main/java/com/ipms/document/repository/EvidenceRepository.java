package com.ipms.document.repository;

import com.ipms.document.model.Evidence;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EvidenceRepository extends CrudRepository<Evidence, Long>, JpaSpecificationExecutor<Evidence> {
    List<Evidence> findByIdIn (List<Long> ids);
    Optional<Evidence> findFirstByAttachmentsFileUniqueName(String fileUniqueName);
    List<Evidence> findByAttachmentsFileUniqueNameIn(List<String> fileUniqueNames);
}
