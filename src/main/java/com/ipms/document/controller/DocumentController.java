package com.ipms.document.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.document.dto.*;
import com.ipms.document.service.DocumentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
public class DocumentController {
    private final DocumentService service;

    @PostMapping("/manual")
    public BaseResponse<DocumentDto> save(@Valid @RequestBody DocumentManualDto documentManualDto) {
        var dto = service.save(documentManualDto);
        return BaseResponse.<DocumentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PostMapping("/with-attachment")
    public BaseResponse<DocumentDto> save(@Valid @RequestBody DocumentCreateDto documentCreateDto) {
        var dto = service.save(documentCreateDto);
        return BaseResponse.<DocumentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<DocumentDto> update(@PathVariable Long id, @Valid @RequestBody DocumentDto documentDto) {
        var dto = service.update(documentDto, id);
        return BaseResponse.<DocumentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<DocumentDto> get(@PathVariable Long id) {
        var dto = service.getById(id);
        return BaseResponse.<DocumentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<DocumentDto>> getByIds(@PathVariable List<Long> ids) {
        return BaseResponse.<List<DocumentDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIdIn(ids))
                .build();
    }

    @GetMapping("/firm/{firmId}/{page}/{size}")
    public BaseResponse<DocumentPageDto> getByFirm(DocumentFilterRequest filterRequest,
                                             @PathVariable int page,
                                             @PathVariable int size) {
        return BaseResponse.<DocumentPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest, page, size))
                .build();
    }

    @GetMapping("/file/{name}")
    public BaseResponse<DocumentDto> getByName(@PathVariable String name) {
        var dto = service.getByFileUniqueName(name);
        return BaseResponse.<DocumentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping("/files/{names}")
    public BaseResponse<List<DocumentDto>> getByNames(@PathVariable List<String> names) {
        return BaseResponse.<List<DocumentDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByFileUniqueName(names))
                .build();
    }

    @GetMapping("/documents/{page}/{size}")
    public BaseResponse<DocumentPageDto> getAll(@PathVariable int page,
                                                  @PathVariable int size, DocumentFilterRequest request) {
        return BaseResponse.<DocumentPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(request, page, size))
                .build();
    }

    @GetMapping("/summaries")
    public BaseResponse<List<DocumentSummaryDto>> getAllIds(DocumentFilterRequest request) {
        return BaseResponse.<List<DocumentSummaryDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getDocumentSummaries(request))
                .build();
    }

}
