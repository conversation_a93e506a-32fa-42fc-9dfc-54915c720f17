package com.ipms.activity.service;

import com.ipms.activity.dto.CounterPartyDto;
import com.ipms.activity.dto.CounterRightOwnerDto;
import com.ipms.activity.dto.SuspectDto;
import com.ipms.activity.mapper.CounterPartyMapper;
import com.ipms.activity.mapper.CounterPartyMapperImpl;
import com.ipms.activity.model.CounterParty;
import com.ipms.activity.model.CounterRightOwner;
import com.ipms.activity.repository.CounterPartyRepository;
import com.ipms.activity.service.impl.CounterPartyServiceImpl;
import com.ipms.activity.util.ActivityUtils;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.model.TransferEvent;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
public class CounterPartyServiceTest {
    @MockBean
    private CounterPartyService service;
    @Mock
    private CounterRightOwnerService counterRightOwnerService;
    @Mock
    private CounterPartyRepository repository;
    @Mock
    private ProducerService producerService;
    private CounterPartyMapper mapper = new CounterPartyMapperImpl();
    private CounterParty counterParty;
    private CounterPartyDto counterPartyDto;

    @Before
    public void setUp() {
        service = new CounterPartyServiceImpl(counterRightOwnerService, repository, mapper, producerService);
        counterParty = getCounterParty();
        counterPartyDto = getCounterPartyDto();
    }

    private CounterParty getCounterParty() {
        CounterRightOwner counterRightOwner = CounterRightOwner.builder()
                .id(1L)
                .version(0L)
                .name("cro-1")
                .address("address-1")
                .build();

        return  CounterParty.builder()
                .id(1L)
                .version(0L)
                .activityId(1L)
                .counterRightOwner(counterRightOwner)
                .build();
    }

    private CounterPartyDto getCounterPartyDto() {
        CounterRightOwnerDto counterRightOwnerDto = CounterRightOwnerDto.builder()
                .id(1L)
                .version(0L)
                .name("cro-1")
                .address("address-1")
                .build();


        var suspectDto = SuspectDto.builder()
                .name("name-1")
                .surname("surname-1")
                .identityNumber("123456")
                .build();

        return CounterPartyDto.builder()
                .id(1L)
                .version(0L)
                .activityId(1L)
                .counterRightOwner(counterRightOwnerDto)
                .suspect(suspectDto)
                .build();
    }
    
    @Test
    public void whenSave_thenReturnCounterPartyDto() {
        ArgumentCaptor<CounterParty> counterPartyCaptor = ArgumentCaptor.forClass(CounterParty.class);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.when(repository.save(counterPartyCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));
        var saved = service.save(counterPartyDto);
        assertThat(saved.getId()).isEqualTo(1l);
        assertEquals(ActivityUtils.maskIdentityNumber(counterPartyDto.getSuspect().getIdentityNumber()),
                saved.getSuspect().getIdentityNumber());

    }

    @Test
    public void whenSaveBulk_thenReturnCounterPartyDtoList() {
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.when(repository.save(Mockito.any(CounterParty.class)))
                .thenReturn(counterParty);
        assertThat(List.of(counterPartyDto)).isEqualTo(service.saveBulk(List.of(counterPartyDto)));
    }

    @Test
    public void whenUpdate_thenReturnCounterPartyDto() {
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.ofNullable(counterParty));
        Mockito.when(repository.save(Mockito.any(CounterParty.class)))
                .thenReturn(counterParty);
        assertEquals(counterPartyDto,  service.save(counterPartyDto));
    }

    @Test
    public void whenGetAllByActivityId_thenReturnCounterPartyDto() {
        Mockito.when(repository.findAllByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(counterParty));
        assertEquals(List.of(counterPartyDto), service.getAllByActivityId(1L));
    }

    @Test
    public void givenId_whenDelete_thenDoesNotThrowAnyException() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(counterParty));
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        ArgumentCaptor<CounterParty> counterPartyCaptor = ArgumentCaptor.forClass(CounterParty.class);
        Mockito.when(repository.save(counterPartyCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));

        service.delete(1L, 1L);
        Assertions.assertTrue(counterPartyCaptor.getValue().isDeleted());
    }

    @Test
    public void givenId_whenGetById_thenReturnCounterPartyDto() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.ofNullable(counterParty));
        assertEquals(counterPartyDto, service.getById(1L));
    }

    @Test
    public void givenId_whenGetCounterPartyById_thenReturnCounterParty() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.ofNullable(counterParty));
        assertEquals(counterParty, service.getCounterPartyById(1L));
    }
}