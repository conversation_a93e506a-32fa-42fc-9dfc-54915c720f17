package com.ipms.activity.mapper;

import com.ipms.activity.dto.CustomsSuspensionsReceiverDto;
import com.ipms.activity.model.CustomsSuspensionsReceiver;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomsSuspensionReceiverMapper {

    CustomsSuspensionsReceiverDto toDto(CustomsSuspensionsReceiver reciever);

    CustomsSuspensionsReceiver toCustomsSuspensionsReceiver(CustomsSuspensionsReceiverDto dto);
}
