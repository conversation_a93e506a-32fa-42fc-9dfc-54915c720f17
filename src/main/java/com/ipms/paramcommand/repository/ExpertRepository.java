package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.Expert;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface ExpertRepository extends PagingAndSortingRepository<Expert, Long>, JpaSpecificationExecutor<Expert> {
    List<Expert> findByIdIn(List<Long> ids);
}
