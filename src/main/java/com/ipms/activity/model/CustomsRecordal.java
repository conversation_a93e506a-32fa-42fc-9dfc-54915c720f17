package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ipms.activity.enums.MatterType;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "customs_recordal")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class CustomsRecordal extends VersionedEntity {

   @Column
   private MatterType matterType;

   @Column
   private LocalDate renewalDate;

    @Column
    private String registrationNumber;

    @OneToOne(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinColumn(name = "activity_id", referencedColumnName = "id")
    @JsonBackReference
    @NotAudited
    private Activity activity;
}
