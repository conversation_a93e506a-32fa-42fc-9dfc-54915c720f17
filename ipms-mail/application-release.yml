eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:release-ipms-mail}.release-ipms-mail
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  email-topic: emailReleaseTopic
  retry-policy:
    max-attempts: 5
  email-trx-topic: emailTrxReleaseTopic
  reply-status-topic: replyStatusReleaseTopic
  notification-topic: notificationReleaseTopic
  data-transfer-topic: dataTransferReleaseTopic
  invoice-is-ready-topic: invoiceIsReadyReleaseTopic

spring:
  datasource:
    url: ************************************************************************************
    username: ipms_release_user
    password: zwk9w4TpjzG7TVX9
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ********************************
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-release/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-release
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ********************************

azure:
  mail:
    clientId: 7cb35efd-5c98-4286-af82-de81b804e342
    clientSecret: ****************************************
    tenantId: ce77442e-69e4-4367-939d-88d85cabf43e
    authTenant: common
    graphUserScopes: user.read,mail.read,mail.send
    scopes:
      - https://graph.microsoft.com/.default
    default-folder-addresses:
      - <EMAIL>
    ipms-folder-addresses:
      - <EMAIL>
    period: 65
    default-folder: inbox
    ipms-folder: load-ipms
    ipms-document-folder: load-ipms-for-document
    categories:
      - IPMS
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=ipmstestsa;AccountKey=****************************************************************************************;
    container-name: ipmstestcontainer
    sas-host-url: https://ipmstestsa.blob.core.windows.net/ipmstestcontainer/