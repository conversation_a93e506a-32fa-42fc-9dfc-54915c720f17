package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.CountryMatterTypeDto;
import com.ipms.paramcommand.dto.FilterRequest;
import com.ipms.paramcommand.dto.groups.UpdateGroup;
import com.ipms.paramcommand.service.CountryMatterTypeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("matter-type")
@Tag(name = "country-matter-type", description = "This endpoints contains country-matter-type APIs")
@Validated
public class CountryMatterTypeController {

    private final CountryMatterTypeService service;

    @PostMapping
    public BaseResponse<CountryMatterTypeDto> save(@Valid @RequestBody CountryMatterTypeDto dto) {
        return BaseResponse.<CountryMatterTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping
    public BaseResponse<CountryMatterTypeDto> update(@Validated(UpdateGroup.class) @Valid @RequestBody CountryMatterTypeDto dto) {
        return BaseResponse.<CountryMatterTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(dto))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/country/{country}")
    public BaseResponse<List<CountryMatterTypeDto>> getByCountry(@PathVariable String country) {
        return BaseResponse.<List<CountryMatterTypeDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByCountry(country))
                .build();
    }

    @GetMapping
    public BaseResponse<List<CountryMatterTypeDto>> getList(FilterRequest filterRequest){
        return BaseResponse.<List<CountryMatterTypeDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest))
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<CountryMatterTypeDto> get(@PathVariable Long id) {
        return BaseResponse.<CountryMatterTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getMatterTypeById(id))
                .build();
    }
}
