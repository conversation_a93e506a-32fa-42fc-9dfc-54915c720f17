package com.ipms.activity.service;

import com.ipms.activity.dto.PreliminaryInjunctionDetailDto;
import com.ipms.activity.dto.PreliminaryInjunctionDto;
import com.ipms.activity.enums.PIPartyRequestingPIOrder;
import com.ipms.activity.enums.PIResult;
import com.ipms.activity.enums.PIType;
import com.ipms.activity.mapper.PreliminaryInjunctionDetailMapper;
import com.ipms.activity.mapper.PreliminaryInjunctionDetailMapperImpl;
import com.ipms.activity.model.PreliminaryInjunction;
import com.ipms.activity.model.PreliminaryInjunctionDetail;
import com.ipms.activity.repository.PreliminaryInjunctionDetailRepository;
import com.ipms.activity.service.impl.PreliminaryInjunctionDetailServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(SpringRunner.class)
public class PreliminaryInjunctionDetailServiceTest {

    @MockBean
    private PreliminaryInjunctionDetailService detailService;
    @Mock
    private PreliminaryInjunctionDetailRepository repository;

    private PreliminaryInjunctionDetailMapper mapper = new PreliminaryInjunctionDetailMapperImpl();

    @Before
    public void setup() {
        detailService = new PreliminaryInjunctionDetailServiceImpl(repository, mapper);
    }

    @Test
    public void whenGetById_thenReturnPreliminaryInjunctionDetailDto() {
        var preliminaryInjunction = PreliminaryInjunction.builder()
                .types(List.of(PIType.PI_ORDER_FOR_PREVENTION_OF_ASSIGNMENT))
                .partyRequestingPIOrder(PIPartyRequestingPIOrder.COUNTER_PARTY)
                .build();

        var preliminaryInjunctionDetail = PreliminaryInjunctionDetail.builder()
                .id(1L)
                .preliminaryInjunction(preliminaryInjunction)
                .filingDate(LocalDate.now())
                .executionDate(LocalDate.now())
                .executionNo("-")
                .executionOffice("-")
                .executionOpeningDate(LocalDate.now())
                .instructionFileNo("-")
                .notificationDate(LocalDate.now())
                .result(PIResult.EXECUTED)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong())).thenReturn(Optional.of(preliminaryInjunctionDetail));
        assertThat(detailService.getById(1L).getId()).isEqualTo(1L);
    }

    @Test
    public void whengetSaveFromPreliminaryInjunction_thenReturnPreliminaryInjunctionDetailList() {
        var preliminaryInjunctionDto = PreliminaryInjunctionDto.builder()
                .types(List.of(PIType.PI_ORDER_FOR_PREVENTION_OF_ASSIGNMENT.name()))
                .filingDate(LocalDate.now())
                .partyRequestingPIOrder(PIPartyRequestingPIOrder.COUNTER_PARTY.name())
                .build();

        var preliminaryInjunction = PreliminaryInjunction.builder()
                .types(List.of(PIType.PI_ORDER_FOR_PREVENTION_OF_ASSIGNMENT))
                .partyRequestingPIOrder(PIPartyRequestingPIOrder.COUNTER_PARTY)
                .build();

        assertThat(detailService.saveFromPreliminaryInjunction(preliminaryInjunctionDto, preliminaryInjunction)).hasSize(1);
    }

    @Test
    public void givenDetailWithId_whenSaveDetails_thenReturnPreliminaryInjunctionDetailList() {
        var detailDto = PreliminaryInjunctionDetailDto.builder()
                .id(1L)
                .filingDate(LocalDate.now())
                .executionDate(LocalDate.now())
                .executionNo("test-execution-no")
                .executionOffice("test-execution-office")
                .executionOpeningDate(LocalDate.now())
                .instructionFileNo("test-instruction-filer-no")
                .notificationDate(LocalDate.now())
                .result(PIResult.EXECUTED.name())
                .build();

        var preliminaryInjunction = PreliminaryInjunction.builder()
                .types(List.of(PIType.PI_ORDER_FOR_PREVENTION_OF_ASSIGNMENT))
                .partyRequestingPIOrder(PIPartyRequestingPIOrder.COUNTER_PARTY)
                .build();

        var preliminaryInjunctionDetail = PreliminaryInjunctionDetail.builder()
                .id(1L)
                .preliminaryInjunction(preliminaryInjunction)
                .filingDate(LocalDate.now())
                .executionDate(LocalDate.now())
                .executionNo("-")
                .executionOffice("-")
                .executionOpeningDate(LocalDate.now())
                .instructionFileNo("-")
                .notificationDate(LocalDate.now())
                .result(PIResult.EXECUTED)
                .build();

        preliminaryInjunction.setDetails(new ArrayList<>(Arrays.asList(preliminaryInjunctionDetail)));

        Mockito.when(repository.findById(Mockito.anyLong())).thenReturn(Optional.of(preliminaryInjunctionDetail));
        detailService.saveDetails(List.of(detailDto), preliminaryInjunction);
        assertThat(preliminaryInjunction.getDetails().get(0)).isEqualTo(mapper.toPreliminaryInjunctionDetail(detailDto));
    }

}
