eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/registry/eureka/
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${instanceId:${random.value}}
    preferIpAddress: true

spring:
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth/realms/ipms-default/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-default
  auth-server-url: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn

cron:
  mail:
    default-folder: 0 * * ? * *
    ipms-folder: 0 * * ? * *
    ipms-document-folder: 0 * * ? * *
    rollback-all: 0 * * ? * *
    retry-all: 0 * * ? * *
  transfer:
    send-all: 0 */10 * ? * *
  billing:
    billingProcess: 0 0 1 * * *
    billingProcessByBillingAmount: 0 0 1 * * *
    billingProcessByStatus: 0 0 1 * * *
    autoDeleteUnapprovedPayments: 0 45 20 * * *
  timesheet:
    approveBySystem: 0 0 9,20 * * *
  integration:
    fetch-exchange-rates: 0 0 * * * *
    send-billing-order: 0 */10 * ? * *
    update-external-order-number: 0 */10 * ? * *
    fetch-invoices: 0 */10 * ? * *
    fetch-prices: 0 * * ? * *

jde:
  main-company: "00004"
  minus-day-invoice: 7
  minus-day-price: 1