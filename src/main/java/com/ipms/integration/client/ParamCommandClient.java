package com.ipms.integration.client;

import com.ipms.core.exception.config.RetreiveMessageErrorDecoder;
import com.ipms.integration.dto.IssuerResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = "ipms-paramcommand", path = "/api/paramcommand", configuration = RetreiveMessageErrorDecoder.class)
public interface ParamCommandClient {

    @GetMapping("/issuer/{id}")
    IssuerResponse getIssuer(@PathVariable Long id);
}
