package com.ipms.activity.service.impl;

import com.ipms.activity.dto.PreliminaryInjunctionDetailDto;
import com.ipms.activity.dto.PreliminaryInjunctionDto;
import com.ipms.activity.enums.PIType;
import com.ipms.activity.exception.PIDetailNotFoundException;
import com.ipms.activity.mapper.PreliminaryInjunctionDetailMapper;
import com.ipms.activity.model.PreliminaryInjunction;
import com.ipms.activity.model.PreliminaryInjunctionDetail;
import com.ipms.activity.repository.PreliminaryInjunctionDetailRepository;
import com.ipms.activity.service.PreliminaryInjunctionDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Service
public class PreliminaryInjunctionDetailServiceImpl implements PreliminaryInjunctionDetailService {
    private final PreliminaryInjunctionDetailRepository repository;
    private final PreliminaryInjunctionDetailMapper mapper;
    @Override
    public PreliminaryInjunctionDetailDto getById(Long id) {
        return mapper.toPreliminaryInjunctionDetailDto(getPreliminaryInjunctionDetailById(id));
    }

    private PreliminaryInjunctionDetail getPreliminaryInjunctionDetailById(Long id) {
        return repository.findById(id).orElseThrow(PIDetailNotFoundException::new);
    }

    @Override
    public List<PreliminaryInjunctionDetail> saveFromPreliminaryInjunction(PreliminaryInjunctionDto preliminaryInjunctionDto, PreliminaryInjunction preliminaryInjunction) {
        return preliminaryInjunctionDto.getTypes()
                .stream()
                .map(type -> getFromPreliminaryInjunction(preliminaryInjunctionDto, preliminaryInjunction, type))
                .toList();
    }

    private PreliminaryInjunctionDetail getFromPreliminaryInjunction(PreliminaryInjunctionDto preliminaryInjunctionDto, PreliminaryInjunction preliminaryInjunction, String type) {
        return PreliminaryInjunctionDetail.builder()
                .filingDate(preliminaryInjunctionDto.getFilingDate())
                .type(PIType.valueOf(type))
                .preliminaryInjunction(preliminaryInjunction)
                .build();
    }

    @Override
    public void saveDetails(List<PreliminaryInjunctionDetailDto> updatedDetails, PreliminaryInjunction preliminaryInjunction) {
        if (updatedDetails != null) {
            var existingDetails = preliminaryInjunction.getDetails();
            deleteDetails(updatedDetails, existingDetails);
            updatedDetails.forEach(detailDto -> {
                if (detailDto.getId() != null) {
                    updateExistingDetail(existingDetails, detailDto);
                } else {
                    var detail = mapper.toPreliminaryInjunctionDetail(detailDto);
                    detail.setPreliminaryInjunction(preliminaryInjunction);
                    existingDetails.add(detail);
                }
            });
        }
    }

    public void updateExistingDetail(List<PreliminaryInjunctionDetail> existingDetails,
                                     PreliminaryInjunctionDetailDto detailDto) {
        var existingDetail = existingDetails.stream()
                .filter(detail -> detail.getId().equals(detailDto.getId()))
                .findFirst()
                .orElseThrow(PIDetailNotFoundException::new);
        var detail = mapper.toPreliminaryInjunctionDetailFromDto(detailDto, existingDetail);
        repository.save(detail);
    }

    private void deleteDetails(List<PreliminaryInjunctionDetailDto> updatedDetails,
                               List<PreliminaryInjunctionDetail> existingDetails) {
        var detailsToBeDeleted = getDetailsToBeDeleted(updatedDetails, existingDetails);
        detailsToBeDeleted.forEach(detail -> repository.save(detail.toBuilder().isDeleted(Boolean.TRUE).build()));
        existingDetails.removeAll(detailsToBeDeleted);
    }

    private List<PreliminaryInjunctionDetail> getDetailsToBeDeleted(List<PreliminaryInjunctionDetailDto> updatedDetails, List<PreliminaryInjunctionDetail> existingDetails) {
        return existingDetails.stream()
                .filter(detail -> detail.getId() != null)
                .filter(detail -> updatedDetails.stream()
                        .filter(detailDto -> detailDto.getId() != null)
                        .noneMatch(detailDto -> Objects.equals(detailDto.getId(), detail.getId())))
                .toList();
    }
}
