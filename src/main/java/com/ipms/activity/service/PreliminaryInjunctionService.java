package com.ipms.activity.service;

import com.ipms.activity.dto.PreliminaryInjunctionDto;
import com.ipms.activity.model.PreliminaryInjunction;

public interface PreliminaryInjunctionService {
    PreliminaryInjunctionDto getById(Long id);
    PreliminaryInjunction getPreliminaryInjunctionById(Long id);

    void save(PreliminaryInjunction preliminaryInjunction, PreliminaryInjunctionDto preliminaryInjunctionDto);

    void update(PreliminaryInjunctionDto dto, PreliminaryInjunction preliminaryInjunction);

    void setFileSignedUrls(PreliminaryInjunctionDto preliminaryInjunctionDto);
}
