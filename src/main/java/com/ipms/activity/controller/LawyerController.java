package com.ipms.activity.controller;

import com.ipms.activity.dto.LawyerDto;
import com.ipms.activity.dto.groups.UpdateActivityGroup;
import com.ipms.activity.service.LawyerService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("lawyer")
@Tag(name = "lawyer", description = "This endpoint contains lawyer APIs ")
public class LawyerController {

    private final LawyerService service;

    @PostMapping("/add-to-related-card/{counterRightOwnerCardId}")
    public BaseResponse<LawyerDto> addLawyerToCounterRightOwnerCard(@PathVariable Long counterRightOwnerCardId,
                                                                        @RequestBody @Valid LawyerDto dto) {
        return BaseResponse.<LawyerDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.addToCounterRightOwnerCard(dto, counterRightOwnerCardId))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<LawyerDto> update(@RequestBody @Validated(UpdateActivityGroup.class) LawyerDto dto,
                                          @PathVariable Long id) {
        return BaseResponse.<LawyerDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(dto, id))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/by-related-card-id/{counterRightOwnerCardId}")
    public BaseResponse<List<LawyerDto>> getLawyer(@PathVariable Long counterRightOwnerCardId) {
        return BaseResponse.<List<LawyerDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByCounterRightOwnerCardId(counterRightOwnerCardId))
                .build();
    }
}
