package com.ipms.document.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.document.dto.*;
import com.ipms.document.enums.DelegationPowerType;
import com.ipms.document.enums.DocumentType;
import com.ipms.document.enums.POALimited;
import com.ipms.document.service.DocumentService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-document/application.yml")
public class DocumentControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private DocumentService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var documentCreateDto = DocumentCreateDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .poaLimited(POALimited.FILE_BASED.name())
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER.name())
                .activityId(1L)
                .contentType("application/pdf")
                .fileName("example.pdf")
                .fileUniqueName("unique_name")
                .firms(Arrays.asList(1L, 2L, 3L))
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2023, 12, 31))
                .notificationDate(LocalDate.of(2023, 12, 28))
                .build();
        var documentDto = DocumentDto.builder()
                .poaLimited(POALimited.FILE_BASED.name())
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER.name())
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        Mockito.when(service.save(Mockito.any(DocumentCreateDto.class)))
                .thenReturn(documentDto);

        mvc.perform(post("/with-attachment")
                        .content(objectMapper.writeValueAsString(documentCreateDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenManual_whenSave_thenReturnBaseResponse() throws Exception {
        var documentManualDto = DocumentManualDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .activityId(1L)
                .firms(Arrays.asList(1L, 2L, 3L))
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2023, 12, 31))
                .notificationDate(LocalDate.of(2023, 12, 28))
                .build();
        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        Mockito.when(service.save(Mockito.any(DocumentManualDto.class)))
                .thenReturn(documentDto);

        mvc.perform(post("/manual")
                        .content(objectMapper.writeValueAsString(documentManualDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {
        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        Mockito.when(service.update(Mockito.any(DocumentDto.class), Mockito.anyLong()))
                .thenReturn(documentDto);

        mvc.perform(put("/1")
                        .content(objectMapper.writeValueAsString(documentDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());
        mvc.perform(delete("/1/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetById_thenReturnBaseResponse() throws Exception {
        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        Mockito.when(service.getById(Mockito.anyLong()))
                .thenReturn(documentDto);

        mvc.perform(get("/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByIds_thenReturnBaseResponse() throws Exception {
        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        Mockito.when(service.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(documentDto));

        mvc.perform(get("/ids/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByFirm_thenReturnBaseResponse() throws Exception {
        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        var documentPageDto = DocumentPageDto.builder()
                .documents(List.of(documentDto))
                .totalPages(1L)
                .totalElements(1L)
                .build();
        Mockito.when(service.getAll(Mockito.any(DocumentFilterRequest.class),Mockito.anyInt(),Mockito.anyInt()))
                .thenReturn(documentPageDto);

        mvc.perform(get("/firm/1/0/5")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByName_thenReturnBaseResponse() throws Exception {
        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        Mockito.when(service.getByFileUniqueName(Mockito.anyString()))
                .thenReturn(documentDto);

        mvc.perform(get("/file/filename.txt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByNames_thenReturnBaseResponse() throws Exception {
        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        Mockito.when(service.getByFileUniqueName(Mockito.anyList()))
                .thenReturn(List.of(documentDto));

        mvc.perform(get("/files/filename.txt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetAll_thenReturnBaseResponse() throws Exception {
        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        var documentPageDto = DocumentPageDto.builder()
                .documents(List.of(documentDto))
                .totalPages(1L)
                .totalElements(1L)
                .build();
        Mockito.when(service.getAll(Mockito.any(DocumentFilterRequest.class), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(documentPageDto);
        mvc.perform(get("/documents/0/15?ids=1,2,3&types=AGREEMENT")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

}
