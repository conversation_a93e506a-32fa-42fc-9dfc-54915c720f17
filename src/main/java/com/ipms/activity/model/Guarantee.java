package com.ipms.activity.model;

import com.ipms.activity.enums.CurrencyCode;
import com.ipms.activity.enums.GuaranteeType;
import com.ipms.activity.enums.PartyProvidingGuarantee;
import com.ipms.activity.enums.PaymentStatus;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "guarantee")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
public class Guarantee extends VersionedEntity {

    @Column
    private Long preliminaryInjunctionId;

    @Column
    private BigDecimal guaranteeAmount;

    @Column
    @Enumerated(value = EnumType.STRING)
    private CurrencyCode currencyCode;

    @Column
    private GuaranteeType guaranteeType;

    @Column
    private PartyProvidingGuarantee partyProvidingGuarantee;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "guarantee")
    private List<PIMinutesAttachment> piMinutesAttachments;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "guarantee")
    private List<ProformaDebitLetterAttachment> proformaDebitLetterAttachments;

    @Column
    private LocalDate dateOfDepositToCourt;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "guarantee")
    private List<CashierReceiptAttachment> cashierReceiptAttachments;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "guarantee")
    private List<LetterOfGuaranteeAttachment> letterOfGuaranteeAttachments;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "guarantee")
    private List<ReceiptAttachment> receiptAttachments;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "guarantee")
    private List<GuaranteePayment> guaranteePayments;

    @Column
    private String safeNumber;

    @Column
    private PaymentStatus paymentStatus;
}
