package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum LawyerResponseCode implements Code {

    LAWYER_NOT_FOUND(1000, "error.code.lawyer.not_found");

    private final Integer code;
    private final String messageKey;

    LawyerResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
