package com.ipms.activity.model;

import com.ipms.activity.enums.EnforcementDepartmentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Embeddable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class EnforcementDepartment {
    private EnforcementDepartmentType enforcementType;
    private String enforcementCity;
    private String enforcementProvince;
}
