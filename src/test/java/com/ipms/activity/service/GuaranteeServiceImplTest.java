package com.ipms.activity.service;

import com.ipms.activity.dto.GuaranteeDto;
import com.ipms.activity.enums.CurrencyCode;
import com.ipms.activity.enums.GuaranteeType;
import com.ipms.activity.enums.PartyProvidingGuarantee;
import com.ipms.activity.exception.AttachmentNotFoundException;
import com.ipms.activity.model.Guarantee;
import com.ipms.activity.repository.GuaranteeRepository;
import com.ipms.activity.mapper.GuaranteeMapper;
import com.ipms.activity.service.impl.GuaranteeServiceImpl;
import com.ipms.core.entity.BaseEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class GuaranteeServiceImplTest {

    @Mock
    private GuaranteeRepository repository;

    @Mock
    private GuaranteeMapper mapper;

    @InjectMocks
    private GuaranteeServiceImpl service;

    @Mock
    private BaseEntity guarantee;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void save_ShouldReturnSavedGuaranteeDto_WhenGivenGuaranteeDto() {
        GuaranteeDto inputDto = createSampleGuaranteeDto();
        Guarantee mappedEntity = createSampleGuarantee();
        when(mapper.toGuarantee(inputDto)).thenReturn(mappedEntity);
        when(repository.save(mappedEntity)).thenReturn(mappedEntity);
        when(mapper.toGuaranteeDto(mappedEntity)).thenReturn(inputDto);

        GuaranteeDto resultDto = service.save(inputDto);

        assertNotNull(resultDto);
        assertEquals(inputDto, resultDto);
        verify(mapper, times(1)).toGuarantee(inputDto);
        verify(repository, times(1)).save(mappedEntity);
        verify(mapper, times(1)).toGuaranteeDto(mappedEntity);
    }

    @Test
    void getEntityById_ShouldReturnGuarantee_WhenIdExists() {
        Long existingId = 1L;
        Guarantee existingGuarantee = createSampleGuarantee();
        when(repository.findById(existingId)).thenReturn(Optional.of(existingGuarantee));

        Guarantee result = service.getEntityById(existingId);

        assertNotNull(result);
        assertEquals(existingGuarantee, result);
        verify(repository, times(1)).findById(existingId);
    }

    @Test
    void getEntityById_ShouldThrowAttachmentNotFoundException_WhenIdDoesNotExist() {
        Long nonExistingId = 2L;
        when(repository.findById(nonExistingId)).thenReturn(Optional.empty());

        assertThrows(EntityNotFoundException.class, () -> service.getEntityById(nonExistingId));
        verify(repository, times(1)).findById(nonExistingId);
    }

    @Test
    void update_ShouldReturnUpdatedGuaranteeDto_WhenGivenGuaranteeDto() {
        GuaranteeDto inputDto = createSampleGuaranteeDto();
        Guarantee existingGuarantee = createSampleGuarantee();
        Guarantee updatedGuarantee = createSampleGuarantee();

        when(repository.findById(anyLong())).thenReturn(Optional.of(existingGuarantee));
        when(repository.save(any(Guarantee.class))).thenReturn(updatedGuarantee);
        when(mapper.toGuaranteeFromDto(any(GuaranteeDto.class), any(Guarantee.class))).thenReturn(updatedGuarantee);
        when(mapper.toGuaranteeDto(any(Guarantee.class))).thenReturn(inputDto);

        GuaranteeDto resultDto = service.update(inputDto);

        assertNotNull(resultDto);
        assertEquals(inputDto, resultDto);
        verify(repository, times(1)).findById(anyLong());
        verify(repository, times(1)).save(any(Guarantee.class));
        verify(mapper, times(1)).toGuaranteeFromDto(any(GuaranteeDto.class), any(Guarantee.class));
        verify(mapper, times(1)).toGuaranteeDto(any(Guarantee.class));
    }


    @Test
    void delete_ShouldSetIsDeletedToTrue_WhenGivenId() {
        Long existingId = 1L;
        Guarantee existingGuarantee = createSampleGuarantee();

        ArgumentCaptor<Guarantee> guaranteeCaptor = ArgumentCaptor.forClass(Guarantee.class);

        when(repository.findById(existingId)).thenReturn(Optional.of(existingGuarantee));

        service.delete(existingId);

        verify(repository, times(1)).save(guaranteeCaptor.capture());

        Guarantee capturedGuarantee = guaranteeCaptor.getValue();

        assertTrue(capturedGuarantee.isDeleted());
        assertEquals(existingGuarantee.getId(), capturedGuarantee.getId()); // diğer alanları da isteğe bağlı olarak kontrol et
    }


    private GuaranteeDto createSampleGuaranteeDto() {
        return GuaranteeDto.builder()
                .id(1L)
                .version(1L)
                .preliminaryInjunctionId(123L)
                .guaranteeAmount(BigDecimal.valueOf(1000))
                .currencyCode("USD")
                .guaranteeType("CASH")
                .partyProvidingGuarantee("CLIENT")
                .dateOfDepositToCourt(LocalDate.now())
                .safeNumber("123456")
                .build();
    }

    private Guarantee createSampleGuarantee() {
        return Guarantee.builder()
                .id(1L)
                .version(1L)
                .preliminaryInjunctionId(123L)
                .guaranteeAmount(BigDecimal.valueOf(1000))
                .currencyCode(CurrencyCode.USD)
                .guaranteeType(GuaranteeType.CASH)
                .partyProvidingGuarantee(PartyProvidingGuarantee.CLIENT)
                .dateOfDepositToCourt(LocalDate.now())
                .safeNumber("123456")
                .piMinutesAttachments(Collections.emptyList())
                .proformaDebitLetterAttachments(Collections.emptyList())
                .cashierReceiptAttachments(Collections.emptyList())
                .letterOfGuaranteeAttachments(Collections.emptyList())
                .receiptAttachments(Collections.emptyList())
                .build();
    }
}
