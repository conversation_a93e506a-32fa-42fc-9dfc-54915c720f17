package com.ipms.document.aspect;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.TransactionState;
import com.ipms.core.common.model.TransactionEvent;
import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.document.dto.MailEvent;
import com.ipms.document.model.DraftDocument;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Aspect
@Slf4j
@Setter
public class DraftDocumentServiceHandler {

    private final ProducerService producerService;

    @Value("${kafka.email-trx-topic}")
    private String emailTrxTopic;

    @Pointcut("execution(* com.ipms.document.service.DraftDocumentService.processMailEvent(*))")
    public void processEventPointcut() {
        //pointcut method
    }

    @AfterThrowing(pointcut="processEventPointcut()", throwing="nullPointerException")
    public void handleException(JoinPoint jp, NullPointerException nullPointerException) {
        send(jp, TransactionState.FAILED);
    }

    @AfterThrowing(pointcut="processEventPointcut()", throwing="feignException")
    public void handleFeignException(JoinPoint jp, FeignException feignException) {
        send(jp, TransactionState.RETRY);
    }

    @AfterThrowing(pointcut="processEventPointcut()", throwing="dataAccessException")
    public void handleDataAccessException(JoinPoint jp, DataAccessException dataAccessException) {
        send(jp, TransactionState.RETRY);
    }

    @AfterReturning(pointcut="processEventPointcut()", returning="draftDocument")
    public void handleSuccess(JoinPoint jp, DraftDocument draftDocument) {
        send(jp, TransactionState.COMPLETED);
    }

    private void send(JoinPoint jp, TransactionState state) {
        var mailId = ((MailEvent) jp.getArgs()[0]).getId();
        var event = TransactionEvent.builder()
                .domainObjectId(mailId)
                .state(state)
                .build();
        producerService.send(emailTrxTopic, ObjectUtils.toJson(event));
    }
}
