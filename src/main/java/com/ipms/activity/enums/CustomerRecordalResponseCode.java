package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum CustomerRecordalResponseCode implements Code {
    NOT_FOUND(1000, "error.code.activity.customer_recordal.not_found");
    private final Integer code;
    private final String messageKey;

    CustomerRecordalResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessageKey() {
        return messageKey;
    }
}
