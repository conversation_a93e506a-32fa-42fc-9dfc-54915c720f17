package com.ipms.activity.mapper;

import com.ipms.activity.dto.ComponentCourtDto;
import com.ipms.activity.model.ComponentCourt;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ComponentCourtMapper {
    ComponentCourt toComponentCourt(ComponentCourtDto componentCourtDto);
    ComponentCourtDto toComponentCourtDto(ComponentCourt componentCourt);
    @Mapping(target = "decisionNotificationDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "decisionNumber", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    ComponentCourt toCourtFromDto(ComponentCourtDto dto, @MappingTarget ComponentCourt court);
}
