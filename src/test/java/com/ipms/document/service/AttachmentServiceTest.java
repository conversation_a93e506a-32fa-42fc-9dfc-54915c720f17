package com.ipms.document.service;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.config.storage.service.StorageService;
import com.ipms.document.dto.AttachmentDto;
import com.ipms.document.enums.DocumentType;
import com.ipms.document.mapper.AttachmentMapper;
import com.ipms.document.mapper.AttachmentMapperImpl;
import com.ipms.document.model.Attachment;
import com.ipms.document.model.Document;
import com.ipms.document.repository.AttachmentRepository;
import com.ipms.document.service.impl.AttachmentServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;

@RunWith(SpringRunner.class)
public class AttachmentServiceTest {
    @MockBean
    private AttachmentService service;

    @Mock
    private AttachmentRepository repository;
    @Mock
    private StorageService storageService;
    @Mock
    private DocumentService documentService;

    private final AttachmentMapper mapper = new AttachmentMapperImpl();

    @Before
    public void setUp() {
        service = new AttachmentServiceImpl(repository, storageService, documentService, mapper);
    }

    @Test
    public void whenSave_thenReturnAttachmentDtoWithSignedUrl() {
        var attachmentDto = AttachmentDto.builder()
                .fileName("example.txt")
                .fileUniqueName("123456")
                .contentType("text/plain")
                .documentId(1L)
                .build();

        Document document = Document.builder()
                .type(DocumentType.AGREEMENT)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();

        Mockito.when(documentService.getDocumentById(Mockito.anyLong()))
                .thenReturn(document);

        var attachment = Attachment.builder()
                .fileName("example.txt")
                .fileUniqueName("123456")
                .contentType("text/plain")
                .build();
        Mockito.when(repository.save(Mockito.any(Attachment.class)))
                .thenReturn(attachment);

        Mockito.when(storageService.generateSas(Mockito.any(StorageFile.class)))
                .thenReturn("http://test.com");

        assertThat(service.save(attachmentDto).getFileSignedUrl())
                .isEqualTo("http://test.com");
    }


    @Test
    public void whenDelete_thenThrowDoesNotThrowAnyException() {
        var attachment = Attachment.builder()
                .fileName("example.txt")
                .fileUniqueName("123456")
                .contentType("text/plain")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(attachment));

        assertThatCode(()->service.delete(1L,0L))
                .doesNotThrowAnyException();
    }
}
