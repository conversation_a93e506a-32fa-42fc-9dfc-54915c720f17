package com.ipms.activity.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "suspended_product")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class SuspendedProduct extends VersionedEntity {
    @Column
    private String type;

    @Column
    private Integer number;

    @Column
    private Long customsRecordalMatterId;

    @Column
    private Long activityId;
}
