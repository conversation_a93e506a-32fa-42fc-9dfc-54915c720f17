# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Service Overview

The **ipms-integration** service is part of the IPMS (Intellectual Property Management System) microservices platform. It handles integration between the IPMS system and external JDE (J<PERSON><PERSON><PERSON> Edwards) systems, managing billing order transfers, exchange rates, customer information, and invoice processing.

## Build & Test Commands

### Maven Operations
```bash
# Build the application
mvn clean compile -DskipTests=true

# Run all tests
mvn clean test -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Run single test class
mvn clean test -Dtest=CustomerInformationServiceTest -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Package application
mvn clean package -DskipTests=true
```

### Docker Operations
```bash
# Build Docker image
docker build -t ipms-integration:latest .

# Run container locally
docker run -p 8063:8063 ipms-integration:latest
```

## Architecture & Key Components

### Core Integration Patterns
- **Feign Clients**: External system communication (JDE, IPMS services)
- **Kafka Consumers**: Event-driven billing order processing
- **Scheduled Operations**: Batch processing for order transfers and updates
- **Service-to-Service**: Communication with other IPMS microservices

### External Dependencies
1. **JDE System**: External ERP integration via REST API
   - Order management (send/fetch/validate)
   - Customer information retrieval
   - Exchange rate synchronization
   - Invoice processing

2. **IPMS Services**: Internal microservice communication
   - `ipms-billing`: Billing order data retrieval
   - `ipms-param-command`: Parameter and issuer information

3. **Infrastructure Services**:
   - **Kafka**: Event streaming for order processing
   - **Eureka**: Service discovery (Spring Cloud Netflix)
   - **Spring Cloud Config**: Centralized configuration management

### Core Business Logic

#### Billing Order Integration Flow
1. **Order Creation**: Kafka consumer receives billing order events
2. **Order Sending**: Batch processing sends orders to JDE system
3. **Status Tracking**: Retry mechanism with exponential backoff
4. **External Order Updates**: Fetch JDE order numbers and update IPMS
5. **Order Validation**: Check cancellation status in JDE

#### Key Service Classes
- `BillingOrderIntegrationServiceImpl`: Main integration logic
- `CustomerInformationServiceImpl`: Customer data processing
- `ExchangeRateServiceImpl`: Currency exchange rate management
- `PriceServiceImpl`: Price information handling

## Configuration Management

### Profile-Based Configuration
- **local**: Development environment with file-based config
- **test**: Test environment with H2 database and mock services
- **release**: Pre-production environment
- **prod**: Production environment with external config server

### Environment Variables
```bash
# Required for non-local environments
SPRING_PROFILE=test|release|prod
IPMS_CONFIG_URI=http://ipms-config-api:8888
IPMS_CONFIG_USERNAME=config-user
IPMS_CONFIG_PASSWORD=config-password

# Local development
CONFIG_REPO_PATH=/path/to/config/repository
```

### Key Configuration Properties
- `jde.url`: JDE system endpoint URL
- `integration.minute`: Retry processing interval
- `kafka.*-topic`: Kafka topic names for event processing

### Bill of Materials (BOM) Pattern
The **ipms-bom** module provides centralized dependency management:

#### BOM Import
```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.ipms</groupId>
            <artifactId>ipms-bom</artifactId>
            <version>1.0.0</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

#### Managed Dependencies
- **IPMS Core Libraries**: Entity, Common, i18n, Exception Handling
- **IPMS Config Libraries**: Security, Kafka, Storage, API Documentation  
- **Third-Party Libraries**: MapStruct, Lombok, Commons utilities

#### Benefits
- **Version Consistency**: Single source of truth for all dependency versions
- **Simplified Maintenance**: Update versions in one place
- **Conflict Resolution**: Prevents version conflicts between services
- **Developer Experience**: No need to specify versions for managed dependencies

## Testing Strategy

### Test Structure
- **Unit Tests**: Service layer testing with Mockito
- **Integration Tests**: End-to-end testing with test containers
- **Test Database**: H2 in-memory database for isolated testing

### Test Patterns
- Mock external Feign clients (JdeClient, BillingClient)
- Use `@ExtendWith(MockitoExtension.class)` for unit tests
- Builder pattern for test data creation
- AssertJ for fluent assertions

## Data Model

### Core Entity
- `BillingOrderIntegration`: Tracks order transfer status and retry attempts
  - `orderId`: Reference to IPMS billing order
  - `status`: Transfer status (PENDING, SENT, UPDATED, ERROR, FAILED)
  - `retryCount`: Number of retry attempts (max 5)

### Transfer Status Flow
```
PENDING → SENT → UPDATED
    ↓       ↓
   ERROR → RETRY → FAILED (after 5 retries)
```

## Integration Protocols

### JDE API Integration
- **Authentication**: Query parameter based
- **Data Format**: JSON request/response
- **Error Handling**: Error codes in response (0 = success)
- **Retry Logic**: Exponential backoff with max 5 retries

### IPMS Service Communication
- **Protocol**: OpenFeign with service discovery
- **Security**: OAuth2 JWT tokens via Keycloak
- **Load Balancing**: Eureka client-side load balancing

## Development Guidelines

### Code Patterns
- Use `@Slf4j` for logging instead of System.out
- Implement null-safe operations with Optional
- Use builder pattern for complex DTOs
- Follow reactive programming principles where applicable

### Error Handling
- Wrap external API calls in try-catch blocks
- Log full exception stack traces for debugging
- Publish error events to Kafka for downstream processing
- Use exponential backoff for transient failures

### Performance Considerations
- Batch process orders to reduce API calls
- Use parallel processing for independent operations
- Implement caching for frequently accessed reference data
- Monitor integration performance with actuator endpoints

## Service Dependencies

### Runtime Dependencies
- **PostgreSQL**: Primary database for persistence
- **Kafka**: Event streaming platform
- **Eureka Server**: Service discovery
- **Keycloak**: Authentication and authorization

### Development Dependencies
- **H2 Database**: In-memory testing database
- **Mockito**: Mocking framework for unit tests
- **AssertJ**: Fluent assertion library

## Monitoring & Observability

### Health Checks
- Spring Boot Actuator endpoints at `/actuator/health`
- Database connectivity monitoring
- Kafka consumer lag monitoring

### Metrics
- Prometheus metrics export via Micrometer
- Custom metrics for integration success/failure rates
- Processing time metrics for order transfers

## Common Issues & Solutions

### JDE Integration Issues
- **Connection Timeouts**: Increase Feign client timeout settings
- **Authentication Failures**: Verify JDE endpoint credentials
- **Data Format Errors**: Validate request payload structure

### Kafka Processing Issues
- **Consumer Lag**: Scale consumer instances or optimize processing
- **Deserialization Errors**: Ensure message format compatibility
- **Dead Letter Queues**: Configure error handling for failed messages

### Database Issues
- **Connection Pool Exhaustion**: Tune HikariCP settings
- **Lock Contention**: Optimize retry logic timing
- **Migration Failures**: Use Flyway for schema versioning