package com.ipms.activity.validator;

import com.ipms.activity.client.BillingClient;
import com.ipms.activity.dto.TSICPackageDto;
import com.ipms.activity.dto.TimesheetDto;
import com.ipms.activity.dto.TimesheetResponse;
import com.ipms.activity.enums.TSICPackageResponseCode;
import com.ipms.activity.exception.TSICPackageValidationException;
import com.ipms.activity.model.TSICPackage;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchThrowableOfType;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TSICPackageValidatorTest {

    @Mock
    private BillingClient billingClient;

    @InjectMocks
    private TSICPackageValidator validator;

    private TSICPackageDto newPackageDto;
    private TSICPackage existingPackage;
    private TSICPackage lastPackage;
    private Long activityId;

    @Before
    public void setup() {
        activityId = 100L;

        newPackageDto = TSICPackageDto.builder()
                .startDate(LocalDate.of(2023, 4, 1))
                .endDate(LocalDate.of(2023, 6, 30))
                .includedCharge(BigDecimal.valueOf(100))
                .fixedFee(true)
                .invoiced(false)
                .build();

        existingPackage = TSICPackage.builder()
                .startDate(LocalDate.of(2023, 4, 1))
                .endDate(LocalDate.of(2023, 6, 30))
                .includedCharge(BigDecimal.valueOf(100))
                .fixedFee(true)
                .invoiced(false)
                .build();

        lastPackage = TSICPackage.builder()
                .startDate(LocalDate.of(2023, 1, 1))
                .endDate(LocalDate.of(2023, 3, 31))
                .includedCharge(BigDecimal.valueOf(50))
                .fixedFee(true)
                .invoiced(false)
                .build();
    }

    @Test
    public void givenValidPackageWithEndDate_whenValidateCreate_thenNoExceptionThrown() {
        Optional<TSICPackage> lastPackageOpt = Optional.of(lastPackage);
        mockBillingClientWithEmptyTimesheetResponse();

        validator.validateCreate(newPackageDto, lastPackageOpt, activityId);
    }

    @Test
    public void givenValidPackageWithoutEndDate_whenValidateCreate_thenNoExceptionThrown() {
        newPackageDto.setEndDate(null);
        Optional<TSICPackage> lastPackageOpt = Optional.of(lastPackage);
        mockBillingClientWithEmptyTimesheetResponse();

        validator.validateCreate(newPackageDto, lastPackageOpt, activityId);
    }

    @Test
    public void givenEndDateBeforeStartDate_whenValidateCreate_thenThrowsException() {
        newPackageDto.setEndDate(LocalDate.of(2023, 3, 15));
        Optional<TSICPackage> lastPackageOpt = Optional.of(lastPackage);

        TSICPackageValidationException exception = catchThrowableOfType(
                () -> validator.validateCreate(newPackageDto, lastPackageOpt, activityId),
                TSICPackageValidationException.class);

        assertThat(exception.getCode())
                .isEqualTo(TSICPackageResponseCode.START_DATE_MUST_NOT_BE_AFTER_THEN_END_DATE);
        verifyNoInteractions(billingClient);
    }

    @Test
    public void givenStartDateBeforeLastPackageEndDate_whenValidateCreate_thenThrowsException() {
        newPackageDto.setStartDate(LocalDate.of(2023, 3, 15));
        Optional<TSICPackage> lastPackageOpt = Optional.of(lastPackage);

        TSICPackageValidationException exception = catchThrowableOfType(
                () -> validator.validateCreate(newPackageDto, lastPackageOpt, activityId),
                TSICPackageValidationException.class);

        assertThat(exception.getCode())
                .isEqualTo(TSICPackageResponseCode.START_DATE_MUST_BE_AFTER_THEN_LAST_PACKAGE_END_DATE);
        verifyNoInteractions(billingClient);
    }

    @Test
    public void givenStartDateBeforeLastPackageStartDate_whenValidateCreate_thenThrowsException() {
        lastPackage.setEndDate(null);
        newPackageDto.setStartDate(LocalDate.of(2022, 12, 15));
        Optional<TSICPackage> lastPackageOpt = Optional.of(lastPackage);

        TSICPackageValidationException exception = catchThrowableOfType(
                () -> validator.validateCreate(newPackageDto, lastPackageOpt, activityId),
                TSICPackageValidationException.class);

        assertThat(exception.getCode())
                .isEqualTo(TSICPackageResponseCode.START_DATE_MUST_BE_AFTER_THEN_LAST_PACKAGE_START_DATE);
        verifyNoInteractions(billingClient);
    }

    @Test
    public void givenStartDate_whenHasUnbilledTimesheetBeforeStart_thenThrowsException() {
        var timesheetResponse = TimesheetResponse.builder()
                .payload(List.of(TimesheetDto.builder()
                                .date(LocalDate.of(2023, 3, 15))
                        .build()))
                .build();
        Optional<TSICPackage> lastPackageOpt = Optional.of(lastPackage);

        when(billingClient.getUnbilledTimesheetsByActivityId(activityId)).thenReturn(timesheetResponse);

        TSICPackageValidationException exception = catchThrowableOfType(
                () -> validator.validateCreate(newPackageDto, lastPackageOpt, activityId),
                TSICPackageValidationException.class);
        assertThat(exception.getCode())
                .isEqualTo(TSICPackageResponseCode.UNBILLED_TIMESHEET_EXIST_BEFORE_START_DATE_WHEN_ADDING_NEW_PACKAGE);
    }

    @Test
    public void givenStartDateAndHasUnbilledTimesheetBeforeStartDate_whenValidateUpdate_thenThrowException() {
        var timesheetResponse = TimesheetResponse.builder()
                .payload(List.of(TimesheetDto.builder()
                                .date(LocalDate.of(2023, 4, 1))
                        .build()))
                .build();
        newPackageDto.setStartDate(LocalDate.of(2023, 4, 15));
        when(billingClient.getUnbilledTimesheetsByActivityId(activityId)).thenReturn(timesheetResponse);

        TSICPackageValidationException exception = catchThrowableOfType(
                () -> validator.validateUpdate(newPackageDto, existingPackage, Optional.empty(), activityId),
                TSICPackageValidationException.class);
        assertThat(exception.getCode())
                .isEqualTo(TSICPackageResponseCode.UNBILLED_TIMESHEET_EXIST_BEFORE_START_DATE_WHEN_UPDATING_PACKAGE);
    }

    @Test
    public void givenEndDateAndHasUnbilledTimesheetBetweenStartDateAndEndDate_whenValidateUpdate_thenThrowException() {
        var timesheetResponse = TimesheetResponse.builder()
                .payload(List.of(TimesheetDto.builder()
                        .date(LocalDate.of(2023, 7, 1))
                        .build()))
                .build();
        newPackageDto.setEndDate(LocalDate.of(2023, 7, 15));
        when(billingClient.getUnbilledTimesheetsByActivityId(activityId)).thenReturn(timesheetResponse);

        TSICPackageValidationException exception = catchThrowableOfType(
                () -> validator.validateUpdate(newPackageDto, existingPackage, Optional.empty(), activityId),
                TSICPackageValidationException.class);
        assertThat(exception.getCode())
                .isEqualTo(TSICPackageResponseCode.UNBILLED_TIMESHEET_EXIST_BETWEEN_START_AND_END_DATE_WHEN_UPDATING_PACKAGE);
    }

    private void mockBillingClientWithEmptyTimesheetResponse() {
        var timesheetResponse = TimesheetResponse.builder()
                .payload(List.of())
                .build();
        when(billingClient.getUnbilledTimesheetsByActivityId(activityId)).thenReturn(timesheetResponse);
    }
}