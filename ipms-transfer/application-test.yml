eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:test-ipms-transfer}.test-ipms-transfer
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  transfer-topic: dataTransferTopic
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic

spring:
  datasource:
    url: *********************************************************************************
    username: ipms_test_user
    password: he9JNxwRb3GxZrAb
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-default/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web
  url: https://test-ipms.deris.com/internal-module/

keycloak:
  realm: ipms-default
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn


legacy:
  uri: http://**************:8045/
  user: acc_deris_root
  password: pQL7.4\@*mgr?ZCa%

transfer:
  minute:
    activity: 10
    firm: 10
    issue: 10
    matter: 10
    timesheet: 10
    document: 10
    disbursement: 10
    expense: 10
    deadline: 10