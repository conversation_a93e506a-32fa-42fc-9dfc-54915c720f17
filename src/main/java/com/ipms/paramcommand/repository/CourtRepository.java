package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.Court;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CourtRepository extends PagingAndSortingRepository<Court, Long>, JpaSpecificationExecutor<Court> {
}
