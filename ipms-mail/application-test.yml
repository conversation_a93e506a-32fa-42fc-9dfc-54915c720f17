eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:test-ipms-mail}.test-ipms-mail
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  email-topic: emailTopic
  retry-policy:
    max-attempts: 5
  email-trx-topic: emailTrxTopic
  reply-status-topic: replyStatusTopic
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  invoice-is-ready-topic: invoiceIsReadyTopic

spring:
  datasource:
    url: *********************************************************************************
    username: ipms_test_user
    password: he9JNxwRb3GxZrAb
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ********************************
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-default/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-default
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ********************************

azure:
  mail:
    clientId: 7cb35efd-5c98-4286-af82-de81b804e342
    clientSecret: ****************************************
    tenantId: ce77442e-69e4-4367-939d-88d85cabf43e
    authTenant: common
    graphUserScopes: user.read,mail.read,mail.send
    scopes:
      - https://graph.microsoft.com/.default
    default-folder-addresses:
      - <EMAIL>
    ipms-folder-addresses:
      - <EMAIL>
    period: 65
    default-folder: inbox
    ipms-folder: load-ipms
    ipms-document-folder: load-ipms-for-document
    categories:
      - IPMS
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=ipmstestsa;AccountKey=****************************************************************************************;
    container-name: ipmstestcontainer
    sas-host-url: https://ipmstestsa.blob.core.windows.net/ipmstestcontainer/