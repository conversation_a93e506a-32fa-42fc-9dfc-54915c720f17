package com.ipms.document.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.document.enums.*;
import com.ipms.document.model.Sample;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.*;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class SampleSpecification extends AbstractSpecification<Sample> {
    private final List<String> sampleTypes;
    private final List<String> collectionPlaces;
    private final List<String> sampleLocations;
    private final Long firmId;

    @Override
    public Predicate toPredicate(Root<Sample> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        Join<Sample, Long> firmsJoin = root.join("firms");
        var predicate = super.toPredicate(root, query, cb);
        Optional.ofNullable(firmId)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(firmsJoin.in(value))));
        Optional.ofNullable(sampleTypes)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("sampleType").in(sampleTypes.stream().map(SampleType::valueOf).toList()))));
        Optional.ofNullable(collectionPlaces)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("collectionPlace").in(collectionPlaces.stream().map(SampleCollectionPlace::valueOf).toList()))));
        Optional.ofNullable(sampleLocations)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("sampleLocation").in(sampleLocations.stream().map(SampleLocation::valueOf).toList()))));
        query.distinct(true);
        return predicate;
    }
}
