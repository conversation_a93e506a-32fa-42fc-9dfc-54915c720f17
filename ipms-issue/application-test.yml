eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:test-ipms-issue}.test-ipms-issue
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  correspondence-reply-date-topic: correspondenceReplyDueDateTopic
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  issue-linked-topic: issueLinkedTopic
  correspondence-updated-topic: correspondenceUpdatedTopic
  convert-issue-rollback-topic: convertIssueRollbackTopic
  close-issue-topic: closeIssueTopic
  issue-update-relational-topic: issueUpdateRelationalTopic
  issue-create-from-deadline-topic: issueCreateFromDeadlineTopic
  billing-issue-closed-topic: billingIssueClosedTopic

spring:
  datasource:
    url: *********************************************************************************
    username: ipms_test_user
    password: he9JNxwRb3GxZrAb
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-default/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-default
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn