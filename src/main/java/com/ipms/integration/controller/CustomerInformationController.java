package com.ipms.integration.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.integration.dto.CustomerInformation;
import com.ipms.integration.service.CustomerInformationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/customer-information")
@Tag(name = "customer information", description = "This endpoint contains exchange rate APIs ")
public class CustomerInformationController {

    private final CustomerInformationService service;

    @GetMapping("/{billingAccountNo}")
    public BaseResponse<CustomerInformation> fetchCustomerInformation(@PathVariable String billingAccountNo) {
        return BaseResponse.<CustomerInformation>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getCustomerInformation(billingAccountNo))
                .build();
    }
}
