package com.ipms.activity.service;

import com.ipms.activity.dto.PreliminaryInjunctionDetailDto;
import com.ipms.activity.dto.PreliminaryInjunctionDto;
import com.ipms.activity.model.PreliminaryInjunction;
import com.ipms.activity.model.PreliminaryInjunctionDetail;

import java.util.List;

public interface PreliminaryInjunctionDetailService {
    PreliminaryInjunctionDetailDto getById(Long id);

    List<PreliminaryInjunctionDetail> saveFromPreliminaryInjunction(PreliminaryInjunctionDto preliminaryInjunctionDto, PreliminaryInjunction preliminaryInjunction);

    void saveDetails(List<PreliminaryInjunctionDetailDto> details, PreliminaryInjunction preliminaryInjunction);
}
