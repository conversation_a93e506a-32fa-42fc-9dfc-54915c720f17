package com.ipms.user.service.keycloak;

import com.ipms.user.dto.keycloak.KeycloakTokenDto;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;

import java.util.List;


public interface KeycloakAuthService {

    KeycloakTokenDto getRefreshToken(String refreshToken, String realm, String clientId);

    KeycloakTokenDto getAccessToken(String username, String password, String realm, String clientId, String otp);

    void logout(String accessToken, String refreshToken, String realm, String clientId);

    KeycloakTokenDto getPermissionToken(String accessToken, String realm, String clientId);

    KeycloakTokenDto getAccessToken(String clientId);

    List<UserInfoDto> getUserList(String accessToken, String realm);

    List<RoleDto> getRoleList(String realm, String clientId, String accessToken);

    List<UserInfoDto> getUserListByRole(String realm, String clientId, String role, String accessToken);

    UserInfoDto filterExpenseCode(String accessToken, String realm, String username, List<String> expenseCodes);
}
