package com.ipms.document.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.document.dto.*;
import com.ipms.document.service.SampleService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-document/application.yml")
public class SampleControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private SampleService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var sampleDto = SampleDto
                .builder()
                .id(1L)
                .version(0L)
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        var sampleManuelDto = SampleManualDto
                .builder()
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .activityId(1L)
                .build();
        Mockito.when(service.save(Mockito.any(SampleManualDto.class)))
                .thenReturn(sampleDto);

        mvc.perform(post("/sample")
                        .content(objectMapper.writeValueAsString(sampleManuelDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {
        var sampleDto = SampleDto
                .builder()
                .id(1L)
                .version(0L)
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        Mockito.when(service.update(Mockito.any(SampleDto.class), Mockito.anyLong()))
                .thenReturn(sampleDto);

        mvc.perform(put("/sample/1")
                        .content(objectMapper.writeValueAsString(sampleDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());

        mvc.perform(delete("/sample/1/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGet_thenReturnBaseResponse() throws Exception {
        var sampleDto = SampleDto
                .builder()
                .id(1L)
                .version(0L)
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        Mockito.when(service.getById(Mockito.anyLong()))
                .thenReturn(sampleDto);

        mvc.perform(get("/sample/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByIds_thenReturnBaseResponse() throws Exception {
        var sampleDto = SampleDto
                .builder()
                .id(1L)
                .version(0L)
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        Mockito.when(service.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(sampleDto));

        mvc.perform(get("/sample/ids/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByFirm_thenReturnBaseResponse() throws Exception {
        var sampleDto = SampleDto
                .builder()
                .id(1L)
                .version(0L)
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        var evidencePageDto = SamplePageDto.builder()
                .samples(List.of(sampleDto))
                .totalElements(10)
                .totalPages(2L)
                .build();
        Mockito.when(service.getAll(Mockito.any(SampleFilterRequest.class), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(evidencePageDto);

        mvc.perform(get("/sample/firm/1/0/10")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetAll_thenReturnBaseResponse() throws Exception {
        var sampleDto = SampleDto
                .builder()
                .id(1L)
                .version(0L)
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        var evidencePageDto = SamplePageDto.builder()
                .samples(List.of(sampleDto))
                .totalElements(10)
                .totalPages(2L)
                .build();
        Mockito.when(service.getAll(Mockito.any(SampleFilterRequest.class), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(evidencePageDto);

        mvc.perform(get("/sample/samples/0/10")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
