package com.ipms.activity.service.impl;

import com.ipms.activity.dto.ComponentCourtDto;
import com.ipms.activity.dto.ComponentCourtFilterRequest;
import com.ipms.activity.exception.ComponentCourtNotFoundException;
import com.ipms.activity.mapper.ComponentCourtMapper;
import com.ipms.activity.model.ComponentCourt;
import com.ipms.activity.repository.ComponentCourtRepository;
import com.ipms.activity.service.ComponentCourtService;
import com.ipms.activity.specification.ComponentCourtSpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ComponentCourtServiceImpl implements ComponentCourtService {
    private final ComponentCourtRepository repository;
    private final ComponentCourtMapper mapper;

    @Override
    public ComponentCourtDto save(ComponentCourtDto componentCourtDto) {
        var court = mapper.toComponentCourt(componentCourtDto);
        var saved = repository.save(court);
        return mapper.toComponentCourtDto(saved);
    }

    @Override
    public ComponentCourtDto update(ComponentCourtDto componentCourtDto, Long id) {
        var court = mapper.toCourtFromDto(componentCourtDto, getComponentCourtById(id));
        var updated = repository.save(court);
        return mapper.toComponentCourtDto(updated);
    }

    @Override
    public List<ComponentCourtDto> getByActivityId(Long activityId) {
        return repository.findByActivityId(activityId)
                .stream()
                .map(mapper::toComponentCourtDto)
                .sorted(Comparator.comparing(ComponentCourtDto::getId))
                .toList();
    }

    @Override
    public List<ComponentCourtDto> getByActivityIds(List<Long> activityIds) {
        return repository.findByActivityIdIn(activityIds)
                .stream()
                .map(mapper::toComponentCourtDto)
                .toList();
    }

    @Override
    public ComponentCourt getByDocketNo(String docketNo) {
        return repository.findFirstByDocketNo(docketNo);
    }

    @Override
    public void delete(Long id, Long version) {
        var court = getComponentCourtById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(court);
    }

    @Override
    public List<ComponentCourtDto> getAllFiltered(ComponentCourtFilterRequest componentCourtFilterRequest) {
        var componentCourts = repository.findAll(ComponentCourtSpecification.builder()
                .decisionNumbers(componentCourtFilterRequest.getDecisionNumbers())
                .filingDates(componentCourtFilterRequest.getFilingDates())
                .docketNos(componentCourtFilterRequest.getDocketNos())
                .build());
        return componentCourts.stream().map(this::toDto).toList();
    }

    private ComponentCourt getComponentCourtById(Long id) {
        return repository.findById(id)
                .orElseThrow(ComponentCourtNotFoundException::new);
    }

    private ComponentCourtDto toDto(ComponentCourt componentCourt) {
        return mapper.toComponentCourtDto(componentCourt);
    }
}
