package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.dto.LawyerDto;
import com.ipms.activity.service.LawyerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class LawyerControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private LawyerService service;

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void givenDtoAndId_whenUpdate_thenReturnBaseResponse() throws Exception {
        var lawyerDto = LawyerDto.builder().id(1L).version(1L).name("test-name").type("test-type").build();
        Mockito.when(service.update(Mockito.any(LawyerDto.class), Mockito.anyLong()))
                .thenReturn(lawyerDto);
        mvc.perform(put("/lawyer/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(lawyerDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());
        mvc.perform(delete("/lawyer/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenLawyerDto_whenAddLawyer_thenReturnBaseResponse() throws Exception {
        var agency = AgencyDto.builder().email("<EMAIL>").city("tr").name("test-name").build();
        var lawyer = LawyerDto.builder().id(1L).name("test-name").type("test-type").agency(agency).build();

        Mockito.when(service.addToCounterRightOwnerCard(Mockito.any(LawyerDto.class), Mockito.anyLong()))
                .thenReturn(lawyer);
        mvc.perform(post("/lawyer/add-to-related-card/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(lawyer))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByCounterRightOwnerCardId_thenReturnBaseResponse() throws Exception {
        var lawyer = LawyerDto.builder()
                .id(1L).name("test-name")
                .type("test-type")
                .build();

        Mockito.when(service.getByCounterRightOwnerCardId(Mockito.anyLong()))
                .thenReturn(List.of(lawyer));
        mvc.perform(get("/lawyer/by-related-card-id/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(lawyer))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
