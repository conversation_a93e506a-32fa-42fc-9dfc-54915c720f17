package com.ipms.paramcommand.service;

import com.ipms.paramcommand.mapper.ExchangeRateMapper;
import com.ipms.paramcommand.mapper.ExchangeRateMapperImpl;
import com.ipms.paramcommand.model.ExchangeRate;
import com.ipms.paramcommand.repository.ExchangeRateRepository;
import com.ipms.paramcommand.service.impl.ExchangeRateServiceImpl;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

@RunWith(SpringRunner.class)
public class ExchangeRateServiceTest {

    @MockBean
    private ExchangeRateService service;

    @Mock
    private ExchangeRateRepository repository;

    private final ExchangeRateMapper mapper = new ExchangeRateMapperImpl();

    @Before
    public void setUp() {
        service = new ExchangeRateServiceImpl(repository, mapper);
    }

    @Test
    public void testSave() {
        ExchangeRate exchangeRate = new ExchangeRate();
        Mockito.when(repository.save(exchangeRate)).thenReturn(exchangeRate);

        var saved = service.save(exchangeRate);

        Assertions.assertThat(saved).isNotNull();
    }

    @Test
    public void testGetLatest() {
        String fromCurrencyCode = "USD";
        String toCurrencyCode = "IDR";
        LocalDate currencyDate = LocalDate.now();
        var exchangeRate = ExchangeRate.builder()
                .currencyDate(currencyDate)
                .fromCurrencyCode(fromCurrencyCode)
                .toCurrencyCode(toCurrencyCode)
                .rate(BigDecimal.ONE)
                .dateOfUpdate(LocalDate.now())
                .build();
        Mockito.when(repository.findTopByFromCurrencyCodeAndToCurrencyCodeAndCurrencyDateLessThanEqualOrderByCurrencyDateDesc(
                fromCurrencyCode, toCurrencyCode, currencyDate)).thenReturn(Optional.of(exchangeRate));

        var exchangeRateResponseDto = service.getLatest(fromCurrencyCode, toCurrencyCode, currencyDate);

        Assertions.assertThat(exchangeRateResponseDto).isNotNull();
    }
}