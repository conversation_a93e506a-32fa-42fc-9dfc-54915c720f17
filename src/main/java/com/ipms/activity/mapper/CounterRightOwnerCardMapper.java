package com.ipms.activity.mapper;

import com.ipms.activity.dto.CounterRightOwnerCardDto;
import com.ipms.activity.model.CounterRightOwnerCard;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", uses = {LawyerMapper.class, AgencyMapper.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CounterRightOwnerCardMapper {
    CounterRightOwnerCard toCounterRightOwnerCard(CounterRightOwnerCardDto counterRightOwnerCardDto);
    @Mapping(target = "counterRightOwner", ignore = true)
    CounterRightOwnerCard toCounterRightOwnerCard(CounterRightOwnerCardDto counterRightOwnerCardDto,
                                                  @MappingTarget CounterRightOwnerCard counterRightOwnerCard);
    CounterRightOwnerCardDto toCounterRightOwnerCardDto(CounterRightOwnerCard counterRightOwnerCard);
}
