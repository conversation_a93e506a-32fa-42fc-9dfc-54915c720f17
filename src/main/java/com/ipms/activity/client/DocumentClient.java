package com.ipms.activity.client;

import com.ipms.activity.dto.DocumentFilterRequest;
import com.ipms.activity.dto.DocumentSummaryResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "ipms-document")
public interface DocumentClient {
    @GetMapping("/api/document/summaries")
    DocumentSummaryResponse getAllDS(@SpringQueryMap DocumentFilterRequest request);
}
