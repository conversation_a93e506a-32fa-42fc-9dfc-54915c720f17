package com.ipms.document.service;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.config.storage.service.StorageService;
import com.ipms.document.client.ActivityClient;
import com.ipms.document.dto.*;
import com.ipms.document.enums.SampleCollectionPlace;
import com.ipms.document.enums.SampleLocation;
import com.ipms.document.enums.SampleType;
import com.ipms.document.exception.SampleCannotRemoveException;
import com.ipms.document.exception.SampleNotFoundException;
import com.ipms.document.mapper.SampleMapper;
import com.ipms.document.mapper.SampleMapperImpl;
import com.ipms.document.model.Sample;
import com.ipms.document.model.SampleAttachment;
import com.ipms.document.repository.SampleRepository;
import com.ipms.document.service.impl.SampleServiceImpl;
import com.ipms.document.specification.SampleSpecification;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;

@RunWith(SpringRunner.class)
public class SampleServiceTest {

    @MockBean
    private SampleService service;

    @Mock
    private StorageService storageService;

    @Mock
    private SampleRepository repository;

    @Mock
    private ActivityClient activityClient;

    private final SampleMapper mapper = new SampleMapperImpl();

    @Before
    public void setUp() {
        service = new SampleServiceImpl(repository, mapper, activityClient, storageService);
    }

    @Test
    public void whenSave_thenReturnSampleDto() {
        var sample = Sample.builder()
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();

        var sampleManuelDto = SampleManualDto
                .builder()
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .activityId(1L)
                .build();
        Mockito.when(repository.save(Mockito.any(Sample.class)))
                .thenReturn(sample);
        Mockito.when(activityClient.addSample(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(ActivityResponse.builder()
                        .code(1)
                        .payload(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build())
                        .build());
        assertThat(service.save(sampleManuelDto).getQuantity())
                .isEqualTo(3L);
    }

    @Test
    public void whenUpdate_thenReturnSampleDto() {
        var sample = Sample.builder()
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .name("test-name")
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();

        var sampleDto = SampleDto
                .builder()
                .id(1L)
                .version(0L)
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .name("test-name")
                .description("test-description")
                .sampleType("TEXTILE")
                .collectionPlace("CLIENT")
                .quantity(3L)
                .sampleLocation("CLIENT")
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(sample));
        Mockito.when(repository.save(Mockito.any(Sample.class)))
                .thenReturn(sample);
        Mockito.when(storageService.generateSas(Mockito.any(StorageFile.class)))
                .thenReturn("http://test.com");
        assertThat(service.update(sampleDto, 1L).getAttachments().get(0).getFileSignedUrl())
                .isEqualTo("http://test.com");
    }

    @Test
    public void givenLinkedSample_whenDelete_thenThrowSampleCannotRemoveException() {
        Mockito.when(activityClient.checkSampleLinked(Mockito.anyLong()))
                .thenReturn(ActivityLinkedResponse.builder()
                        .payload(List.of(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build()))
                        .build());
        Throwable thrown = catchThrowable(()->service.delete(1L,0L));
        assertThat(thrown).isInstanceOf(SampleCannotRemoveException.class);
    }

    @Test
    public void whenDelete_thenThrowDoesNotThrowAnyException() {
        Mockito.when(activityClient.checkSampleLinked(Mockito.anyLong()))
                .thenReturn(ActivityLinkedResponse.builder()
                        .payload(List.of())
                        .build());
        var sample = Sample.builder()
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .name("test-name")
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(sample));
        assertThatCode(()->service.delete(1L,0L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenId_whenGetById_thenReturnSampleDto() {
        var sample = Sample.builder()
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .name("test-name")
                .id(1L)
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(sample));
        assertThat(service.getById(1L).getId()).isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetSampleById_thenReturnSample() {
        var sample = Sample.builder()
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .name("test-name")
                .id(1L)
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(sample));
        assertThat(service.getSampleById(1L).getId()).isEqualTo(1L);
    }

    @Test
    public void whenGetById_thenThrowDocumentNotFoundException() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(()->service.getSampleById(1L));
        assertThat(thrown).isInstanceOf(SampleNotFoundException.class);
    }

    @Test
    public void whenGetByIdIn_thenReturnDocumentDtoList() {
        var sample = Sample.builder()
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .name("test-name")
                .id(1L)
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        Mockito.when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(sample));
        assertThat(service.getByIdIn(List.of(1L))).isNotEmpty();
    }

    @Test
    public void whenGetAll_thenReturnEvidencePageDto() {
        var sample = Sample.builder()
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .attachments(new ArrayList<>(Arrays.asList(
                        SampleAttachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .name("test-name")
                .id(1L)
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        var evidenceRequest = SampleFilterRequest.builder()
                .firmId(1L)
                .ids(List.of(1L))
                .build();

        Page<Sample> samplePage = new PageImpl<>(List.of(sample), Pageable.ofSize(1), 1);
        Mockito.when(repository.findAll(Mockito.any(SampleSpecification.class), Mockito.any(Pageable.class)))
                .thenReturn(samplePage);
        AssertionsForClassTypes.assertThat(service.getAll(evidenceRequest, 1, 1).getSamples().get(0).getId())
                .isEqualTo(1L);
    }
}
