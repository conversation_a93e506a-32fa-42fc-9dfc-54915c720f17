package com.ipms.activity.service.impl;

import com.ipms.activity.dto.CustomsSuspensionsReceiverDto;
import com.ipms.activity.mapper.CustomsSuspensionReceiverMapper;
import com.ipms.activity.repository.CustomSuspensionsReceiverRepository;
import com.ipms.activity.service.CustomsSuspensionsReceiverService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class CustomsSuspensionsReceiverServiceImpl implements CustomsSuspensionsReceiverService {
    private final CustomSuspensionsReceiverRepository repository;
    private final CustomsSuspensionReceiverMapper mapper;

    @Override
    public List<CustomsSuspensionsReceiverDto> findByName(String name) {
        var receivers = repository.findAllByNameContainingIgnoreCase(name);
        List<CustomsSuspensionsReceiverDto> dtos = new ArrayList<>();
        receivers.forEach(receiver -> dtos.add(mapper.toDto(receiver)));
        return dtos;
    }
}
