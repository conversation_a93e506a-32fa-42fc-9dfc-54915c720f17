package com.ipms.document.specification;

import com.ipms.document.enums.EvidenceType;
import com.ipms.document.enums.MediaType;
import com.ipms.document.model.Evidence;
import com.ipms.document.repository.EvidenceRepository;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-document/application.yml")
public class EvidenceSpecificationIntegrationTest {

    @Autowired
    private EvidenceRepository repository;

    @Before
    public void givenEvidence() {
        var evidence1 = Evidence.builder()
                .id(1L)
                .name("test-name")
                .description("test-description")
                .firms(List.of(1L, 2L))
                .mediaType(MediaType.DRAWING)
                .evidenceType(EvidenceType.AGREEMENT)
                .build();
        repository.save(evidence1);

        var evidence2 = Evidence.builder()
                .id(2L)
                .name("test-name2")
                .description("test-description2")
                .firms(List.of(1L, 2L))
                .mediaType(MediaType.EXCEL)
                .evidenceType(EvidenceType.ADVERTISEMENT)
                .build();
        repository.save(evidence2);
    }

    @After
    public void deleteAll() {
        repository.deleteAll();
    }

    @Test
    public void whenFindAll_thenReturnEvidenceList() {
        Page<Evidence> evidencePage = repository.findAll(EvidenceSpecification.builder()
                .createdBy("system")
                .updatedBy("system")
                .ids(List.of(1L, 2L))
                .firmId(1L)
                .evidenceTypes(List.of("AGREEMENT"))
                .mediaTypes(List.of("DRAWING"))
                .build(), PageRequest.of(0, 10));
        Predicate<Page<Evidence>> predicate = page -> page.hasContent() && page.getTotalElements() == 1
                && page.getTotalPages() == 1
                && page.getContent().get(0).getName().equals("test-name");

        AssertionsForClassTypes.assertThat(evidencePage)
                .matches(predicate, "list size must be 1 and evidence id 1");
    }

    @Test
    public void givenSortField_whenFindAll_thenReturnSortedEvidenceList() {
        Page<Evidence> issuePage = repository.findAll(EvidenceSpecification.builder()
                .createdBy("system")
                .updatedBy("system")
                .ids(List.of(1L, 2L))
                .evidenceTypes(List.of("AGREEMENT"))
                .mediaTypes(List.of("DRAWING"))
                .build(), PageRequest.of(0, 10));
        assertThat(issuePage.getContent())
                .isSortedAccordingTo(Comparator.comparing(Evidence::getId).reversed());
    }
}
