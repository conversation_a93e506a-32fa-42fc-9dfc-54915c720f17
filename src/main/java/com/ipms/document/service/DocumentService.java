package com.ipms.document.service;

import com.ipms.document.dto.*;
import com.ipms.document.model.Document;

import java.util.List;

public interface DocumentService {
    DocumentDto save(DocumentCreateDto documentCreateDto);
    DocumentDto save(DocumentManualDto documentManualDto);
    DocumentDto update(DocumentDto documentDto, Long id);
    DocumentDto getById(Long id);
    Document getDocumentById(Long id);
    List<DocumentDto> getByIdIn(List<Long> ids);
    DocumentDto getByFileUniqueName(String fileUniqueName);
    List<DocumentDto> getByFileUniqueName(List<String> fileUniqueNames);
    void delete(Long id, Long version);

    DocumentPageDto getAll(DocumentFilterRequest request, int page, int size);
    List<DocumentSummaryDto> getDocumentSummaries(DocumentFilterRequest request);

    void processUpdatedEvent(DocumentEvent event);
}
