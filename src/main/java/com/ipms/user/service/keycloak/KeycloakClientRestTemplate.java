package com.ipms.user.service.keycloak;

import com.ipms.user.dto.keycloak.KeycloakAuthRequestDto;
import com.ipms.user.dto.keycloak.KeycloakLogoutRequestDto;
import com.ipms.user.dto.keycloak.KeycloakTokenDto;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class KeycloakClientRestTemplate {

    @Value("${keycloak.auth-server-url}")
    private String keycloakBaseUrl;

    private final RestTemplate restTemplate;

    public KeycloakTokenDto getPermissionToken(KeycloakAuthRequestDto request, String realm, String bearerToken) {
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("grant_type", request.getGrantType());
        body.add("client_id", request.getClientId());
        body.add("client_secret", request.getClientSecret());
        body.add("audience", request.getAudience());

        ResponseEntity<KeycloakTokenDto> responseEntity = restTemplate.exchange(
                keycloakBaseUrl.concat("/realms/").concat(realm).concat("/protocol/openid-connect/token"),
                HttpMethod.POST,
                new HttpEntity<>(body, getHttpHeaders(bearerToken)),
                KeycloakTokenDto.class
        );

        return responseEntity.getBody();
    }

    public KeycloakTokenDto logout(KeycloakLogoutRequestDto request, String realm, String bearerToken) {
        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("client_id", request.getClientId());
        body.add("client_secret", request.getClientSecret());
        body.add("refresh_token", request.getRefreshToken());

        ResponseEntity<KeycloakTokenDto> responseEntity = restTemplate.exchange(
                String.format(keycloakBaseUrl.concat("/realms/%s/protocol/openid-connect/logout"), realm),
                HttpMethod.POST,
                new HttpEntity<>(body, getHttpHeaders(bearerToken)),
                KeycloakTokenDto.class
        );

        return responseEntity.getBody();
    }

    public List<UserInfoDto> getUserListByRole(String realm, String clientId, String role, String bearerToken) {
        ResponseEntity<List<UserInfoDto>> responseEntity = restTemplate.exchange(
                String.format(keycloakBaseUrl.concat("/admin/realms/%s/clients/%s/roles/%s/users"),
                        realm, clientId, role),
                HttpMethod.GET,
                new HttpEntity<>(getHttpHeaders(bearerToken)),
                new ParameterizedTypeReference<>() {
                }
        );

        return responseEntity.getBody();
    }

    public List<RoleDto> getRoleList(String realm, String clientId, String bearerToken) {
        ResponseEntity<List<RoleDto>> responseEntity = restTemplate.exchange(
                String.format(keycloakBaseUrl.concat("/admin/realms/%s/clients/%s/roles"),
                        realm, clientId),
                HttpMethod.GET,
                new HttpEntity<>(getHttpHeaders(bearerToken)),
                new ParameterizedTypeReference<>() {
                }
        );

        return responseEntity.getBody();
    }

    public List<UserInfoDto> getUserList(String realm, String bearerToken) {
        ResponseEntity<List<UserInfoDto>> responseEntity = restTemplate.exchange(
                String.format(keycloakBaseUrl.concat("/admin/realms/%s/users?enabled=true&first=0&max=9999999"),
                        realm),
                HttpMethod.GET,
                new HttpEntity<>(getHttpHeaders(bearerToken)),
                new ParameterizedTypeReference<>() {
                }
        );
        return responseEntity.getBody();
    }

    public UserInfoDto filterExpenseCode(String realm, String bearerToken, String username, List<String> expenseCodes) {

        UserInfoDto resultDto = null;

        for(String expenseCode : expenseCodes) {
            for(String label : new String[]{"expenseCode", "expenseCode2"}) {
                ResponseEntity<List<UserInfoDto>> responseEntity = restTemplate.exchange(
                        String.format(keycloakBaseUrl
                                .concat("/admin/realms/%s/users?username=%s&enabled=true&first=0&max=99999&q=%s:%s")
                                , realm, username, label, expenseCode),
                        HttpMethod.GET,
                        new HttpEntity<>(getHttpHeaders(bearerToken)),
                        new ParameterizedTypeReference<>() {
                        }
                );

                var respBody  = responseEntity.getBody().stream().filter(b -> b.getUsername().equals(username)).toList();

                if(respBody.size() > 0) {
                    if (resultDto == null) {
                        resultDto = respBody.get(0);
                    }
                    if(label.equals("expenseCode")) {
                        resultDto.setExpenseCode(expenseCode);
                        break;
                    }
                    if(label.equals("expenseCode2")) {
                        resultDto.setExpenseCode2(expenseCode);
                        break;
                    }
                }
            }
        }
        return resultDto;
    }

    private HttpHeaders getHttpHeaders(String bearerToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Authorization", bearerToken);
        return headers;
    }
}
