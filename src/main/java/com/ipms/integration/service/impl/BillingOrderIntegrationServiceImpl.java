package com.ipms.integration.service.impl;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.integration.client.BillingClient;
import com.ipms.integration.client.JdeClient;
import com.ipms.integration.client.ParamCommandClient;
import com.ipms.integration.dto.*;
import com.ipms.integration.enums.TransferStatus;
import com.ipms.integration.model.BillingOrderIntegration;
import com.ipms.integration.repository.BillingOrderIntegrationRepository;
import com.ipms.integration.service.BillingOrderIntegrationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class BillingOrderIntegrationServiceImpl implements BillingOrderIntegrationService {
    private final BillingClient billingClient;
    private final JdeClient jdeClient;
    private final ParamCommandClient paramCommandClient;
    private final ProducerService producerService;
    private final BillingOrderIntegrationRepository repository;
    private final List<TransferStatus> sendStatusList = List.of(TransferStatus.ERROR,TransferStatus.RETRY,TransferStatus.PENDING);

    @Value("${integration.minute}")
    private Integer minute;

    @Value("${kafka.billing-order-integration-success-topic}")
    private String orderIntegrationSuccessTopic;

    @Value("${kafka.billing-order-integration-error-topic}")
    private String orderIntegrationErrorTopic;

    @Value("${kafka.billing-order-integration-update-topic}")
    private String orderIntegrationUpdateTopic;

    @Value("${kafka.invoice-fetched-topic}")
    private String invoiceFetchedTopic;


    @Override
    public void saveBillingOrder(Long id) {
        var billingOrderIntegration = BillingOrderIntegration.builder()
                .orderId(id)
                .status(TransferStatus.PENDING)
                .build();
        repository.save(billingOrderIntegration);
    }

    @Override
    public void sendBillingOrder() {
        var billingOrderIntegrations = getSendList(LocalDateTime.now().minusMinutes(minute));
        if (!billingOrderIntegrations.isEmpty()) {
            var billingOrderIntegrationMap = mapByBillingOrderIntegration(billingOrderIntegrations);
            var billingOrderDtos = billingClient.getBillingOrdersByIds(billingOrderIntegrations.stream()
                    .map(BillingOrderIntegration::getOrderId)
                    .toList()).getPayload();

            billingOrderDtos.forEach(billingOrderDto -> {
                try {
                    log.info("Sending billing order with orderId: {}", billingOrderDto.getId());
                    var jdeOrderRequest = JdeOrderRequest.builder()
                            .mainCompany(getIssuerCode(billingOrderDto.getIssuerId()))
                            .orderNumber(billingOrderDto.getOrderNumber())
                            .orderDate(billingOrderDto.getOrderDate().toString())
                            .customerorSupplierNumber(billingOrderDto.getBillingAccountNo())
                            .businessUnit(billingOrderDto.getBusinessUnit())
                            .attachments(getAttachments(billingOrderDto))
                            .orderLines(getOrderLines(billingOrderDto))
                            .build();
                    log.info("JDE send order request: {}", ObjectUtils.toJson(jdeOrderRequest));
                    var jdeOrderResponse = jdeClient.sendOrders(jdeOrderRequest);
                    log.info("JDE send order response: {}", jdeOrderResponse);

                    if (jdeOrderResponse.getErrorCode().equals("0")) {
                        var billingOrderIntegration = billingOrderIntegrationMap.get(billingOrderDto.getId());
                        billingOrderIntegration.setStatus(TransferStatus.SENT);
                        repository.save(billingOrderIntegration);
                        var successEvent = BillingIntegrationSuccessEvent.builder()
                                .orderNumber(billingOrderDto.getOrderNumber())
                                .build();
                        producerService.send(orderIntegrationSuccessTopic, ObjectUtils.toJson(successEvent));
                    }
                } catch (Exception e) {
                    log.error("{},{}", e, ExceptionUtils.getStackTrace(e));
                    updateStatusAndIncreaseRetry(billingOrderIntegrationMap.get(billingOrderDto.getId()).getId(),
                            TransferStatus.ERROR.name(),
                            billingOrderDto.getOrderNumber());
                }
            });
        }
    }

    private String getIssuerCode(Long issuerId) {
        return paramCommandClient.getIssuer(issuerId).getPayload().getCode();
    }

    @Override
    public void updateExternalOrderNumber() {
        var billingOrderIntegrations = getUpdateList();
        if (!billingOrderIntegrations.isEmpty()) {
            var billingOrderIntegrationMap = mapByBillingOrderIntegration(billingOrderIntegrations);
            var billingOrderDtos = billingClient.getBillingOrdersByIds(billingOrderIntegrations.stream()
                    .map(BillingOrderIntegration::getOrderId)
                    .toList()).getPayload();

            billingOrderDtos.forEach(billingOrderDto -> {
                try {
                    log.info("Updating external order with orderId: {}", billingOrderDto.getId());
                    var jdeOrderResponse = jdeClient.fetchOrders(getIssuerCode(billingOrderDto.getIssuerId()),
                            billingOrderDto.getOrderNumber());
                    log.info("JDE fetch external order response: {}", jdeOrderResponse);

                    if (jdeOrderResponse.getResponseInformatin().getErrorCode().equals("0")) {
                        var erpOrderNumber = jdeOrderResponse.getOrders().get(0).getErpOrderNumber();
                        if (!erpOrderNumber.isEmpty()) {
                            var billingOrderIntegration = billingOrderIntegrationMap.get(billingOrderDto.getId());
                            billingOrderIntegration.setStatus(TransferStatus.UPDATED);
                            repository.save(billingOrderIntegration);
                            var updateEvent = BillingIntegrationUpdateEvent.builder()
                                    .id(billingOrderDto.getId())
                                    .externalOrderNumber(erpOrderNumber)
                                    .build();
                            producerService.send(orderIntegrationUpdateTopic, ObjectUtils.toJson(updateEvent));
                        }
                    }
                } catch (Exception e) {
                    log.error("{},{}", e, ExceptionUtils.getStackTrace(e));
                }
            });
        }

    }

    @Override
    public Boolean isOrderCancelled(Long issuerId, String externalOrderNumber, String billingAccountNo) {
        OrderValidateResponse response = validateOrder(issuerId, externalOrderNumber, billingAccountNo);
        return BooleanUtils.isNotTrue(response.getValidOrderorInvoice());
    }

    private OrderValidateResponse validateOrder(Long issuerId, String externalOrderNumber, String billingAccountNo) {
        return jdeClient.validateOrder(getIssuerCode(issuerId), externalOrderNumber, billingAccountNo);
    }

    @Override
    public void fetchInvoices(LocalDate updateDateFrom, LocalDate updateDateTo, String mainCompany) {
        var invoiceRequestParam = InvoiceRequestParam.builder()
                .updateDateFrom(updateDateFrom.toString())
                .updateDateTo(updateDateTo.toString())
                .mainCompany(mainCompany)
                .build();
        log.info("JDE fetch invoices request: {}", ObjectUtils.toJson(invoiceRequestParam));
        var jdeInvoiceResponse = jdeClient.fetchInvoices(invoiceRequestParam);
        log.info("JDE fetch invoices response: {}", jdeInvoiceResponse);
        if (jdeInvoiceResponse.getResponseInformatin().getResponseCount() != 0) {
            producerService.send(invoiceFetchedTopic, ObjectUtils.toJson(jdeInvoiceResponse));
        }
    }

    @Override
    public void processBillingOrderCancelledEvent(Long orderId) {
        var billingOrderIntegration = getBillingOrderIntegrationByOrdereId(orderId).toBuilder()
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(billingOrderIntegration);
    }

    private BillingOrderIntegration getBillingOrderIntegrationByOrdereId(Long orderId) {
        return repository.findByOrderId(orderId).orElseThrow(EntityNotFoundException::new);
    }

    private void updateStatusAndIncreaseRetry(Long id, String status, String orderNumber) {
        var transfer = repository.findById(id)
                .orElseThrow(EntityNotFoundException::new);
        transfer.setStatus(TransferStatus.valueOf(status));
        transfer.setUpdatedAt(LocalDateTime.now());
        transfer.setRetryCount(transfer.getRetryCount()+1);
        if (transfer.getRetryCount()>5) {
            transfer.setStatus(TransferStatus.FAILED);
            var errorEvent = BillingIntegrationErrorEvent.builder()
                    .orderNumber(orderNumber)
                    .build();
            producerService.send(orderIntegrationErrorTopic, ObjectUtils.toJson(errorEvent));
        }
        repository.save(transfer);
    }

    private Map<Long, BillingOrderIntegration> mapByBillingOrderIntegration(List<BillingOrderIntegration> billingOrderIntegrations) {
        return billingOrderIntegrations.stream()
                .collect(Collectors.toMap(BillingOrderIntegration::getOrderId, Function.identity()));
    }

    private List<JdeOrderRequest.OrderLine> getOrderLines(BillingOrderDto billingOrderDto) {
        List<JdeOrderRequest.OrderLine> orderLines = new ArrayList<>();
        AtomicInteger count = new AtomicInteger(1);
        billingOrderDto.getOrderDetails().forEach(orderDetail ->
                orderLines.add(JdeOrderRequest.OrderLine.builder()
                        .billingId(String.valueOf(orderDetail.getId()))
                        .orderLineNo(String.valueOf(count.getAndIncrement()))
                        .itemNumber(orderDetail.getExpenseCode())
                        .quantity(Objects.toString(orderDetail.getQuantity(), ""))
                        .unitPrice(getUnitPrice(orderDetail))
                        .discountRate(orderDetail.getDiscountRate())
                        .build())
        );
        return orderLines;
    }

    private BigDecimal getUnitPrice(BillingOrderDetailDto orderDetail) {
        var isUnitPriceDefined = orderDetail.getUnitPrice() != null
                && orderDetail.getUnitPrice().compareTo(BigDecimal.ONE) > 0;
        if (isUnitPriceDefined) {
            return orderDetail.getUnitPrice().multiply(BigDecimal.valueOf(10000));
        } else {
            return null;
        }
    }

    private List<JdeOrderRequest.Attachment> getAttachments(BillingOrderDto billingOrderDto) {
        new JdeOrderRequest.Attachment(billingOrderDto.getDescription1());
        return List.of(
                new JdeOrderRequest.Attachment(billingOrderDto.getDescription1()),
                new JdeOrderRequest.Attachment(billingOrderDto.getDescription2()),
                new JdeOrderRequest.Attachment(billingOrderDto.getDescription3())
        );
    }

    private List<BillingOrderIntegration> getSendList(LocalDateTime time) {
        return repository.findByUpdatedAtLessThanEqualAndStatusIn(time, sendStatusList);
    }

    private List<BillingOrderIntegration> getUpdateList() {
        return repository.findByStatusIn(List.of(TransferStatus.SENT));
    }
}
