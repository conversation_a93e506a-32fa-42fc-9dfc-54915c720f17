package com.ipms.billing.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.billing.dto.BillingAccountDto;
import com.ipms.billing.dto.BillingAccountFilterRequest;
import com.ipms.billing.service.BillingAccountService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import=classpath:/application.yml")
class BillingAccountControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BillingAccountService service;

    ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    private BillingAccountDto billingAccountDto;

    @BeforeEach
    void setUp() {
        billingAccountDto = BillingAccountDto.builder()
                .accountName("accountName-1")
                .accountNo("accountNo-1")
                .issuerId(1L)
                .address("address-1")
                .discountRate(BigDecimal.ONE)
                .currency("USD")
                .riskManagement("NO_RISK")
                .description("description-1")
                .issueIds(List.of(1L, 2L, 3L))
                .billingDay("DAY_1")
                .endOfBillingPeriod("END_OF_MONTH")
                .willBeSentToResponsible(true)
                .sendToEmails(List.of("<EMAIL>", "<EMAIL>"))
                .copyToEmails(List.of("<EMAIL>", "<EMAIL>"))
                .willOriginalInvoiceBeSent(true)
                .originalInvoiceRecipient("Name Surname or Department")
                .billingSystem("Tymetrix 360")
                .attachmentsToBeSent("Timespents, disbursements")
                .note("Lorem ipsum dolor sit amet")
                .build();
    }

    @Test
    void whenSave_thenReturnBaseResponse() throws Exception {
        when(service.save(billingAccountDto)).thenReturn(billingAccountDto);

        mockMvc.perform(post("/billing-accounts/")
                        .content(objectMapper.writeValueAsString(billingAccountDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenUpdate_thenReturnBaseResponse() throws Exception {
        billingAccountDto.setVersion(0L);
        when(service.update(1L, billingAccountDto)).thenReturn(billingAccountDto);

        mockMvc.perform(put("/billing-accounts/1")
                        .content(objectMapper.writeValueAsString(billingAccountDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenDelete_thenReturnBaseResponse() throws Exception {
        mockMvc.perform(delete("/billing-accounts/1/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenGetById_thenReturnBaseResponse() throws Exception {
        when(service.getById(1L)).thenReturn(billingAccountDto);

        mockMvc.perform(get("/billing-accounts/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenGetAll_thenReturnBaseResponse() throws Exception {
        when(service.getAll(any(BillingAccountFilterRequest.class)))
                .thenReturn(List.of(billingAccountDto, billingAccountDto));

        mockMvc.perform(get("/billing-accounts/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenGetAllPageable_thenReturnBaseResponse() throws Exception {
        when(service.getAllPageable(any(BillingAccountFilterRequest.class), anyInt(), anyInt()))
                .thenReturn(null);

        mockMvc.perform(get("/billing-accounts/1/10")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenGetBillingAccountList_thenReturnBaseResponse() throws Exception {
        when(service.getByIdIn(List.of(1L, 2L, 3L)))
                .thenReturn(List.of(billingAccountDto, billingAccountDto));

        mockMvc.perform(get("/billing-accounts/ids/1,2,3")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

}