package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.dto.CounterRightOwnerCardDto;
import com.ipms.activity.dto.CounterRightOwnerDto;
import com.ipms.activity.dto.LawyerDto;
import com.ipms.activity.service.CounterRightOwnerCardService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class CounterRightOwnerCardControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CounterRightOwnerCardService service;

    private CounterRightOwnerCardDto dto;

    ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    @Before
    public void initialize() {
        dto = getCounterRightOwnerCardDto();
    }

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        when(service.save(any(CounterRightOwnerCardDto.class))).thenReturn(dto);

        mockMvc.perform(post("/counter-right-owner-card")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

    }

    @Test
    public void whenSaveBulk_thenReturnBaseResponse() throws Exception {
        var dtos = List.of(dto);

        when(service.saveBulk(anyList())).thenReturn(dtos);

        mockMvc.perform(post("/counter-right-owner-card/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dtos))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {
        when(service.save(any(CounterRightOwnerCardDto.class))).thenReturn(dto);

        mockMvc.perform(put("/counter-right-owner-card/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetAllByActivityId_thenReturnBaseResponse() throws Exception {
        when(service.save(any(CounterRightOwnerCardDto.class))).thenReturn(dto);

        mockMvc.perform(get("/counter-right-owner-card/by-activity/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());
        mockMvc.perform(delete("/counter-right-owner-card/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    private CounterRightOwnerCardDto getCounterRightOwnerCardDto() {
        var counterRightOwnerDto = CounterRightOwnerDto.builder()
                .name("cro-1")
                .address("address-1")
                .version(0L)
                .id(1L)
                .build();

        var agencyDto = AgencyDto.builder()
                .name("agency-1")
                .email("email-1")
                .city("city-1")
                .country("country-1")
                .build();

        var lawyerDto = LawyerDto.builder()
                .name("lawyer-1")
                .type("type-1")
                .email("email-1")
                .phoneNumber("phone-number-1")
                .agency(agencyDto)
                .build();


        return CounterRightOwnerCardDto.builder()
                .id(1L)
                .activityId(123L)
                .counterRightOwner(counterRightOwnerDto)
                .lawyers(List.of(lawyerDto))
                .build();
    }
}