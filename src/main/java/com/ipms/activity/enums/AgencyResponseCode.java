package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum AgencyResponseCode implements Code {

    AGENCY_NOT_FOUND(1000, "error.code.agency.not_found");

    private final Integer code;
    private final String messageKey;

    AgencyResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
