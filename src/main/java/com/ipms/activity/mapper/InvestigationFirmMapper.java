package com.ipms.activity.mapper;

import com.ipms.activity.dto.InvestigationFirmDto;
import com.ipms.activity.model.InvestigationFirm;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface InvestigationFirmMapper {
    InvestigationFirm toInvestigationFirm(InvestigationFirmDto investigationFirmDto);
    InvestigationFirmDto toInvestigationFirmDto(InvestigationFirm investigationFirm);
    InvestigationFirm toInvestigationFirmFromDto(InvestigationFirmDto dto, @MappingTarget InvestigationFirm investigationFirm);
}
