eureka:
  client:
    enabled: false
server:
  servlet:
    context-path: /api/users
keycloak:
  realm: ipms-default
  auth-server-url: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth
  ssl-required: external
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ********************************

ipms:
  keycloak-web-client: ipms-web
  keycloak-web-client-secret: ********************************
  keycloak-master-client-secret: ********************************
  keycloak-master-realm: master
  keycloak-master-clientId: 68bbeeb8-eef9-446b-928e-353b78874c72
  keycloak-master-clientNo: api

spring:
  cloud:
    config:
      enabled: false
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ********************************
        provider:
          keycloak:
            token-uri: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth/realms/ipms-default/protocol/openid-connect/token