package com.ipms.activity.service;

import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.exception.AgencyNotFoundException;
import com.ipms.activity.mapper.AgencyMapper;
import com.ipms.activity.mapper.AgencyMapperImpl;
import com.ipms.activity.model.Agency;
import com.ipms.activity.repository.AgencyRepository;
import com.ipms.activity.service.impl.AgencyServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;

@RunWith(SpringRunner.class)
public class AgencyServiceTest {

    @MockBean
    private AgencyService service;

    @Mock
    private AgencyRepository repository;

    private final AgencyMapper mapper = new AgencyMapperImpl();

    @Before
    public void setUp() {
        service = new AgencyServiceImpl(repository, mapper);
    }

    @Test
    public void givenId_whenGetById_thenReturnAgency() {
        var agency = Agency.builder()
                .id(1L)
                .name("test-name")
                .city("TR")
                .email("<EMAIL>")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(agency));
        assertThat(service.getById(1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetById_thenThrowAgencyNotFoundException() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(()->service.getById(1L));
        assertThat(thrown).isInstanceOf(AgencyNotFoundException.class);
    }

    @Test
    public void givenName_whenGetByName_thenReturnAgencyList() {
        var agency = Agency.builder().id(1L).name("test-name").build();
        Mockito.when(repository.findByNameContainsOrderByName(Mockito.anyString()))
                .thenReturn(List.of(agency));
        assertThat(service.getByName("test-name").get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenSave_thenReturnAgency() {
        var agency = Agency.builder()
                .id(1L)
                .name("test-name")
                .city("TR")
                .email("<EMAIL>")
                .build();
        var agencyDto = AgencyDto.builder()
                .id(1L)
                .name("test-name")
                .city("TR")
                .email("<EMAIL>")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                        .thenReturn(Optional.of(agency));
        Mockito.when(repository.save(Mockito.any(Agency.class)))
                        .thenReturn(agency);
        assertThat(service.save(agencyDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenSave_thenReturnAgency() {
        var agency = Agency.builder()
                .id(1L)
                .name("test-name")
                .city("TR")
                .email("<EMAIL>")
                .build();
        var agencyDto = AgencyDto.builder()
                .name("test-name")
                .city("TR")
                .email("<EMAIL>")
                .build();
        Mockito.when(repository.save(Mockito.any(Agency.class)))
                .thenReturn(agency);
        assertThat(service.save(agencyDto).getId())
                .isEqualTo(1L);
    }
}
