package com.ipms.activity.service;

import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.dto.LawyerDto;
import com.ipms.activity.exception.LawyerNotFoundException;
import com.ipms.activity.mapper.LawyerMapper;
import com.ipms.activity.mapper.LawyerMapperImpl;
import com.ipms.activity.model.Agency;
import com.ipms.activity.model.CounterRightOwner;
import com.ipms.activity.model.CounterRightOwnerCard;
import com.ipms.activity.model.Lawyer;
import com.ipms.activity.repository.LawyerRepository;
import com.ipms.activity.service.impl.LawyerServiceImpl;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.*;

@RunWith(SpringRunner.class)
public class LawyerServiceTest {

    @MockBean
    private LawyerService service;

    @Mock
    private LawyerRepository repository;

    @Mock
    private AgencyService agencyService;

    @Mock
    private CounterRightOwnerCardService counterRightOwnerCardService;

    private final LawyerMapper mapper = new LawyerMapperImpl();

    private CounterRightOwnerCard counterRightOwnerCard;

    @Before
    public void setUp() {
        service = new LawyerServiceImpl(repository, mapper, agencyService, counterRightOwnerCardService);
        counterRightOwnerCard = getCounterRightOwnerCard();
    }

    @Test
    public void whenAddToCounterRightOwnerCard_thenReturnLawyerDto() {
        var agencyDto = AgencyDto.builder()
                .email("<EMAIL>")
                .city("tr")
                .name("test-name")
                .build();
        var agency = Agency.builder()
                .email("<EMAIL>")
                .city("tr")
                .name("test-name")
                .build();
        var lawyer = Lawyer.builder()
                .id(1L)
                .name("lawyer-name")
                .email("<EMAIL>")
                .phoneNumber("5555555555")
                .agencies(Set.of(agency))
                .build();

        var lawyerDto = LawyerDto.builder()
                .id(1L)
                .name("lawyer-name")
                .email("<EMAIL>")
                .phoneNumber("5555555555")
                .agency(agencyDto)
                .build();


        Mockito.when(counterRightOwnerCardService.getCounterRightOwnerCardById(Mockito.anyLong()))
                .thenReturn(counterRightOwnerCard);
        Mockito.when(agencyService.save(Mockito.any(AgencyDto.class)))
                .thenReturn(agency);
        Mockito.when(repository.save(Mockito.any(Lawyer.class)))
                .thenReturn(lawyer);
        Assert.assertEquals(lawyerDto, service.addToCounterRightOwnerCard(lawyerDto, 1L));

    }

    private CounterRightOwnerCard getCounterRightOwnerCard() {
        CounterRightOwner counterRightOwner = CounterRightOwner.builder()
                .id(1L)
                .version(0L)
                .name("cro-1")
                .address("address-1")
                .build();

        return  CounterRightOwnerCard.builder()
                .id(1L)
                .version(0L)
                .activityId(1L)
                .counterRightOwner(counterRightOwner)
                .build();
    }

    @Test
    public void givenDto_whenUpdate_thenReturnLawyerDto() {
        var agency = Agency.builder()
                .name("test-name")
                .build();
        var lawyer = Lawyer.builder()
                .id(1L)
                .name("test-name")
                .agencies(Set.of(agency))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong())).thenReturn(Optional.of(lawyer));
        Mockito.when(repository.save(Mockito.any(Lawyer.class)))
                .thenReturn(lawyer);
        assertThat(service.update(LawyerDto.builder().id(1L).build(), 1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenDto_whenUpdate_thenThrowLawyerNotFoundException() {
        var lawyer = LawyerDto.builder().id(1L).name("test-name").build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(()->service.update(lawyer, 1L));
        assertThat(thrown).isInstanceOf(LawyerNotFoundException.class);
    }

    @Test
    public void whenDelete_thenDoesNotThrowAnyException() {
        var lawyer = Lawyer.builder().id(1L).name("test-name").build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(lawyer));

        assertThatCode(() -> service.delete(1L, 0L)).doesNotThrowAnyException();
    }

    @Test
    public void whenGetByCounterRightOwnerId_thenReturnList() {
        var lawyer = Lawyer.builder()
                .id(1L)
                .name("test-name")
                .build();
        Mockito.when(repository.findByCounterRightOwnerCardId(Mockito.anyLong()))
                .thenReturn(List.of(lawyer));
        Assertions.assertThat(service.getByCounterRightOwnerCardId(1L))
                .isNotEmpty();
    }
}
