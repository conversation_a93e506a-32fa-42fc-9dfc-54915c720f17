package com.ipms.activity.service;

import com.ipms.activity.client.MatterClient;
import com.ipms.activity.client.ParamCommandClient;
import com.ipms.activity.dto.*;
import com.ipms.activity.dto.trademark.MarkDto;
import com.ipms.activity.dto.trademark.RegistrationDto;
import com.ipms.activity.enums.ActivityBaseDateType;
import com.ipms.activity.enums.ActivityPeriodType;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.OppositionOut;
import com.ipms.activity.repository.ActivityRepository;
import com.ipms.activity.service.impl.ActivityDueDateServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ActivityDueDateServiceTest {

    @Mock
    private MatterClient matterClient;

    @Mock
    private ParamCommandClient paramCommandClient;

    @Mock
    private ActivityRepository repository;

    private ActivityDueDateServiceImpl service;

    @Before
    public void setUp() {
        service = new ActivityDueDateServiceImpl(matterClient, paramCommandClient, repository);
    }

    @Test
    public void whenCalculateAndSetStatusDueDate_withNullBaseDate_thenDoesNotSetStatusDueDate() {
        var oppositionOut = OppositionOut.builder().build();

        var activity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.OPINION)
                .oppositionOut(oppositionOut)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder().type("TRADEMARK").build())
                .build();

        var activityDueDateDto = ActivityTypeDueDateResponse.ActivityDueDateDto.builder()
                .baseDate(ActivityBaseDateType.BULLETIN_DATE)
                .period(ActivityPeriodType.DAY)
                .periodValue(5)
                .build();

        var dueDateResponse = ActivityTypeDueDateResponse.builder()
                .code(200)
                .payload(activityDueDateDto)
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getLatestByMatterTypeActivityAndStatus(anyString(), anyString(), anyString()))
                .thenReturn(dueDateResponse);

        service.calculateAndSetStatusDueDate(activity);

        assertThat(activity.getStatusDueDate()).isNull();
    }

    @Test
    public void whenCalculateAndSetStatusDueDate_withNullDueDateFormula_thenDoesNotSetStatusDueDate() {
        var oppositionOut = OppositionOut.builder()
                .bulletinPublicationDate(LocalDate.of(2024, 4, 1))
                .build();

        var activity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.OPINION)
                .oppositionOut(oppositionOut)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder().type("TRADEMARK").build())
                .build();

        var dueDateResponse = ActivityTypeDueDateResponse.builder()
                .code(200)
                .payload(null)
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getLatestByMatterTypeActivityAndStatus(anyString(), anyString(), anyString()))
                .thenReturn(dueDateResponse);

        service.calculateAndSetStatusDueDate(activity);

        assertThat(activity.getStatusDueDate()).isNull();
    }

    @Test
    public void whenCalculateAndSetStatusDueDate_withMonthPeriod_thenSetsCorrectDueDate() {
        var oppositionOut = OppositionOut.builder()
                .bulletinPublicationDate(LocalDate.of(2024, 3, 30))
                .build();

        var activity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.OPINION)
                .oppositionOut(oppositionOut)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder().type("TRADEMARK").build())
                .build();

        var activityDueDateDto = ActivityTypeDueDateResponse.ActivityDueDateDto.builder()
                .baseDate(ActivityBaseDateType.BULLETIN_DATE)
                .period(ActivityPeriodType.MONTH)
                .periodValue(3)
                .build();

        var dueDateResponse = ActivityTypeDueDateResponse.builder()
                .code(200)
                .payload(activityDueDateDto)
                .build();

        var calculatedDueDate = LocalDate.of(2024, 6, 30);
        var nextWorkDay = LocalDate.of(2024, 7, 1);

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getLatestByMatterTypeActivityAndStatus(anyString(), anyString(), anyString()))
                .thenReturn(dueDateResponse);

        when(paramCommandClient.isWorkingDay(calculatedDueDate))
                .thenReturn(BooleanResponse.builder().payload(false).build());

        when(paramCommandClient.getHolidayList())
                .thenReturn(HolidayResponse.builder()
                        .payload(List.of(
                                LocalDate.of(2024, 6, 30)
                        )).build()
                );

        service.calculateAndSetStatusDueDate(activity);

        assertThat(activity.getStatusDueDate()).isEqualTo(nextWorkDay);
    }

    @Test
    public void whenCalculateAndSetStatusDueDate_withDayPeriod_thenSetsCorrectDueDate() {
        var oppositionOut = OppositionOut.builder()
                .bulletinPublicationDate(LocalDate.of(2024, 4, 1))
                .build();

        var activity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.OPINION)
                .oppositionOut(oppositionOut)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder().type("TRADEMARK").build())
                .build();

        var activityDueDateDto = ActivityTypeDueDateResponse.ActivityDueDateDto.builder()
                .baseDate(ActivityBaseDateType.BULLETIN_DATE)
                .period(ActivityPeriodType.DAY)
                .periodValue(30)
                .build();

        var dueDateResponse = ActivityTypeDueDateResponse.builder()
                .code(200)
                .payload(activityDueDateDto)
                .build();

        var calculatedDueDate = LocalDate.of(2024, 5, 1);
        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getLatestByMatterTypeActivityAndStatus(anyString(), anyString(), anyString()))
                .thenReturn(dueDateResponse);

        when(paramCommandClient.isWorkingDay(calculatedDueDate))
                .thenReturn(BooleanResponse.builder().payload(true).build());

        service.calculateAndSetStatusDueDate(activity);

        assertThat(activity.getStatusDueDate()).isEqualTo(calculatedDueDate);
    }

    @Test
    public void whenCalculateAndSetStatusDueDate_withApplicationDateBaseType_thenUsesApplicationDate() {
        var oppositionOut = OppositionOut.builder()
                .bulletinPublicationDate(LocalDate.of(2024, 4, 1))
                .build();

        var activity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.OPINION)
                .oppositionOut(oppositionOut)
                .build();

        var registrationDto = RegistrationDto.builder()
                .applicationDate(LocalDate.of(2024, 3, 15))
                .build();

        var markDto = MarkDto.builder()
                .registration(registrationDto)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .type("TRADEMARK")
                        .mark(markDto)
                        .build())
                .build();

        var activityDueDateDto = ActivityTypeDueDateResponse.ActivityDueDateDto.builder()
                .baseDate(ActivityBaseDateType.APPLICATION_DATE)
                .period(ActivityPeriodType.MONTH)
                .periodValue(6)
                .build();

        var dueDateResponse = ActivityTypeDueDateResponse.builder()
                .code(200)
                .payload(activityDueDateDto)
                .build();

        var calculatedDueDate = LocalDate.of(2024, 9, 15);
        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getLatestByMatterTypeActivityAndStatus(anyString(), anyString(), anyString()))
                .thenReturn(dueDateResponse);

        when(paramCommandClient.isWorkingDay(calculatedDueDate))
                .thenReturn(BooleanResponse.builder().payload(true).build());

        service.calculateAndSetStatusDueDate(activity);

        assertThat(activity.getStatusDueDate()).isEqualTo(calculatedDueDate);
    }

    @Test
    public void whenUpdateStatusDueDate_withDifferentStatuses_thenCalculatesNewDueDate() {
        var oppositionOut = OppositionOut.builder()
                .bulletinPublicationDate(LocalDate.of(2024, 4, 1))
                .build();

        var existingActivity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.ACCEPTED)
                .oppositionOut(oppositionOut)
                .build();

        var updatedActivity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.OPINION)
                .oppositionOut(oppositionOut)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder().type("TRADEMARK").build())
                .build();

        var activityDueDateDto = ActivityTypeDueDateResponse.ActivityDueDateDto.builder()
                .baseDate(ActivityBaseDateType.BULLETIN_DATE)
                .period(ActivityPeriodType.DAY)
                .periodValue(30)
                .build();

        var dueDateResponse = ActivityTypeDueDateResponse.builder()
                .code(200)
                .payload(activityDueDateDto)
                .build();

        var calculatedDueDate = LocalDate.of(2024, 5, 1);
        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getLatestByMatterTypeActivityAndStatus(anyString(), anyString(), anyString()))
                .thenReturn(dueDateResponse);

        when(paramCommandClient.isWorkingDay(calculatedDueDate))
                .thenReturn(BooleanResponse.builder().payload(true).build());

        service.updateStatusDueDate(existingActivity, updatedActivity);

        assertThat(updatedActivity.getStatusDueDate()).isEqualTo(calculatedDueDate);
    }

    @Test
    public void whenUpdateStatusDueDate_withSameStatusAndBaseDateChanged_thenCalculatesNewDueDate() {
        var existingOppositionOut = OppositionOut.builder()
                .bulletinPublicationDate(LocalDate.of(2024, 4, 1))
                .build();

        var updatedOppositionOut = OppositionOut.builder()
                .bulletinPublicationDate(LocalDate.of(2024, 4, 15))
                .build();

        var existingActivity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.OPINION)
                .oppositionOut(existingOppositionOut)
                .build();

        var updatedActivity = Activity.builder()
                .id(1L)
                .matterId(100L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.OPINION)
                .oppositionOut(updatedOppositionOut)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder().type("TRADEMARK").build())
                .build();

        var activityDueDateDto = ActivityTypeDueDateResponse.ActivityDueDateDto.builder()
                .baseDate(ActivityBaseDateType.BULLETIN_DATE)
                .period(ActivityPeriodType.MONTH)
                .periodValue(2)
                .build();

        var dueDateResponse = ActivityTypeDueDateResponse.builder()
                .code(200)
                .payload(activityDueDateDto)
                .build();

        var calculatedDueDate = LocalDate.of(2024, 6, 15);
        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getLatestByMatterTypeActivityAndStatus(anyString(), anyString(), anyString()))
                .thenReturn(dueDateResponse);

        when(paramCommandClient.isWorkingDay(calculatedDueDate))
                .thenReturn(BooleanResponse.builder().payload(true).build());

        service.updateStatusDueDate(existingActivity, updatedActivity);

        assertThat(updatedActivity.getStatusDueDate()).isEqualTo(calculatedDueDate);
    }
}
