package com.ipms.activity.controller;

import com.ipms.activity.dto.SuspendedProductDto;
import com.ipms.activity.dto.SuspendedProductPageDto;
import com.ipms.activity.service.SuspendedProductService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("suspended-product")
public class SuspenedProductController {
    private final SuspendedProductService service;

    @GetMapping("/{id}")
    public BaseResponse<SuspendedProductDto> getById(@PathVariable("id") Long id) {
        var dto = service.getById(id);
        return BaseResponse.<SuspendedProductDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(dto)
                .build();
    }

    @GetMapping("/{activityId}/{page}/{size}")
    public BaseResponse<SuspendedProductPageDto> getAllByActivity(@PathVariable Long activityId,
                                                                  @PathVariable int page,
                                                                  @PathVariable int size) {
        return BaseResponse.<SuspendedProductPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByActivity(activityId, page, size))
                .build();
    }

    @GetMapping("/by-activity-id/{activityId}")
    public BaseResponse<List<SuspendedProductDto>> getAllByActivityId(@PathVariable Long activityId) {
        return BaseResponse.<List<SuspendedProductDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByActivity(activityId))
                .build();
    }

    @PostMapping
    public BaseResponse<SuspendedProductDto> save(@RequestBody SuspendedProductDto suspendedProductDto) {
        var saved = service.save(suspendedProductDto);
        return BaseResponse.<SuspendedProductDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(saved)
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }



}
