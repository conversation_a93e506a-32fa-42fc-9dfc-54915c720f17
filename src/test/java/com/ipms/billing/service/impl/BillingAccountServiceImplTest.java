package com.ipms.billing.service.impl;

import com.ipms.billing.client.ParamCommandClient;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.BillingDay;
import com.ipms.billing.exception.BillingAccountNotFoundException;
import com.ipms.billing.mapper.BillingAccountMapper;
import com.ipms.billing.model.BillingAccount;
import com.ipms.billing.model.BillingAccountExpenseCode;
import com.ipms.billing.repository.BillingAccountExpenseCodeRepository;
import com.ipms.billing.repository.BillingAccountRepository;
import com.ipms.billing.specification.BillingAccountSpecification;
import com.ipms.billing.validator.BillingAccountValidator;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.utils.NumberUtils;
import org.jetbrains.annotations.Nullable;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BillingAccountServiceImplTest {

    @InjectMocks
    private BillingAccountServiceImpl service;

    @Mock
    private BillingAccountRepository repository;

    @Mock
    private BillingAccountMapper mapper;

    @Mock
    private BillingAccountValidator validator;

    @Mock
    private ProducerService producerService;

    @Mock
    private ParamCommandClient paramCommandClient;

    @Mock
    private BillingAccountExpenseCodeRepository expenseCodeRepository;


    @Test
    void whenSave_thenReturnBillingAccountDto() {
        // Arrange
        BillingAccountDto dto = new BillingAccountDto();
        BillingAccount mockBillingAccount = mock(BillingAccount.class);
        when(mapper.toBillingAccount(dto)).thenReturn(mockBillingAccount);
        when(repository.save(mockBillingAccount)).thenReturn(mockBillingAccount);
        when(mapper.toBillingAccountDto(mockBillingAccount)).thenReturn(dto);

        // Act
        BillingAccountDto savedDto = service.save(dto);

        // Assert
        verify(mapper, times(1)).toBillingAccount(dto);
        verify(repository, times(1)).save(mockBillingAccount);
        assertEquals(dto, savedDto);
    }

    @Test
    void whenUpdate_thenReturnBillingAccountDto() {
        // Arrange
        Long id = 1L;
        BillingAccountDto dto = new BillingAccountDto();
        BillingAccount existingBillingAccount = mock(BillingAccount.class);
        BillingAccount updatedBillingAccount = mock(BillingAccount.class);

        when(repository.findById(id)).thenReturn(Optional.of(existingBillingAccount));
        when(mapper.toBillingAccountFromDto(dto, existingBillingAccount)).thenReturn(updatedBillingAccount);
        when(repository.save(updatedBillingAccount)).thenReturn(updatedBillingAccount);
        when(mapper.toBillingAccountDto(updatedBillingAccount)).thenReturn(dto);

        // Act
        BillingAccountDto updatedDto = service.update(id, dto);

        // Assert
        verify(repository, times(1)).findById(id);
        verify(mapper, times(1)).toBillingAccountFromDto(dto, existingBillingAccount);
        verify(repository, times(1)).save(updatedBillingAccount);
        assertEquals(dto, updatedDto);
    }

    @Test
    void whenGetById_thenReturnBillingAccountDto() {
        // Arrange
        Long id = 1L;
        BillingAccountDto dto = new BillingAccountDto();
        BillingAccount billingAccount = mock(BillingAccount.class);

        when(repository.findById(id)).thenReturn(Optional.of(billingAccount));
        when(mapper.toBillingAccountDto(billingAccount)).thenReturn(dto);

        // Act
        BillingAccountDto actualDto = service.getById(id);

        // Assert
        verify(repository, times(1)).findById(id);
        assertEquals(dto, actualDto);
    }

    @Test
    @DisplayName("When billing account is not found, then throw BillingAccountNotFoundException")
    void whenGetById_throwBillingAccountNotFoundException() {
        // Arrange
        Long id = 1L;
        when(repository.findById(id)).thenReturn(Optional.empty());

        // Act and Assert
        Assertions.assertThrows(BillingAccountNotFoundException.class, () -> service.getById(id));
    }

    @Test
    void whenDelete_thenBillingAccountIsDeleted() {
        Long id = 1L;
        Long version = 2L;

        var billingAccount = BillingAccount.builder()
                .id(id)
                .version(version)
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingAccount));

        ArgumentCaptor<BillingAccount> captor = ArgumentCaptor.forClass(BillingAccount.class);

        service.delete(id, version);

        verify(repository).findById(id);
        verify(repository).save(captor.capture());
        assertTrue(captor.getValue().isDeleted());
    }

    @Test
    @DisplayName("When billing account is not found, then throw BillingAccountNotFoundException")
    void whenDelete_throwBillingAccountNotFoundException() {
        // Arrange
        Long id = 1L;
        Long version = 2L;
        when(repository.findById(id)).thenReturn(Optional.empty());

        // Act and Assert
        Assertions.assertThrows(BillingAccountNotFoundException.class, () -> service.delete(id, version));
    }

    @Test
    void whenGetAll_thenReturnListOfBillingAccountDto() {
        when(repository.findAll(any(BillingAccountSpecification.class)))
                .thenReturn(List.of(mock(BillingAccount.class), mock(BillingAccount.class)));

        var billingAccountDtos = service.getAll(mock(BillingAccountFilterRequest.class));

        verify(repository, times(1)).findAll(any(BillingAccountSpecification.class));
        assertEquals(2, billingAccountDtos.size());
    }

    @Test
    void whenGetAllPageable_thenReturnBillingAccountPageDto() {
        Page<BillingAccount> billingAccountPage = mock(Page.class);
        when(billingAccountPage.getTotalPages()).thenReturn(1);
        when(billingAccountPage.getTotalElements()).thenReturn(2L);
        when(billingAccountPage.getContent()).thenReturn(List.of(mock(BillingAccount.class), mock(BillingAccount.class)));

        when(repository.findAll(any(BillingAccountSpecification.class), any(PageRequest.class)))
                .thenReturn(billingAccountPage);

        var billingAccountPageDto = service.getAllPageable(mock(BillingAccountFilterRequest.class), 1, 10);

        verify(repository, times(1)).findAll(any(BillingAccountSpecification.class), any(Pageable.class));
        assertEquals(1, billingAccountPageDto.getTotalPages());
        assertEquals(2, billingAccountPageDto.getTotalElements());
        assertEquals(2, billingAccountPageDto.getBillingAccountDtos().size());
    }

    @Test
    void whenGetByIdIn_thenReturnListOfBillingAccountDto() {
        when(repository.findByIdIn(List.of(1L, 2L, 3L)))
                .thenReturn(List.of(mock(BillingAccount.class), mock(BillingAccount.class)));

        var billingAccountDtos = service.getByIdIn(List.of(1L, 2L, 3L));

        verify(repository, times(1)).findByIdIn(List.of(1L, 2L, 3L));
        assertEquals(2, billingAccountDtos.size());
    }

    @Test
    void whenGetBillingAccountById_thenReturnBillingAccount() {
        Long id = 1L;
        BillingAccount billingAccount = mock(BillingAccount.class);
        when(repository.findById(id)).thenReturn(Optional.of(billingAccount));

        service.getBillingAccountById(id);

        verify(repository).findById(id);
    }

    @Test
    void whenIsBillableWithNoBillingDay_thenReturnsBillableBillingAccount() {
        var billingAccount = BillingAccount.builder()
                .billingDay(BillingDay.NA)
                .build();
        when(repository.findById(1L)).thenReturn(Optional.of(billingAccount));
        var billableBillingAccount = service.isBillable(1L, List.of(LocalDate.parse("2024-12-01")));

        assertFalse(billableBillingAccount.getIsBillable());
    }

    @Test
    void whenIsBillableWithBillingDay_thenReturnsBillableBillingAccount() {
        var billingAccount = BillingAccount.builder()
                .billingDay(BillingDay.DAY_1)
                .build();
        when(repository.findById(1L)).thenReturn(Optional.of(billingAccount));
        var billableBillingAccount = service.isBillable(1L, List.of(LocalDate.parse("2024-12-01")));

        assertTrue(billableBillingAccount.getIsBillable());
        assertEquals(LocalDate.parse("2024-12-01"), billableBillingAccount.getBillingDay());
    }

    @Test
    void whenIsBillableWithNotMatchesDate_thenReturnsBillableBillingAccount() {
        var billingAccount = BillingAccount.builder()
                .billingDay(BillingDay.DAY_3)
                .build();
        when(repository.findById(1L)).thenReturn(Optional.of(billingAccount));
        var billableBillingAccount = service.isBillable(1L, List.of(LocalDate.parse("2024-12-01"),
                LocalDate.parse("2024-12-02")));

        assertFalse(billableBillingAccount.getIsBillable());
    }

    @Test
    void whenIsBillableWithEndOfMonth31_thenReturnsBillableBillingAccount() {
        var billingAccount = BillingAccount.builder()
                .billingDay(BillingDay.END_OF_MONTH)
                .build();
        when(repository.findById(1L)).thenReturn(Optional.of(billingAccount));

        var billableBillingAccount = service.isBillable(1L, List.of(LocalDate.parse("2024-12-28"),
                LocalDate.parse("2024-12-29")));

        assertTrue(billableBillingAccount.getIsBillable());
        assertEquals(LocalDate.parse("2024-12-31"), billableBillingAccount.getBillingDay());
    }

    @Test
    void whenIsBillableWithEndOfMonth30_thenReturnsBillableBillingAccount() {
        var billingAccount = BillingAccount.builder()
                .billingDay(BillingDay.END_OF_MONTH)
                .build();
        when(repository.findById(1L)).thenReturn(Optional.of(billingAccount));

        var billableBillingAccount = service.isBillable(1L, List.of(LocalDate.parse("2024-11-28"),
                LocalDate.parse("2024-11-29")));

        assertTrue(billableBillingAccount.getIsBillable());
        assertEquals(LocalDate.parse("2024-11-30"), billableBillingAccount.getBillingDay());
    }

    @Test
    void whenProcessJdePriceEvent_thenSaveOrUpdatePrices() {
        var accountNo1 = "6";
        var accountNo2 = "8";
        var price1 = PriceDto.builder()
                .itemNo("1")
                .price("12,3456")
                .currencyCode("TL")
                .fromDate("2024-12-01")
                .toDate("2024-12-10")
                .mainCompany("00001")
                .customerorSupplierNumber("0")
                .build();
        var price2 = PriceDto.builder()
                .itemNo("2")
                .price("13,3456")
                .currencyCode("USD")
                .fromDate("2024-12-02")
                .toDate("2024-12-11")
                .mainCompany("00001")
                .customerorSupplierNumber(accountNo1)
                .build();
        var price3 = PriceDto.builder()
                .itemNo("3")
                .toDate("2024-12-02")
                .price("13,4567")
                .currencyCode("TL")
                .fromDate("2024-12-03")
                .toDate("2024-12-12")
                .mainCompany("00002")
                .customerorSupplierNumber("0")
                .build();
        var price4 = PriceDto.builder()
                .itemNo("4")
                .toDate("2024-12-02")
                .price("14,4567")
                .currencyCode("TL")
                .fromDate("2024-12-03")
                .toDate("2024-12-12")
                .mainCompany("00002")
                .customerorSupplierNumber(accountNo2)
                .build();
        var priceEvent = PriceEvent.builder()
                .priceLists(List.of(price1, price2, price3, price4))
                .build();
        var issuer1 = IssuerDto.builder()
                .id(1L)
                .code("00001")
                .build();
        var issuer2 = IssuerDto.builder()
                .id(2L)
                .code("00002")
                .build();
        var issuerResponse = IssuerResponse.builder()
                .payload(List.of(issuer1, issuer2))
                .build();
        when(paramCommandClient.getIssuers()).thenReturn(issuerResponse);

        var billingAccount1 = BillingAccount.builder()
                .id(1L)
                .accountNo(accountNo1)
                .issuerId(issuer1.getId())
                .build();
        var billingAccount2 = BillingAccount.builder()
                .id(2L)
                .accountNo(accountNo2)
                .issuerId(issuer2.getId())
                .build();
        when(repository.findByAccountNoIn(List.of(accountNo1, accountNo2)))
                .thenReturn(List.of(billingAccount1, billingAccount2));
        var expenseCode1 = BillingAccountExpenseCode.builder()
                .billingAccountId(billingAccount1.getId())
                .specialExpenseCode("2")
                .build();
        var expenseCode2 = BillingAccountExpenseCode.builder()
                .billingAccountId(billingAccount2.getId())
                .specialExpenseCode("4")
                .build();
        when(expenseCodeRepository.findByBillingAccountIdIn(List.of(billingAccount1.getId(), billingAccount2.getId())))
                .thenReturn(List.of(expenseCode1, expenseCode2));

        service.processJdePriceEvent(priceEvent);

        assertEquals(parseUnitPrice(price2.getPrice()), expenseCode1.getUnitPrice());
        assertEquals(parseUnitPrice(price4.getPrice()), expenseCode2.getUnitPrice());
    }

    private static @Nullable BigDecimal parseUnitPrice(String unitPrice) {
        return NumberUtils.parseBigDecimal(unitPrice, 4, RoundingMode.HALF_UP);
    }

}