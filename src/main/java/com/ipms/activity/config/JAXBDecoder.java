package com.ipms.activity.config;

import feign.Response;
import feign.codec.Decoder;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;

public class JAXBDecoder implements Decoder {
    @Override
    public Object decode(Response response, Type type) throws IOException {
        try (InputStream inputStream = response.body().asInputStream()) {
            JAXBContext context = JAXBContext.newInstance((Class<?>) type);
            return context.createUnmarshaller().unmarshal(inputStream);
        } catch (JAXBException e) {
            throw new IOException(e);
        }
    }
}