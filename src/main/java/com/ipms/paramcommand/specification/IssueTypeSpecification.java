package com.ipms.paramcommand.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.paramcommand.model.IssueType;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class IssueTypeSpecification extends AbstractSpecification<IssueType> {
    private final List<String> types;
    private final List<String> activityTypes;

    @Override
    public Predicate toPredicate(Root<IssueType> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Optional.ofNullable(types)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("type").in(types))));
        Optional.ofNullable(activityTypes)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("activityType").in(activityTypes))));
        return predicate;
    }
}
