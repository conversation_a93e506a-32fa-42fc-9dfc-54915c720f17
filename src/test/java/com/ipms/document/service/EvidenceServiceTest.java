package com.ipms.document.service;

import com.ipms.config.storage.service.StorageService;
import com.ipms.document.client.ActivityClient;
import com.ipms.document.dto.*;
import com.ipms.document.enums.EvidenceType;
import com.ipms.document.enums.MediaType;
import com.ipms.document.exception.EvidenceCannotRemoveException;
import com.ipms.document.exception.EvidenceNotFoundException;
import com.ipms.document.mapper.EvidenceMapper;
import com.ipms.document.mapper.EvidenceMapperImpl;
import com.ipms.document.model.Evidence;
import com.ipms.document.model.EvidenceAttachment;
import com.ipms.document.repository.EvidenceRepository;
import com.ipms.document.service.impl.EvidenceServiceImpl;
import com.ipms.document.specification.EvidenceSpecification;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;

@RunWith(SpringRunner.class)
public class EvidenceServiceTest {

    @MockBean
    private EvidenceService service;

    @Mock
    private ActivityClient activityClient;

    @Mock
    private EvidenceRepository repository;

    @Mock
    private StorageService storageService;

    private final EvidenceMapper mapper = new EvidenceMapperImpl();

    @Before
    public void setUp() {
        service = new EvidenceServiceImpl(repository, mapper, activityClient, storageService);
    }

    @Test
    public void whenSaveWithAttachment_thenReturnEvidenceDto() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();
        var evidenceCreateDto = EvidenceCreateDto.builder()
                .activityId(1L)
                .contentType("image/png")
                .fileName("image001.png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .evidenceType("FINANCIAL_DATA")
                .mediaType("PDF")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(repository.save(Mockito.any(Evidence.class)))
                .thenReturn(evidence);
        Mockito.when(activityClient.addEvidence(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(ActivityResponse.builder()
                        .code(1)
                        .payload(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build())
                        .build());
        assertThat(service.save(evidenceCreateDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenSave_thenReturnEvidenceDto() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();

        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .build();
        Mockito.when(repository.save(Mockito.any(Evidence.class)))
                .thenReturn(evidence);
        Mockito.when(activityClient.addEvidence(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(ActivityResponse.builder()
                        .code(1)
                        .payload(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build())
                        .build());
        assertThat(service.save(evidenceDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenUpdate_thenReturnEvidenceDto() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();

        var evidenceDto = EvidenceDto.builder()
                .id(1L)
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .name("test-name")
                .description("test-description")
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(evidence));
        Mockito.when(repository.save(Mockito.any(Evidence.class)))
                .thenReturn(evidence);
        assertThat(service.update(evidenceDto, 1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenGetEvidenceById_thenReturnEvidence() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(() -> service.getById(1L));
        assertThat(thrown).isInstanceOf(EvidenceNotFoundException.class);
    }

    @Test
    public void whenGetByIdIn_thenReturnEvidenceDtoList() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();

        Mockito.when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(evidence));
        assertThat(service.getByIdIn(List.of(1L)).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenGetById_thenReturnEvidenceDto() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(evidence));
        AssertionsForClassTypes.assertThat(service.getById(1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenDelete_thenThrowEvidenceCannotRemoveException() {
        Mockito.when(activityClient.checkEvidenceLinked(Mockito.anyLong()))
                .thenReturn(ActivityLinkedResponse.builder()
                        .payload(List.of(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build()))
                        .build());
        Throwable thrown = catchThrowable(()->service.delete(1L,0L));
        assertThat(thrown).isInstanceOf(EvidenceCannotRemoveException.class);
    }

    @Test
    public void whenDelete_thenThrowDoesNotThrowAnyException() {
        Mockito.when(activityClient.checkEvidenceLinked(Mockito.anyLong()))
                .thenReturn(ActivityLinkedResponse.builder()
                        .payload(List.of())
                        .build());
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(evidence));
        assertThatCode(()->service.delete(1L,0L))
                .doesNotThrowAnyException();
    }

    @Test
    public void whenGetAll_thenReturnEvidencePageDto() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();
        var evidenceRequest = EvidenceFilterRequest.builder()
                .mediaTypes(List.of("DRAWING"))
                .evidenceTypes(List.of("AGREEMENT"))
                .ids(List.of(1L))
                .build();

        Page<Evidence> evidencePage = new PageImpl<>(List.of(evidence), Pageable.ofSize(1), 1);
        Mockito.when(repository.findAll(Mockito.any(EvidenceSpecification.class), Mockito.any(Pageable.class)))
                .thenReturn(evidencePage);
        AssertionsForClassTypes.assertThat(service.getAll(evidenceRequest, 1, 1).getEvidences().get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenGetByFileUniqueName_thenReturnEvidenceDto() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();
        Mockito.when(repository.findFirstByAttachmentsFileUniqueName(Mockito.anyString()))
                .thenReturn(Optional.of(evidence));
        AssertionsForClassTypes.assertThat(service.getByFileUniqueName("test-name").getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenGetByFileUniqueName_thenReturnEvidenceDtoList() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();
        Mockito.when(repository.findByAttachmentsFileUniqueNameIn(Mockito.anyList()))
                .thenReturn(List.of(evidence));
        AssertionsForClassTypes.assertThat(service.getByFileUniqueName(List.of("test-name")).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenUseEvidence_thenReturnEvidenceDto() {
        var attachment = EvidenceAttachment.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var evidence = Evidence.builder()
                .id(1L)
                .evidenceType(EvidenceType.AGREEMENT)
                .mediaType(MediaType.DRAWING)
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachment))
                .build();

        var attachmentDto = EvidenceAttachmentDto.builder()
                .fileName("test-file.png")
                .contentType("image/png")
                .fileUniqueName("a1b5f1c9e-3a50-4a15-869b-2e2e937384c1.png")
                .build();
        var createDto = EvidenceCreateDto.builder()
                .evidenceType("AGREEMENT")
                .mediaType("DRAWING")
                .firms(List.of(1L))
                .name("test-name")
                .description("test-description")
                .attachments(List.of(attachmentDto))
                .build();
        Mockito.when(repository.save(Mockito.any(Evidence.class)))
                .thenReturn(evidence);
        Mockito.when(activityClient.addEvidence(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(ActivityResponse.builder()
                        .code(1)
                        .payload(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build())
                        .build());
        assertThat(service.useEvidence(createDto).getId())
                .isEqualTo(1L);
    }
}
