eureka:
  client:
    enabled: false
server:
  servlet:
    context-path: /api/integration
keycloak:
  realm: ipms-default
  auth-server-url: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth
  ssl-required: external
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn

spring:
  cloud:
    config:
      enabled: false
  datasource:
    url: jdbc:h2:file:~/test
    username: sa
    password:
    driverClassName: org.h2.Driver
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth/realms/ipms-default/protocol/openid-connect/token

kafka:
  server: localhost:29092
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic