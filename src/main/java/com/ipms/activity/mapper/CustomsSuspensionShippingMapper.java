package com.ipms.activity.mapper;

import com.ipms.activity.dto.CustomsSuspensionsShippingDto;
import com.ipms.activity.model.CustomsSuspensionsShipping;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomsSuspensionShippingMapper {

    CustomsSuspensionsShippingDto toDto(CustomsSuspensionsShipping shipping);

    CustomsSuspensionsShipping toCustomsSuspensionsShipping(CustomsSuspensionsShippingDto dto);
}
