server:
  port: 8074
  servlet:
    context-path: /api/paramquery

app:
  allowed-origins: >
    *
  allowed-headers: >
    *
  allowed-methods: >
    GET,
    POST,
    PUT,
    PATCH,
    DELETE,
    OPTIONS,
    HEAD

spring:
  application:
    name: ipms-paramquery

springdoc:
  api-docs:
    path: /docs
  swagger-ui:
    path: /docs/ui
    tagsSorter: alpha
    operationsSorter: alpha

management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health,info,metrics"
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessState:
      enabled: true
    readinessState:
      enabled: true