package com.ipms.document.converter;

import com.ipms.document.enums.SampleLocation;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class SampleLocationConverter implements AttributeConverter<SampleLocation, String> {

    @Override
    public String convertToDatabaseColumn(SampleLocation sampleLocation) {
        return sampleLocation == null ? null : sampleLocation.getValue();
    }

    @Override
    public SampleLocation convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : SampleLocation.getEnum(s);
    }
}
