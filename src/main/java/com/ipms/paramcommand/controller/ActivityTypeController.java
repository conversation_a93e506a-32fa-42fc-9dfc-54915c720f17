package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.ActivityTypeDto;
import com.ipms.paramcommand.dto.FilterRequest;
import com.ipms.paramcommand.dto.groups.UpdateGroup;
import com.ipms.paramcommand.service.ActivityTypeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("activity-type")
@Tag(name = "activity-type", description = "This endpoints contains activity-type APIs")
@Validated
public class ActivityTypeController {

    private final ActivityTypeService service;

    @PostMapping
    public BaseResponse<ActivityTypeDto> save(@Valid @RequestBody ActivityTypeDto dto) {
        return BaseResponse.<ActivityTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping
    public BaseResponse<ActivityTypeDto> update(@Validated(UpdateGroup.class) @Valid @RequestBody ActivityTypeDto dto) {
        return BaseResponse.<ActivityTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(dto))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping
    public BaseResponse<List<ActivityTypeDto>> getList(FilterRequest filterRequest){
        return BaseResponse.<List<ActivityTypeDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest))
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<ActivityTypeDto> get(@PathVariable Long id) {
        return BaseResponse.<ActivityTypeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }
}
