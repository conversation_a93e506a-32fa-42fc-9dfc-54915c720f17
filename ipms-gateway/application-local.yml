eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/registry/eureka/
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${instanceId:${random.value}}
    preferIpAddress: true

spring:
  cloud:
    gateway:
      routes:
        - id: users
          uri: http://localhost:8010/api/users/**
          predicates:
            - Path=/api/users/**
        - id: users-swagger
          uri: http://localhost:8010/api/users/docs/**
          predicates:
            - Path=/api/users/docs/**

        - id: mail
          uri: http://localhost:8020/api/mail/**
          predicates:
            - Path=/api/mail/**

        - id: correspondence
          uri: http://localhost:8030/api/correspondence/**
          predicates:
            - Path=/api/correspondence/**

        - id: issue
          uri: http://localhost:8040/api/issue/**
          predicates:
            - Path=/api/issue/**

        - id: firm
          uri: http://localhost:8050/api/firm/**
          predicates:
            - Path=/api/firm/**

        - id: matter
          uri: http://localhost:8060/api/matter/**
          predicates:
            - Path=/api/matter/**

        - id: activity
          uri: http://localhost:8070/api/activity/**
          predicates:
            - Path=/api/activity/**

        - id: notification
          uri: http://localhost:8071/api/notification/**
          predicates:
            - Path=/api/notification/**

        - id: executor
          uri: http://localhost:8072/api/executor/**
          predicates:
            - Path=/api/executor/**

        - id: paramcommand
          uri: http://localhost:8073/api/paramcommand/**
          predicates:
            - Path=/api/paramcommand/**

        - id: paramquery
          uri: http://localhost:8074/api/paramquery/**
          predicates:
            - Path=/api/paramquery/**

        - id: timesheet
          uri: http://localhost:8075/api/timesheet/**
          predicates:
            - Path=/api/timesheet/**

        - id: transfer
          uri: http://localhost:8076/api/transfer/**
          predicates:
            - Path=/api/transfer/**

        - id: expense
          uri: http://localhost:8077/api/expense/**
          predicates:
            - Path=/api/expense/**

        - id: document
          uri: http://localhost:8078/api/document/**
          predicates:
            - Path=/api/document/**

        - id: quotation
          uri: http://localhost:8061/api/quotation/**
          predicates:
            - Path=/api/quotation/**

        - id: billing
          uri: http://localhost:8062/api/billing/**
          predicates:
            - Path=/api/billing/**

        - id: integration
          uri: http://localhost:8063/api/integration/**
          predicates:
            - Path=/api/integration/**