package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum TSICPackageResponseCode implements Code {
    START_DATE_MUST_NOT_BE_AFTER_THEN_END_DATE(
            1001, "error.code.activity.tsic_package.start_date_must_not_be_after_then_end_date"),
    START_DATE_MUST_BE_AFTER_THEN_LAST_PACKAGE_END_DATE(
            1002, "error.code.activity.tsic_package.start_date_must_be_after_then_last_package_end_date"),
    START_DATE_MUST_BE_AFTER_THEN_LAST_PACKAGE_START_DATE(
            1003, "error.code.activity.tsic_package.start_date_must_be_after_then_last_package_start_date"),
    OUTDATED_PACKAGE_CANNOT_BE_UPDATED(
            1004, "error.code.activity.tsic_package.outdated_package_cannot_be_updated"),
    UNBILLED_TIMESHEET_EXIST_BEFORE_START_DATE_WHEN_ADDING_NEW_PACKAGE(
            1005, "error.code.activity.tsic_package.unbilled_timesheet_exist_before_start_date_when_adding_new_package"),
    UNBILLED_TIMESHEET_EXIST_BETWEEN_START_AND_END_DATE_WHEN_UPDATING_PACKAGE(
            1006, "error.code.activity.tsic_package.unbilled_timesheet_exist_between_start_and_end_date_when_updating_package"),
    UNBILLED_TIMESHEET_EXIST_BEFORE_START_DATE_WHEN_UPDATING_PACKAGE(
            1007, "error.code.activity.tsic_package.unbilled_timesheet_exist_before_start_date_when_updating_package");


    private final Integer code;
    private final String messageKey;

    TSICPackageResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
