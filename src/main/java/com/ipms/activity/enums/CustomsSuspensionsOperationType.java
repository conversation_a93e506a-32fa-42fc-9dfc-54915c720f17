package com.ipms.activity.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum CustomsSuspensionsOperationType {

    IMPORT("01"),
    EXPORT("02"),
    TRANSIT("03"),
    PARALLEL_IMPORT("04");


    private final String value;

    CustomsSuspensionsOperationType(String value) {
        this.value = value;
    }

    public static CustomsSuspensionsOperationType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
