package com.ipms.activity.service.impl;

import com.ipms.activity.dto.PreliminaryInjunctionDto;
import com.ipms.activity.exception.PINotFoundException;
import com.ipms.activity.mapper.PreliminaryInjunctionMapper;
import com.ipms.activity.model.PreliminaryInjunction;
import com.ipms.activity.repository.PreliminaryInjunctionRepository;
import com.ipms.activity.service.PreliminaryInjunctionDetailService;
import com.ipms.activity.service.PreliminaryInjunctionService;
import com.ipms.activity.service.RefundedAttachmentService;
import com.ipms.activity.service.ReleasedAttachmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

@RequiredArgsConstructor
@Service
public class PreliminaryInjunctionServiceImpl implements PreliminaryInjunctionService {
    private final PreliminaryInjunctionRepository repository;
    private final PreliminaryInjunctionMapper mapper;
    private final ReleasedAttachmentService releasedAttachmentService;
    private final RefundedAttachmentService refundedAttachmentService;
    private final PreliminaryInjunctionDetailService detailService;

    @Override
    public PreliminaryInjunctionDto getById(Long id) {
        return mapper.toPreliminaryInjunctionDto(getPreliminaryInjunctionById(id));
    }

    @Override
    public PreliminaryInjunction getPreliminaryInjunctionById(Long id) {
        return repository.findById(id).orElseThrow(PINotFoundException::new);
    }

    @Override
    public void save(PreliminaryInjunction preliminaryInjunction, PreliminaryInjunctionDto preliminaryInjunctionDto) {
        var details = detailService.saveFromPreliminaryInjunction(preliminaryInjunctionDto, preliminaryInjunction);
        preliminaryInjunction.setDetails(details);
    }

    @Override
    public void update(PreliminaryInjunctionDto preliminaryInjunctionDto, PreliminaryInjunction preliminaryInjunction) {
        detailService.saveDetails(preliminaryInjunctionDto.getDetails(), preliminaryInjunction);
        releasedAttachmentService.saveAttachments(preliminaryInjunctionDto.getReleasedAttachments(), preliminaryInjunction);
        refundedAttachmentService.saveAttachments(preliminaryInjunctionDto.getRefundedAttachments(), preliminaryInjunction);
    }

    @Override
    public void setFileSignedUrls(PreliminaryInjunctionDto preliminaryInjunctionDto) {
        if (preliminaryInjunctionDto == null) {
            return;
        }
        Optional.ofNullable(preliminaryInjunctionDto.getRefundedAttachments()).ifPresent(attachmentDtos ->
                refundedAttachmentService.setFileSignedUrls(preliminaryInjunctionDto.getRefundedAttachments()));
        Optional.ofNullable(preliminaryInjunctionDto.getReleasedAttachments()).ifPresent(attachmentDtos ->
                releasedAttachmentService.setFileSignedUrls(preliminaryInjunctionDto.getReleasedAttachments()));
    }
}
