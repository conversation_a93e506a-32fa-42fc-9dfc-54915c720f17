package com.ipms.integration.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OrderValidateResponse {
    private ResponseInformation responseInformation;
    private Boolean validOrderorInvoice;

    @Getter
    @Setter
    public static class ResponseInformation {
        private Integer responseCount;
        private String errorCode;
        private String errorMessage;
    }

    public Boolean getValidOrderorInvoice() {
        return validOrderorInvoice != null && validOrderorInvoice;
    }
}
