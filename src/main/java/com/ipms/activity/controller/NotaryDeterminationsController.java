package com.ipms.activity.controller;

import com.ipms.activity.dto.NotaryDeterminationDto;
import com.ipms.activity.service.NotaryDeterminationService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("notary-determination")
public class NotaryDeterminationsController {
    private final NotaryDeterminationService service;

    public NotaryDeterminationsController(NotaryDeterminationService service) {
        this.service = service;
    }

    @GetMapping("/get-by-activityId/{activityId}")
    public BaseResponse<List<NotaryDeterminationDto>> getByActivityId(@PathVariable("activityId") Long activityId) {
        return BaseResponse.<List<NotaryDeterminationDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByActivityId(activityId))
                .build();
    }

    @GetMapping("/get-by-id/{id}")
    public BaseResponse<NotaryDeterminationDto> getById(@PathVariable("id") Long id) {
        return BaseResponse.<NotaryDeterminationDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }
}
