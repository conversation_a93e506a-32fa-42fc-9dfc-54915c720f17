package com.ipms.activity.converter;

import com.ipms.activity.enums.CourtActionType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class CourtActionTypeConverter implements AttributeConverter<CourtActionType, String> {
    @Override
    public String convertToDatabaseColumn(CourtActionType courtActionType) {
        return courtActionType == null ? null : courtActionType.getValue();
    }

    @Override
    public CourtActionType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : CourtActionType.getEnum(s);
    }
}
