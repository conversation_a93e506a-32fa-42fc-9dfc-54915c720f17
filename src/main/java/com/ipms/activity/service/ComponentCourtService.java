package com.ipms.activity.service;

import com.ipms.activity.dto.ComponentCourtDto;
import com.ipms.activity.dto.ComponentCourtFilterRequest;
import com.ipms.activity.model.ComponentCourt;

import java.util.List;

public interface ComponentCourtService {
    ComponentCourtDto save(ComponentCourtDto componentCourtDto);
    ComponentCourtDto update(ComponentCourtDto componentCourtDto, Long id);
    List<ComponentCourtDto> getByActivityId(Long activityId);
    List<ComponentCourtDto> getByActivityIds(List<Long> activityIds);
    ComponentCourt getByDocketNo(String docketNo);
    void delete(Long id, Long version);
    List<ComponentCourtDto> getAllFiltered(ComponentCourtFilterRequest componentCourtFilterRequest);
}
