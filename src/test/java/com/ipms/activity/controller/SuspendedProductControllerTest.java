package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.activity.dto.SuspendedProductDto;
import com.ipms.activity.dto.SuspendedProductPageDto;
import com.ipms.activity.service.SuspendedProductService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class SuspendedProductControllerTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private SuspendedProductService service;

    ObjectMapper objectMapper = new ObjectMapper();


    @Test
    public void givenId_whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong());
        mvc.perform(delete("/suspended-product/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var suspendedProductDto = SuspendedProductDto.builder()
                .activityId(1L)
                .number(123)
                .customsRecordalMatterId(1L)
                .build();
        Mockito.when(service.save(Mockito.any(SuspendedProductDto.class)))
                .thenReturn(suspendedProductDto);
        mvc.perform(post("/suspended-product")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(suspendedProductDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenActivityIdAndPageAndSize_whenGetAllByActivity_thenReturnBaseResponse() throws Exception {
        var suspendedProductDto = SuspendedProductDto.builder()
                .activityId(1L)
                .number(123)
                .customsRecordalMatterId(1L)
                .build();
        var suspendedProductPageDto = SuspendedProductPageDto.builder()
                .suspendedProductDtos(List.of(suspendedProductDto))
                .build();
        Mockito.when(service.getAllByActivity(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(suspendedProductPageDto);
        mvc.perform(get("/suspended-product/1/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetById_thenReturnBaseResponse() throws Exception {
        var suspendedProductDto = SuspendedProductDto.builder()
                .activityId(1L)
                .number(123)
                .customsRecordalMatterId(1L)
                .build();

        Mockito.when(service.getById(Mockito.anyLong()))
                .thenReturn(suspendedProductDto);
        mvc.perform(get("/suspended-product/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(suspendedProductDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}