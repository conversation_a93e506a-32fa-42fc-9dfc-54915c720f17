package com.ipms.billing.specification;

import com.ipms.billing.model.Timesheet;
import com.ipms.billing.repository.TimesheetRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/application.yml")
@Transactional
public class TimesheetSpecificationIntegrationTest {
    @Autowired
    private TimesheetRepository repository;

    @Before
    public void setUp() {
        var timesheet1 = Timesheet.builder()
                .isApproved(true)
                .activityId(1L)
                .matterId(1L)
                .isBillingApproved(true)
                .includedInCharge(true)
                .build();
        var timesheet2 = Timesheet.builder()
                .activityId(2L)
                .matterId(2L)
                .isBillingApproved(true)
                .includedInCharge(true)
                .build();

        repository.saveAll(List.of(timesheet1, timesheet2));
    }

    @Test
    public void whenFindAll_thenReturnTimesheets() {
        var specification = TimesheetSpecification.builder()
                .isApproved(true)
                .activityId(1L)
                .activityIdsNotIn(List.of(2L))
                .activityIds(List.of(1L))
                .matterIds(List.of(1L))
                .createdBys(List.of("system"))
                .build();

        var timesheets = repository.findAll(specification);

        Assertions.assertEquals(1, timesheets.size());
    }

    @Test
    public void whenFindAll_ActivityIdsNotIn_thenReturnEmpty() {
        var specification = TimesheetSpecification.builder()
                .activityIdsNotIn(List.of(1L))
                .activityId(1L)
                .build();

        var timesheets = repository.findAll(specification);

        Assertions.assertEquals(0, timesheets.size());
    }
}