package com.ipms.activity.controller;

import com.ipms.activity.dto.CounterRightOwnerDto;
import com.ipms.activity.service.CounterRightOwnerService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("counter-right-owner")
@Tag(name = "counterRightOwner", description = "This endpoint contains counter right owner APIs ")
@Validated
public class CounterRightOwnerController {

    private final CounterRightOwnerService service;

    @PostMapping
    public BaseResponse<CounterRightOwnerDto> save(@RequestBody @Valid CounterRightOwnerDto dto) {
        return BaseResponse.<CounterRightOwnerDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<CounterRightOwnerDto> save(@PathVariable Long id, @RequestBody @Valid CounterRightOwnerDto dto) {
        return BaseResponse.<CounterRightOwnerDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(id, dto))
                .build();
    }

    @GetMapping("/{name}")
    public BaseResponse<List<CounterRightOwnerDto>> getByName(@PathVariable @Size(min = 3) String name) {
        return BaseResponse.<List<CounterRightOwnerDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByName(name))
                .build();
    }

    @GetMapping("/get-matter-ids/{id}")
    public BaseResponse<List<Long>> getActivityIds(@PathVariable Long id){
        return BaseResponse.<List<Long>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getActivityIds(id))
                .build();
    }
}
