package com.ipms.document.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.document.dto.SampleAttachmentDto;
import com.ipms.document.service.SampleAttachmentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RequiredArgsConstructor
@RestController
@RequestMapping("/sample-attachment")
@Tag(name = "sample-attachment", description = "This endpoint contains sample-attachment APIs")
public class SampleAttachmentController {

    private final SampleAttachmentService service;

    @PostMapping("/")
    public BaseResponse<SampleAttachmentDto> save(@Valid @RequestBody SampleAttachmentDto attachmentDto) {
        var dto = service.save(attachmentDto);
        return BaseResponse.<SampleAttachmentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
