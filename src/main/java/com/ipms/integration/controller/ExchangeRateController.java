package com.ipms.integration.controller;


import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.integration.dto.ExchangeRateDto;
import com.ipms.integration.service.ExchangeRateService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/exchange-rate")
@Tag(name = "exchange rate", description = "This endpoint contains exchange rate APIs ")
public class ExchangeRateController {
    private final ExchangeRateService service;

    @GetMapping("/{updateDate}")
    public BaseResponse<List<ExchangeRateDto>> fetchExchangeRates(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate updateDate) {
        return BaseResponse.<List<ExchangeRateDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.fetchExchangeRates(updateDate))
                .build();
    }

    @GetMapping
    public BaseResponse<List<ExchangeRateDto>> fetchAllExchangeRates() {
        return BaseResponse.<List<ExchangeRateDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.fetchAllExchangeRates())
                .build();
    }
    
}
