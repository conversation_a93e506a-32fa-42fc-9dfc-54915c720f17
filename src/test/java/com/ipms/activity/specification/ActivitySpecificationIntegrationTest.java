package com.ipms.activity.specification;

import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.model.Activity;
import com.ipms.activity.repository.ActivityRepository;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Predicate;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class ActivitySpecificationIntegrationTest {

    @Autowired
    private ActivityRepository repository;

    @Before
    public void givenMatters() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .billingAccountId(1L)
                .build();
        repository.save(activity);
    }

    @Test
    public void whenFindAll_thenReturnActivityPage() {
        var activities = repository.findAll(ActivitySpecification.builder()
                .types(List.of(ActivityType.CUSTOMS.name()))
                .statuses(List.of(ActivityStatus.ABANDONMENT.name()))
                .billingAccountIds(List.of(1L))
                .build(), PageRequest.of(0, 100));
        Predicate<List<Activity>> predicate = list -> list.size() == 1 && list.get(0).getAgentReference().equals("test-ref");
        AssertionsForClassTypes.assertThat(activities.getContent()).matches(predicate, "list size must be 1 and AgentReference test-ref");
    }
}
