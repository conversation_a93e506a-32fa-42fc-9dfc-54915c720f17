package com.ipms.document.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.document.dto.EvidenceCreateDto;
import com.ipms.document.dto.EvidenceDto;
import com.ipms.document.dto.EvidenceFilterRequest;
import com.ipms.document.dto.EvidencePageDto;
import com.ipms.document.dto.groups.CreateGroup;
import com.ipms.document.dto.groups.UpdateGroup;
import com.ipms.document.dto.groups.UseEvidenceGroup;
import com.ipms.document.service.EvidenceService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/evidence")
@Tag(name = "evidence", description = "This endpoint contains evidence APIs ")
public class EvidenceController {

    private final EvidenceService service;

    @PostMapping
    public BaseResponse<EvidenceDto> save(@Validated(CreateGroup.class) @RequestBody EvidenceDto evidenceDto) {
        var dto = service.save(evidenceDto);
        return BaseResponse.<EvidenceDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PostMapping("/with-attachment")
    public BaseResponse<EvidenceDto> saveWithAttachment(@Validated(CreateGroup.class) @RequestBody EvidenceCreateDto evidenceCreateDto) {
        var dto = service.save(evidenceCreateDto);
        return BaseResponse.<EvidenceDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PostMapping("/use-evidence")
    public BaseResponse<EvidenceDto> useEvidence(@Validated(UseEvidenceGroup.class) @RequestBody EvidenceCreateDto evidenceCreateDto) {
        var dto = service.useEvidence(evidenceCreateDto);
        return BaseResponse.<EvidenceDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<EvidenceDto> update(@PathVariable Long id,
                                            @Validated(UpdateGroup.class) @RequestBody EvidenceDto evidenceDto) {
        var dto = service.update(evidenceDto, id);
        return BaseResponse.<EvidenceDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<EvidenceDto> get(@PathVariable Long id) {
        var dto = service.getById(id);
        return BaseResponse.<EvidenceDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<EvidenceDto>> getByIds(@PathVariable List<Long> ids) {
        return BaseResponse.<List<EvidenceDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIdIn(ids))
                .build();
    }

    @GetMapping("/file/{name}")
    public BaseResponse<EvidenceDto> getByName(@PathVariable String name) {
        var dto = service.getByFileUniqueName(name);
        return BaseResponse.<EvidenceDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping("/files/{names}")
    public BaseResponse<List<EvidenceDto>> getByNames(@PathVariable List<String> names) {
        return BaseResponse.<List<EvidenceDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByFileUniqueName(names))
                .build();
    }

    @GetMapping("/evidences/{page}/{size}")
    public BaseResponse<EvidencePageDto> getAll(EvidenceFilterRequest request,
                                                      @PathVariable int page,
                                                      @PathVariable int size) {
        return BaseResponse.<EvidencePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(request, page, size))
                .build();
    }
}
