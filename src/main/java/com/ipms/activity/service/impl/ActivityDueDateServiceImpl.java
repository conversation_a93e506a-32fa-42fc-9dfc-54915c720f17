package com.ipms.activity.service.impl;

import com.ipms.activity.client.MatterClient;
import com.ipms.activity.client.ParamCommandClient;
import com.ipms.activity.dto.ActivityTypeDueDateResponse;
import com.ipms.activity.dto.MatterDto;
import com.ipms.activity.dto.trademark.MarkDto;
import com.ipms.activity.dto.trademark.RegistrationDto;
import com.ipms.activity.enums.ActivityBaseDateType;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.BulletinRelatedActivity;
import com.ipms.activity.repository.ActivityRepository;
import com.ipms.activity.service.ActivityDueDateService;
import com.ipms.core.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class ActivityDueDateServiceImpl implements ActivityDueDateService {
    private final MatterClient matterClient;
    private final ParamCommandClient paramCommandClient;
    private final ActivityRepository repository;

    @Override
    public void updateStatusDueDate(Activity existingActivity, Activity updated) {
        if (isActivityStatusChanged(existingActivity, updated)) {
            calculateAndSetStatusDueDate(updated);
        }
        else {
            var dueDateFormula = getDueDateFormula(updated);
            if (dueDateFormula != null) {
                var baseDateType = dueDateFormula.getBaseDate();

                if (isBaseDateChanged(existingActivity, updated, baseDateType)) {
                    calculateAndSetStatusDueDate(updated);
                }
            }
            else {
                updated.setStatusDueDate(null);
            }
        }
        existingActivity.setStatusDueDate(updated.getStatusDueDate());
    }

    private boolean isActivityStatusChanged(Activity existingActivity, Activity updated) {
        return !Objects.equals(existingActivity.getStatus(), updated.getStatus());
    }

    private boolean isBaseDateChanged(Activity existingActivity,
                                      Activity updated,
                                      ActivityBaseDateType baseDateType) {

        if (baseDateType == ActivityBaseDateType.APPLICATION_DATE) {
            return false;
        }

        var existingLinked = getBulletinReleatedActivity(existingActivity);
        var updatedLinked = getBulletinReleatedActivity(updated);

        if (existingLinked == null || updatedLinked == null) return false;

        return switch (baseDateType) {
            case BULLETIN_DATE -> hasDateChanged(
                    existingLinked.getBulletinPublicationDate(),
                    updatedLinked.getBulletinPublicationDate());

            case NOTIFICATION_DATE -> hasDateChanged(
                    existingLinked.getNotificationDate(),
                    updatedLinked.getNotificationDate());

            case DECISION_DATE -> hasDateChanged(
                    existingLinked.getDecisionDate(),
                    updatedLinked.getDecisionDate());

            default -> false;
        };
    }

    private boolean hasDateChanged(LocalDate oldDate, LocalDate newDate) {
        return !Objects.equals(oldDate, newDate);
    }

    private LocalDate extractBaseDate(BulletinRelatedActivity bulletinLinkedActivity,
                                      ActivityBaseDateType baseDateType,
                                      LocalDate applicationDate) {
        if (bulletinLinkedActivity == null) {
            return null;
        }

        return switch (baseDateType) {
            case BULLETIN_DATE -> bulletinLinkedActivity.getBulletinPublicationDate();
            case NOTIFICATION_DATE -> bulletinLinkedActivity.getNotificationDate();
            case APPLICATION_DATE -> applicationDate;
            case DECISION_DATE -> bulletinLinkedActivity.getDecisionDate();
        };
    }

    private LocalDate getApplicationDate(MatterDto matterDto) {
        return Optional.ofNullable(matterDto.getMark())
                .map(MarkDto::getRegistration)
                .map(RegistrationDto::getApplicationDate)
                .orElse(null);
    }

    private BulletinRelatedActivity getBulletinReleatedActivity(Activity activity) {
        if (activity.getOppositionOut() != null) {
            return activity.getOppositionOut();
        }

        if (activity.getRegistration() != null) {
            return activity.getRegistration();
        }

        return null;
    }

    @Override
    public void processApplicationDateChange(List<Activity> activities) {
        if (activities.isEmpty()) {
            return;
        }

        for (Activity activity : activities) {
            var dueDateFormula = getDueDateFormula(activity);
            if (dueDateFormula != null && dueDateFormula.getBaseDate() == ActivityBaseDateType.APPLICATION_DATE) {
                calculateAndSetStatusDueDate(activity);
                repository.save(activity);
            }
        }
    }

    private ActivityTypeDueDateResponse.ActivityDueDateDto getDueDateFormula(Activity activity) {
        var matterDto = matterClient.getById(activity.getMatterId()).getPayload();
        return paramCommandClient.getLatestByMatterTypeActivityAndStatus(
                matterDto.getType(),
                activity.getType().name(),
                activity.getStatus().name()
        ).getPayload();
    }

    @Override
    public void calculateAndSetStatusDueDate(Activity activity) {
        var matterDto = matterClient.getById(activity.getMatterId()).getPayload();

        var dueDateFormula = getDueDateFormula(activity);
        if(dueDateFormula == null) {
            activity.setStatusDueDate(null);
            return;
        }

        var bulletinLinkedActivity = getBulletinReleatedActivity(activity);
        if (bulletinLinkedActivity == null) {
            activity.setStatusDueDate(null);
            return;
        }

        var baseDate = extractBaseDate(
                bulletinLinkedActivity,
                dueDateFormula.getBaseDate(),
                getApplicationDate(matterDto)
        );

        var dueDate = (baseDate != null) ? switch (dueDateFormula.getPeriod()) {
            case MONTH -> baseDate.plusMonths(dueDateFormula.getPeriodValue());
            case DAY -> baseDate.plusDays(dueDateFormula.getPeriodValue());
        } : null;

        if (dueDate != null && !paramCommandClient.isWorkingDay(dueDate).isPayload()) {
            List<LocalDate> holidayDates = paramCommandClient.getHolidayList().getPayload();
            dueDate = DateUtils.nextWorkDay(dueDate, holidayDates);
        }

        activity.setStatusDueDate(dueDate);
    }
}
