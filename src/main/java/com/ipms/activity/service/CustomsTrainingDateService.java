package com.ipms.activity.service;

import com.ipms.activity.dto.ActivityId;
import com.ipms.activity.dto.CustomsTrainingDateDto;
import com.ipms.activity.dto.CustomsTrainingDateFilterRequest;
import com.ipms.activity.dto.CustomsTrainingDatePageDto;

import java.util.List;

public interface CustomsTrainingDateService {

    CustomsTrainingDateDto save(CustomsTrainingDateDto customsTrainingDateDto);

    CustomsTrainingDateDto update(CustomsTrainingDateDto customsTrainingDateDto, Long id);

    void delete(Long id);

    CustomsTrainingDateDto getById(Long id);

    CustomsTrainingDatePageDto getAll(CustomsTrainingDateFilterRequest filterRequest, int page, int size);

    List<ActivityId> getByCustoms(Long customsId);
}
