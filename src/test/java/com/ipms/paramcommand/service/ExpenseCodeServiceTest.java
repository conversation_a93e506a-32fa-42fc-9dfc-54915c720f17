package com.ipms.paramcommand.service;

import com.ipms.paramcommand.dto.ExpenseDto;
import com.ipms.paramcommand.dto.PriceDto;
import com.ipms.paramcommand.dto.PriceEvent;
import com.ipms.paramcommand.enums.ExpenseCodeType;
import com.ipms.paramcommand.exception.ExpenseCodeNotFoundException;
import com.ipms.paramcommand.mapper.ExpenseCodeMapper;
import com.ipms.paramcommand.mapper.ExpenseCodeMapperImpl;
import com.ipms.paramcommand.model.CurrencyUnitPrice;
import com.ipms.paramcommand.model.ExpenseCode;
import com.ipms.paramcommand.repository.ExpenseCodeRepository;
import com.ipms.paramcommand.service.impl.ExpenseCodeServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ExpenseCodeServiceTest {

    @InjectMocks
    private ExpenseCodeServiceImpl service;

    @Mock
    private ExpenseCodeRepository repository;

    @Mock
    private ExpenseCodeMapper mapper = new ExpenseCodeMapperImpl();

    @Captor
    private ArgumentCaptor<List<ExpenseCode>> saveAllCaptor;

    @Test
    void getDiscountEligibleExpenseIds_shouldReturnEligibleExpenseIds() {
        Long eligibleExpenseId1 = 1L;
        Long eligibleExpenseId2 = 2L;
        Long nonEligibleExpenseId1 = 3L;
        Long nonEligibleExpenseId2 = 4L;

        var eligibleExpense1 = ExpenseDto.builder()
                .id(eligibleExpenseId1)
                .expenseCode("ABC123")
                .currency("USD")
                .build();

        var eligibleExpense2 = ExpenseDto.builder()
                .id(eligibleExpenseId2)
                .expenseCode("DEF456")
                .currency("EUR")
                .build();

        var nonEligibleExpense1 = ExpenseDto.builder()
                .id(nonEligibleExpenseId1)
                .expenseCode("GHI789Z")
                .currency("USD")
                .build();

        var nonEligibleExpense2 = ExpenseDto.builder()
                .id(nonEligibleExpenseId2)
                .expenseCode("JKL012")
                .currency("GBP")
                .build();

        List<ExpenseDto> expenses = List.of(eligibleExpense1, eligibleExpense2, nonEligibleExpense1, nonEligibleExpense2);

        var eligibleExpenseCode1 = ExpenseCode.builder()
                .code("ABC123")
                .type(ExpenseCodeType.SERVICE_FEE)
                .currencyUnitPrices(List.of(
                    CurrencyUnitPrice.builder()
                        .currency("USD")
                        .unitPrice(new BigDecimal("10.50"))
                        .build()
                ))
                .build();

        var eligibleExpenseCode2 = ExpenseCode.builder()
                .code("DEF456")
                .type(ExpenseCodeType.SERVICE_FEE)
                .currencyUnitPrices(List.of(
                    CurrencyUnitPrice.builder()
                        .currency("EUR")
                        .unitPrice(new BigDecimal("15.75"))
                        .build()
                ))
                .build();

        var nonEligibleExpenseCode1 = ExpenseCode.builder()
                .code("GHI789Z")
                .type(ExpenseCodeType.SERVICE_FEE)
                .currencyUnitPrices(List.of(
                    CurrencyUnitPrice.builder()
                        .currency("USD")
                        .unitPrice(new BigDecimal("20.00"))
                        .build()
                ))
                .build();

        var nonEligibleExpenseCode2 = ExpenseCode.builder()
                .code("JKL012")
                .type(ExpenseCodeType.OFFICIAL_FEE)
                .currencyUnitPrices(List.of(
                    CurrencyUnitPrice.builder()
                        .currency("GBP")
                        .unitPrice(new BigDecimal("25.00"))
                        .build()
                ))
                .build();

        when(repository.findByCode("ABC123")).thenReturn(Optional.of(eligibleExpenseCode1));
        when(repository.findByCode("DEF456")).thenReturn(Optional.of(eligibleExpenseCode2));
        when(repository.findByCode("GHI789Z")).thenReturn(Optional.of(nonEligibleExpenseCode1));
        when(repository.findByCode("JKL012")).thenReturn(Optional.of(nonEligibleExpenseCode2));

        List<Long> result = service.getDiscountEligibleExpenseIds(expenses);

        assertThat(result)
                .hasSize(2)
                .contains(eligibleExpenseId1, eligibleExpenseId2)
                .doesNotContain(nonEligibleExpenseId1, nonEligibleExpenseId2);
    }

    @Test
    void getDiscountEligibleExpenseIds_shouldHandleEmptyList() {
        List<ExpenseDto> emptyExpenses = List.of();
        List<Long> result = service.getDiscountEligibleExpenseIds(emptyExpenses);
        assertThat(result).isEmpty();
    }

    @Test
    void getDiscountEligibleExpenseIds_shouldThrowExceptionWhenExpenseCodeNotFound() {
        var expense = ExpenseDto.builder()
                .id(1L)
                .expenseCode("NONEXISTENT")
                .currency("USD")
                .build();

        when(repository.findByCode("NONEXISTENT")).thenReturn(Optional.empty());

        assertThatThrownBy(() -> service.getDiscountEligibleExpenseIds(List.of(expense)))
                .isInstanceOf(ExpenseCodeNotFoundException.class);
    }

    @Test
    void saveJdePrices() {
        var price1 = PriceDto.builder()
                .itemNo("1")
                .price("12.3456")
                .currencyCode("TL")
                .fromDate("2024-12-01")
                .toDate("2024-12-10")
                .customerorSupplierNumber("0")
                .build();
        var price2 = PriceDto.builder()
                .itemNo("1")
                .price("13.3456")
                .currencyCode("USD")
                .fromDate("2024-12-02")
                .toDate("2024-12-11")
                .customerorSupplierNumber("0")
                .build();
        var price3 = PriceDto.builder()
                .itemNo("2")
                .toDate("2024-12-02")
                .price("13.4567")
                .currencyCode("TL")
                .fromDate("2024-12-03")
                .toDate("2024-12-12")
                .customerorSupplierNumber("0")
                .build();
        var price4 = PriceDto.builder()
                .itemNo("3")
                .toDate("2024-12-02")
                .price("14.4567")
                .currencyCode("TL")
                .fromDate("2024-12-03")
                .toDate("2024-12-12")
                .customerorSupplierNumber("0")
                .build();
        var priceEvent = PriceEvent.builder()
                .priceLists(List.of(price1, price2, price3, price4))
                .build();

        var currency1 = CurrencyUnitPrice.builder()
                .currency("TL")
                .unitPrice(BigDecimal.ONE)
                .build();
        var expenseCode1 = ExpenseCode.builder()
                .code("1")
                .currencyUnitPrices(new ArrayList<>(List.of(currency1)))
                .build();
        var expenseCode2 = ExpenseCode.builder()
                .code("2")
                .currencyUnitPrices(new ArrayList<>(List.of()))
                .build();
        var expenseCodes = new ArrayList<>(List.of(expenseCode1, expenseCode2));
        when(repository.findByCodeIn(anyList())).thenReturn(expenseCodes);

        Mockito.when(repository.saveAll(saveAllCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));


        service.processJdePriceEvent(priceEvent);

        assertThat(expenseCode1.getExpiryDate()).isEqualTo(LocalDate.parse("2024-12-11"));
        assertThat(expenseCode2.getExpiryDate()).isEqualTo(LocalDate.parse("2024-12-12"));
        assertThat(saveAllCaptor.getValue()).hasSize(3);
        assertThat(expenseCode1.getCurrencyUnitPrices()).hasSize(2);
        assertThat(expenseCode1.getCurrencyUnitPrices().get(0).getUnitPrice())
                .isEqualTo(new BigDecimal("12.3456"));
        assertThat(expenseCode1.getCurrencyUnitPrices().get(1).getUnitPrice())
                .isEqualTo(new BigDecimal("13.3456"));
        assertThat(saveAllCaptor.getValue().get(2).getCurrencyUnitPrices()).hasSize(1);
    }
}
