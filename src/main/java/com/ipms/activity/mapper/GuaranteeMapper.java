package com.ipms.activity.mapper;

import com.ipms.activity.dto.GuaranteeDto;
import com.ipms.activity.model.Guarantee;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface GuaranteeMapper {
    Guarantee toGuarantee(GuaranteeDto dto);

    GuaranteeDto toGuaranteeDto(Guarantee guarantee);

    @Mapping(target = "dateOfDepositToCourt", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "partyProvidingGuarantee", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    Guarantee toGuaranteeFromDto(GuaranteeDto dto, @MappingTarget Guarantee guarantee);
}
