package com.ipms.activity.converter;

import com.ipms.activity.enums.CriminalActionStage;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class CriminalActionStageConverter implements AttributeConverter<CriminalActionStage, String> {

    @Override
    public String convertToDatabaseColumn(CriminalActionStage deliveryType) {
        return deliveryType == null ? null : deliveryType.getValue();
    }

    @Override
    public CriminalActionStage convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : CriminalActionStage.getEnum(s);
    }
}
