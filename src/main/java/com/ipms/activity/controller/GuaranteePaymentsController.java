package com.ipms.activity.controller;

import com.ipms.activity.dto.GuaranteePaymentDto;
import com.ipms.activity.service.GuaranteePaymentService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Positive;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("guarantee-payments")
public class GuaranteePaymentsController {

    private final GuaranteePaymentService service;

    @PostMapping("/")
    public BaseResponse<GuaranteePaymentDto> save(@RequestBody @Valid GuaranteePaymentDto dto) {
        return BaseResponse.<GuaranteePaymentDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/get/{id}")
    public BaseResponse<GuaranteePaymentDto> getById(@PathVariable Long id) {
        return BaseResponse.<GuaranteePaymentDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

    @GetMapping("/get-all-by-guarantee-id/{id}")
    public BaseResponse<List<GuaranteePaymentDto>> getAllByPreliminaryInjunctionId(@PathVariable Long id) {
        return BaseResponse.<List<GuaranteePaymentDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByGuaranteeId(id))
                .build();
    }

    @GetMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable @Positive Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
