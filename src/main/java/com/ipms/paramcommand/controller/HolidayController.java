package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.HolidayDto;
import com.ipms.paramcommand.dto.groups.UpdateGroup;
import com.ipms.paramcommand.service.HolidayService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("holiday")
@Tag(name = "holiday", description = "This endpoints contains holiday APIs")
public class HolidayController {
    private final HolidayService service;

    @PostMapping
    public BaseResponse<HolidayDto> save(@Valid @RequestBody HolidayDto dto) {
        return BaseResponse.<HolidayDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<HolidayDto> update(@Validated(UpdateGroup.class) @RequestBody HolidayDto dto,
                                            @PathVariable Long id) {
        return BaseResponse.<HolidayDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(dto, id))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/check")
    public BaseResponse<Boolean> check(@RequestParam(name = "date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.checkHoliday(date))
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/year")
    public BaseResponse<List<HolidayDto>> getYearList() {
        var start = LocalDate.of(LocalDate.now().getYear(), 1,1);
        var end = LocalDate.of(LocalDate.now().getYear(), 12,31);
        return BaseResponse.<List<HolidayDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getBetweenDate(start,end))
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/work-date")
    public BaseResponse<LocalDate> getWorkDate(@RequestParam(name = "date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime date,
                                               @RequestParam(name = "dueDay") int dueDay) {
        return BaseResponse.<LocalDate>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.determineWorkDay(date, dueDay))
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/is-working-day/{date}")
    public BaseResponse<Boolean> isWorkingDay(@PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.isWorkingDay(date))
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/previous-work-day/{date}")
    public BaseResponse<LocalDate> getPreviousWorkDay(@PathVariable LocalDate date) {
        return BaseResponse.<LocalDate>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.previousWorkingDay(date))
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/holidays")
    public BaseResponse<List<LocalDate>> getHolidayList() {
        return BaseResponse.<List<LocalDate>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getHolidayDates())
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
