package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "activity")
@Entity
@Where(clause = "is_deleted='0'")
public class SearchActivity extends VersionedEntity {

    @Column
    private ActivityType type;

    @Column
    private ActivityStatus status;

    @Column
    private LocalDate instructionDate;

    @Column
    private Long firmId;

    @Column
    private Long matterId;

    @Column
    private String agentReference;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    private CriminalAction criminalAction;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    private CustomsSuspension customsSuspension;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    private CustomsRecordal customsRecordal;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    private CDLetter cdLetter;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonIgnore
    private UseInvestigation useInvestigation;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    private CourtAction courtAction;

}
