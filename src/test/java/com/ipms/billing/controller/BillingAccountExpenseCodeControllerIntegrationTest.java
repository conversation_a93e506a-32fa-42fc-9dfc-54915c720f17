package com.ipms.billing.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.billing.dto.BillingAccountExpenseCodeDto;
import com.ipms.billing.dto.BillingAccountExpenseCodeFilterRequest;
import com.ipms.billing.service.BillingAccountExpenseCodeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import=classpath:/application.yml")
class BillingAccountExpenseCodeControllerIntegrationTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BillingAccountExpenseCodeService service;

    ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    private BillingAccountExpenseCodeDto billingAccountExpenseCodeDto;

    @BeforeEach
    void setUp() {
        billingAccountExpenseCodeDto = BillingAccountExpenseCodeDto.builder()
                .billingAccountId(1L)
                .countryCode("TR")
                .protectionType("02")
                .activityType("100")
                .generalExpenseCode("11111")
                .build();
    }

    @Test
    void whenGetAll_thenReturnBaseResponse() throws Exception {
        when(service.getAll(any(BillingAccountExpenseCodeFilterRequest.class)))
                .thenReturn(List.of(billingAccountExpenseCodeDto, billingAccountExpenseCodeDto));

        mockMvc.perform(get("/billing-account-expense-code/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
