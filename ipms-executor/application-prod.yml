eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:prod-ipms-executor}.prod-ipms-executor
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

spring:
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ${keycloak_ipms_backend_client_secret}
        provider:
          keycloak:
            token-uri: http://keycloak.keycloak/auth/realms/ipms-production/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-production
  auth-server-url: http://keycloak.keycloak/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ${keycloak_ipms_backend_client_secret}

cron:
  mail:
    default-folder: 0 * * ? * *
    ipms-folder: 0 * * ? * *
    ipms-document-folder: 0 * * ? * *
    rollback-all: 0,10,20,30,40,50 * * * * *
    retry-all: 0 0 * ? * *
  transfer:
    send-all: 0 */10 * ? * *
  billing:
    billingProcess: 0 0 1 ? * *
    billingProcessByBillingAmount: 0 30 1 ? * *
    billingProcessByStatus: 0 45 1 ? * *
    autoDeleteUnapprovedPayments: 0 45 20 * * *
  timesheet:
    approveBySystem: 0 0 9,20 * * *
  integration:
    fetch-exchange-rates: 0 0 * * * *
    send-billing-order: 0 */10 * ? * *
    update-external-order-number: 0 */10 * ? * *
    fetch-invoices: 0 */10 * ? * *
    fetch-prices: '-'

jde:
  main-company: "00004"
  minus-day-invoice: 7
  minus-day-price: 1