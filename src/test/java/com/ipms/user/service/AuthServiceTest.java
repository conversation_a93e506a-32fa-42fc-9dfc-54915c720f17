package com.ipms.user.service;

import com.ipms.user.dto.auth.AuthDto;
import com.ipms.user.dto.keycloak.KeycloakTokenDto;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;
import com.ipms.user.exception.AuthenticationFailedException;
import com.ipms.user.mapper.AuthDtoMapper;
import com.ipms.user.service.impl.AuthServiceImpl;
import com.ipms.user.service.keycloak.KeycloakAuthService;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.*;

@RunWith(SpringRunner.class)
public class AuthServiceTest {
    @MockBean
    private AuthServiceImpl service;

    @Mock
    private KeycloakAuthService keycloakTokenService;

    @Mock
    private AuthDtoMapper authDtoMapper;

    @Before
    public void setUp() {
        service = new AuthServiceImpl(keycloakTokenService,authDtoMapper);
        ReflectionTestUtils.setField(service, "realm", "90");
        ReflectionTestUtils.setField(service, "clientId", "testClientId");
        ReflectionTestUtils.setField(service, "clientNo", "clientNo");
    }

    @Test
    public void givenActiveUser_whenGetAccessToken_thenReturnAuthDto() {
        var tokenDto = new KeycloakTokenDto();
        tokenDto.setAccessToken("accesstoken");
        Mockito.when(keycloakTokenService.getAccessToken(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),Mockito.anyString()))
                .thenReturn(tokenDto);

        var authDto =new AuthDto();
        authDto.setAccessToken("accesstoken");
        Mockito.when(authDtoMapper.keycloakTokenDtoToAuthDto(Mockito.any(KeycloakTokenDto.class)))
                        .thenReturn(authDto);

        assertThat(service.getAccessToken("test", "test","clientId", "otp").getAccessToken())
                .isEqualTo("accesstoken");
    }


    @Test
    public void givenNotExistUser_whenGetAccessToken_thenThrowAuthenticationFailedException() {
        var tokenDto = new KeycloakTokenDto();
        tokenDto.setAccessToken("accesstoken");
        Mockito.when(keycloakTokenService.getAccessToken(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(),Mockito.anyString()))
                .thenReturn(null);

        Throwable thrown = catchThrowable(()->service.getAccessToken("test", "test","clientId","otp"));
        assertThat(thrown).isInstanceOf(AuthenticationFailedException.class);
    }

    @Test
    public void whenGetRefreshToken_thenReturnAuthDto() {
        var tokenDto = new KeycloakTokenDto();
        tokenDto.setRefreshToken("refreshtoken");
        Mockito.when(keycloakTokenService.getRefreshToken(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(tokenDto);

        var authDto =new AuthDto();
        authDto.setRefreshToken("refreshtoken");
        Mockito.when(authDtoMapper.keycloakTokenDtoToAuthDto(Mockito.any(KeycloakTokenDto.class)))
                .thenReturn(authDto);
        assertThat(service.getRefreshToken("test", "clientId").getRefreshToken())
                .isEqualTo("refreshtoken");
    }

    @Test
    public void whenLogout_thenDoesNotThrowAnyException() {
        var tokenDto = new KeycloakTokenDto();
        tokenDto.setRefreshToken("refreshtoken");
        Mockito.when(keycloakTokenService.getRefreshToken(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(tokenDto);
        assertThatCode(() -> service.logout("test", "test","clientId"))
                .doesNotThrowAnyException();
    }

    @Test
    public void whenGetAccessToken_thenReturnAuthDto(){
        KeycloakTokenDto tokenDto = new KeycloakTokenDto();
        tokenDto.setAccessToken("accessToken");
        Mockito.when(keycloakTokenService.getAccessToken(Mockito.any()))
                .thenReturn(tokenDto);
        var authDto = new AuthDto();
        authDto.setAccessToken("accessToken");
        Mockito.when(authDtoMapper.keycloakTokenDtoToAuthDto(Mockito.any(KeycloakTokenDto.class)))
                .thenReturn(authDto);
        assertThat(service.getAccessToken().getAccessToken())
                .isEqualTo("accessToken");
    }

    @Test
    public void whenGetAccessToken_thenThrowAuthenticationFailedException() {
        var tokenDto = new KeycloakTokenDto();
        tokenDto.setAccessToken("accessToken");
        Mockito.when(keycloakTokenService.getAccessToken(Mockito.anyString()))
                .thenReturn(null);

        Throwable thrown = catchThrowable(()->service.getAccessToken());
        assertThat(thrown).isInstanceOf(AuthenticationFailedException.class);
    }

    @Test
    public void givenAccessToken_whenGetUserList_thenReturnUserList(){
        var userInfoDto = UserInfoDto
                .builder()
                .username("testUsername")
                .email("testEmail")
                .build();
        Mockito.when(keycloakTokenService.getUserList(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(userInfoDto));
        assertThat(service.getUserList("accessToken").get(0).getUsername())
                .isEqualTo("testUsername");
    }

    @Test
    public void givenAccessToken_whenGetRoleList_thenReturnRoleList() {
        var role = RoleDto
                .builder()
                .id("id")
                .name("testRole")
                .build();
        Mockito.when(keycloakTokenService.getRoleList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(role));
        assertThat(service.getRoleList("accessToken").get(0).getName())
                .isEqualTo("testRole");
    }

    @Test
    public void givenAccessToken_whenGetUserListWithRole_thenReturnUserList(){
                var user = UserInfoDto
                .builder()
                .email("testMail")
                .username("testUsername")
                .roles(List.of("testRole"))
                .build();

        List<UserInfoDto> userList = new ArrayList<>();
        userList.add(user);

        KeycloakTokenDto tokenDto = new KeycloakTokenDto();
        tokenDto.setAccessToken("accessToken");
        Mockito.when(keycloakTokenService.getAccessToken(Mockito.any()))
                .thenReturn(tokenDto);
        var authDto = new AuthDto();
        authDto.setAccessToken("accessToken");
        Mockito.when(authDtoMapper.keycloakTokenDtoToAuthDto(Mockito.any(KeycloakTokenDto.class)))
                .thenReturn(authDto);
        Mockito.when(keycloakTokenService.getUserList(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(userList);
        Mockito.when(keycloakTokenService.getUserListByRole(Mockito.anyString(), Mockito.any(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(userList);
        assertThat(service.getUserListWithRole("accessToken").get(0).getUsername())
                .isEqualTo("testUsername");
    }

    @Test
    public void givenAccessTokenAndRole_whenGetUserListByRole_thenReturnUserList(){
        var user = UserInfoDto
                .builder()
                .email("testMail")
                .username("testUsername")
                .roles(List.of("testRole"))
                .build();

        List<UserInfoDto> userList = new ArrayList<>();
        userList.add(user);

        KeycloakTokenDto tokenDto = new KeycloakTokenDto();
        tokenDto.setAccessToken("accessToken");
        Mockito.when(keycloakTokenService.getAccessToken(Mockito.any()))
                .thenReturn(tokenDto);
        var authDto = new AuthDto();
        authDto.setAccessToken("accessToken");
        Mockito.when(authDtoMapper.keycloakTokenDtoToAuthDto(Mockito.any(KeycloakTokenDto.class)))
                .thenReturn(authDto);
        Mockito.when(keycloakTokenService.getUserList(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(userList);
        Mockito.when(keycloakTokenService.getUserListByRole(Mockito.anyString(), Mockito.any(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(userList);
        assertThat(service.getUserListByRole("accessToken", List.of("testRole")).get(0).getUsername())
                .isEqualTo("testUsername");
    }

    @Test
    public void whenGetByEmail_thenReturnUserInfo() {
        var user = UserInfoDto
                .builder()
                .email("<EMAIL>")
                .username("testUsername")
                .roles(List.of("testRole"))
                .build();
        var user1 = UserInfoDto
                .builder()
                .email("<EMAIL>")
                .username("test1Username")
                .roles(List.of("testRole"))
                .build();
        Mockito.when(keycloakTokenService.getUserList(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(user,user1));

        var role = RoleDto
                .builder()
                .id("id")
                .name("testRole")
                .build();

        Mockito.when(keycloakTokenService.getRoleList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(role));

        var tokenDto = new KeycloakTokenDto();
        tokenDto.setAccessToken("accesstoken");
        Mockito.when(keycloakTokenService.getAccessToken(Mockito.anyString()))
                .thenReturn(tokenDto);

        var authDto =new AuthDto();
        authDto.setAccessToken("accesstoken");
        Mockito.when(authDtoMapper.keycloakTokenDtoToAuthDto(Mockito.any(KeycloakTokenDto.class)))
                .thenReturn(authDto);

        Assertions.assertThat(service.getByEmail("<EMAIL>", "token").getUsername())
                .isEqualTo("testUsername");
    }

    @Test
    public void whenGetByUsername_thenReturnUserInfo() {
        var user = UserInfoDto
                .builder()
                .email("<EMAIL>")
                .username("testUsername")
                .roles(List.of("testRole"))
                .build();
        var user1 = UserInfoDto
                .builder()
                .email("<EMAIL>")
                .username("test1Username")
                .roles(List.of("testRole"))
                .build();
        Mockito.when(keycloakTokenService.getUserList(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(user,user1));

        var role = RoleDto
                .builder()
                .id("id")
                .name("testRole")
                .build();

        Mockito.when(keycloakTokenService.getRoleList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(role));

        var tokenDto = new KeycloakTokenDto();
        tokenDto.setAccessToken("accesstoken");
        Mockito.when(keycloakTokenService.getAccessToken(Mockito.anyString()))
                .thenReturn(tokenDto);

        var authDto =new AuthDto();
        authDto.setAccessToken("accesstoken");
        Mockito.when(authDtoMapper.keycloakTokenDtoToAuthDto(Mockito.any(KeycloakTokenDto.class)))
                .thenReturn(authDto);



        Assertions.assertThat(service.getByUsername("testUsername", "token").getUsername())
                .isEqualTo("testUsername");

    }
}
