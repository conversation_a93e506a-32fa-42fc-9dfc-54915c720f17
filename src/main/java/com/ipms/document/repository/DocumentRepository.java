package com.ipms.document.repository;

import com.ipms.document.model.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentRepository extends PagingAndSortingRepository<Document, Long>, JpaSpecificationExecutor<Document> {
    List<Document> findByIdIn (List<Long> ids);
    Page<Document> findByFirms(Long firmId, Pageable pageable);
    Optional<Document> findFirstByAttachmentsFileUniqueName(String fileUniqueName);
    List<Document> findByAttachmentsFileUniqueNameIn(List<String> fileUniqueNames);
}
