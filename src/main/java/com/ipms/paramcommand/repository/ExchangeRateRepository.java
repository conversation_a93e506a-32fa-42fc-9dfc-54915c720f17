package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.ExchangeRate;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDate;
import java.util.Optional;

public interface ExchangeRateRepository extends JpaRepository<ExchangeRate, Long> {
    Optional<ExchangeRate> findByFromCurrencyCodeAndToCurrencyCodeAndCurrencyDate(String fromCurrencyCode,
                                                                                  String toCurrencyCode,
                                                                                  LocalDate currencyDate);
    Optional<ExchangeRate> findTopByFromCurrencyCodeAndToCurrencyCodeAndCurrencyDateLessThanEqualOrderByCurrencyDateDesc(
            String fromCurrencyCode, String toCurrencyCode, LocalDate currencyDate);

}
