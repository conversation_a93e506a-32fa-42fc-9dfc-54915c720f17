package com.ipms.activity.controller;


import com.ipms.activity.dto.CounterPartyDto;
import com.ipms.activity.service.CounterPartyService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/counter-party")
public class CounterPartyController {
    private final CounterPartyService service;

    @PostMapping
    public BaseResponse<CounterPartyDto> save(@RequestBody @Valid CounterPartyDto dto) {
        return BaseResponse.<CounterPartyDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PostMapping("/bulk")
    public BaseResponse<List<CounterPartyDto>> saveBulk(@RequestBody @Valid List<CounterPartyDto> dtos) {
        return BaseResponse.<List<CounterPartyDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.saveBulk(dtos))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<CounterPartyDto> update(@PathVariable Long id, @RequestBody @Valid CounterPartyDto dto) {
        return BaseResponse.<CounterPartyDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(id, dto))
                .build();
    }

    @GetMapping("/by-activity/{activityId}")
    public BaseResponse<List<CounterPartyDto>> getAllByActivityId(@PathVariable Long activityId) {
        return BaseResponse.<List<CounterPartyDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByActivityId(activityId))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

}
