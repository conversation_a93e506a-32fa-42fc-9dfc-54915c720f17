package com.ipms.document.repository;

import com.ipms.document.model.Sample;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SampleRepository extends CrudRepository<Sample, Long>, JpaSpecificationExecutor<Sample> {
    List<Sample> findByIdIn(List<Long> ids);
}
