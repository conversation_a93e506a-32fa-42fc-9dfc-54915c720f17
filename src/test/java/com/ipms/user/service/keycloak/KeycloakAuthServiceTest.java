package com.ipms.user.service.keycloak;

import com.ipms.core.exception.InternalServerException;
import com.ipms.user.dto.keycloak.KeycloakAuthRequestDto;
import com.ipms.user.dto.keycloak.KeycloakLogoutRequestDto;
import com.ipms.user.dto.keycloak.KeycloakTokenDto;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;
import com.ipms.user.exception.InvalidCredentialsException;
import com.ipms.user.exception.InvalidRefreshTokenException;
import com.ipms.user.service.keycloak.impl.KeycloakAuthServiceImpl;
import feign.FeignException;
import feign.Request;
import feign.RequestTemplate;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;

@RunWith(SpringRunner.class)
public class KeycloakAuthServiceTest {
    @MockBean
    private KeycloakAuthServiceImpl service;

    @Mock
    private KeycloakClient keycloakClient;
    @Mock
    private KeycloakClientRestTemplate keycloakClientRestTemplate;

    @Before
    public void setUp() {
        service = new KeycloakAuthServiceImpl(keycloakClient, keycloakClientRestTemplate);
    }

    @Test
    public void whenGetRefreshToken_thenReturnKeycloakTokenDto() {
        Mockito.when(keycloakClient.getToken(Mockito.any(KeycloakAuthRequestDto.class), Mockito.anyString()))
                .thenReturn(new KeycloakTokenDto());
        assertThat(service.getRefreshToken("refreshtoken", "realm", "clientid"))
                .isNotNull();
    }

    @Test
    public void whenGetRefreshToken_thenThrowInvalidRefreshTokenException() {
        Mockito.doThrow(FeignException.class)
                .when(keycloakClient).getToken(Mockito.any(KeycloakAuthRequestDto.class), Mockito.anyString());

        Throwable thrown = catchThrowable(()->service.getRefreshToken("refreshtoken", "realm", "clientid"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InvalidRefreshTokenException.class);
    }

    @Test
    public void whenGetAccessToken_thenReturnKeycloakTokenDto() {
        Mockito.when(keycloakClient.getToken(Mockito.any(KeycloakAuthRequestDto.class), Mockito.anyString()))
                .thenReturn(new KeycloakTokenDto());
        assertThat(service.getAccessToken("username", "password", "realm", "clientid", "otp"))
                .isNotNull();
    }

    @Test
    public void whenGetAccessToken_thenThrowInternalServerException() {
        Mockito.doThrow(FeignException.FeignClientException.class)
                .when(keycloakClient).getToken(Mockito.any(KeycloakAuthRequestDto.class), Mockito.anyString());

        Throwable thrown = catchThrowable(()->service.getAccessToken("username", "password", "realm", "clientid", "otp"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InternalServerException.class);
    }

    @Test
    public void givenUnauthorized_whenGetAccessToken_thenThrowInvalidCredentialsException() {
        Request request = Request.create( Request.HttpMethod.GET,"url", new HashMap<>(), null, new RequestTemplate());
        FeignException.FeignClientException feignClientException = new FeignException.FeignClientException(401, "bu hata mesajinin basilmasi yanlis degil. testte beklenen bir sey", request,null,null);

        Mockito.doThrow(feignClientException)
                .when(keycloakClient).getToken(Mockito.any(KeycloakAuthRequestDto.class), Mockito.anyString());

        Throwable thrown = catchThrowable(()->service.getAccessToken("username", "password", "realm", "clientid", "otp"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InvalidCredentialsException.class);
    }

    @Test
    public void whenLogout_thenDoesNotThrowAnyException() {
        Mockito.doNothing().when(keycloakClient).logout(Mockito.any(KeycloakLogoutRequestDto.class),Mockito.anyString(),Mockito.anyString());
        assertThatCode(() -> service.logout("test", "test","realm","clientId"))
                .doesNotThrowAnyException();
    }

    @Test
    public void whenGetUserList_thenReturnUserList(){
        var userInfo = UserInfoDto
                .builder()
                .username("testUsername")
                .firstName("testFirstName")
                .build();
        Mockito.when(keycloakClientRestTemplate.getUserList(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(userInfo));
        assertThat(service.getUserList("accessToken", "testRealm").get(0).getUsername())
                .isEqualTo("testUsername");
    }

    @Test
    public void whenGetUserList_thenThrowInternalServerException() {
        Mockito.doThrow(FeignException.FeignClientException.class)
                .when(keycloakClientRestTemplate).getUserList(Mockito.anyString(), Mockito.anyString());

        Throwable thrown = catchThrowable(() -> service.getUserList("accessToken", "testRealm"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InternalServerException.class);
    }

    @Test
    public void givenUnauthorized_whenGetUserList_thenThrowInvalidCredentialsException() {
        Request request = Request.create(Request.HttpMethod.GET, "url", new HashMap<>(), null, new RequestTemplate());
        FeignException.FeignClientException feignClientException = new FeignException.FeignClientException(401, "bu hata mesajinin basilmasi yanlis degil. testte beklenen bir sey", request, null, null);

        Mockito.doThrow(feignClientException)
                .when(keycloakClientRestTemplate).getUserList(Mockito.anyString(), Mockito.anyString());

        Throwable thrown = catchThrowable(() -> service.getUserList("accessToken", "testRealm"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InvalidCredentialsException.class);
    }

    @Test
    public void whenGetRoleList_thenReturnRoleList() {
        var role = RoleDto
                .builder()
                .id("id")
                .name("testName")
                .build();
        Mockito.when(keycloakClientRestTemplate.getRoleList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(role));
        assertThat(service.getRoleList("testRealm", "testClientId", "testAccessToken").get(0).getName())
                .isEqualTo("testName");
    }

    @Test
    public void whenGetRoleList_thenThrowInternalServerException() {
        Mockito.doThrow(FeignException.FeignClientException.class)
                .when(keycloakClientRestTemplate).getRoleList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        Throwable thrown = catchThrowable(() -> service.getRoleList("testRealm", "testClientId", "accessToken"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InternalServerException.class);
    }

    @Test
    public void givenUnauthorized_whenGetRoleList_thenThrowInvalidCredentialsException() {
        Request request = Request.create(Request.HttpMethod.GET, "url", new HashMap<>(), null, new RequestTemplate());
        FeignException.FeignClientException feignClientException = new FeignException.FeignClientException(401, "bu hata mesajinin basilmasi yanlis degil. testte beklenen bir sey", request, null, null);

        Mockito.doThrow(feignClientException)
                .when(keycloakClientRestTemplate).getRoleList(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        Throwable thrown = catchThrowable(() -> service.getRoleList("testRealm", "testClientId", "accessToken"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InvalidCredentialsException.class);
    }

    @Test
    public void whenGetUserListByRole_thenReturnUserList() {
        var userInfo = UserInfoDto
                .builder()
                .username("testUsername")
                .firstName("testFirstName")
                .build();
        Mockito.when(keycloakClientRestTemplate.getUserListByRole(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(userInfo));
        assertThat(service.getUserListByRole("testRealm", "testClientId", "testRole", "testAccessToken")
                .get(0).getUsername()).isEqualTo("testUsername");
    }

    @Test
    public void whenGetUserListByRole_thenThrowInternalServerException() {
        Mockito.doThrow(FeignException.FeignClientException.class)
                .when(keycloakClientRestTemplate).getUserListByRole(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        Throwable thrown = catchThrowable(() -> service.getUserListByRole("testRealm", "testClientId", "testRole", "accessToken"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InternalServerException.class);
    }

    @Test
    public void givenUnauthorized_whenGetUserListByRole_thenThrowInvalidCredentialsException() {
        Request request = Request.create(Request.HttpMethod.GET, "url", new HashMap<>(), null, new RequestTemplate());
        FeignException.FeignClientException feignClientException = new FeignException.FeignClientException(401, "bu hata mesajinin basilmasi yanlis degil. testte beklenen bir sey", request, null, null);

        Mockito.doThrow(feignClientException)
                .when(keycloakClientRestTemplate).getUserListByRole(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

        Throwable thrown = catchThrowable(() -> service.getUserListByRole("testRealm", "testClientId", "testRole", "accessToken"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InvalidCredentialsException.class);
    }

    @Test
    public void givenClientId_whenGetAccessToken_thenReturnKeycloakTokenDto() {
        Mockito.when(keycloakClient.getToken(Mockito.any(KeycloakAuthRequestDto.class), Mockito.any()))
                .thenReturn(new KeycloakTokenDto());
        assertThat(service.getAccessToken("testClientId"))
                .isNotNull();
    }

    @Test
    public void givenClientId_whenGetAccessToken_thenThrowInternalServerException() {
        Mockito.doThrow(FeignException.FeignClientException.class)
                .when(keycloakClient).getToken(Mockito.any(KeycloakAuthRequestDto.class), Mockito.any());

        Throwable thrown = catchThrowable(() -> service.getAccessToken("clientId"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InternalServerException.class);
    }

    @Test
    public void givenClientId_whenGetAccessToken_thenThrowInvalidCredentialsException() {
        Request request = Request.create(Request.HttpMethod.GET, "url", new HashMap<>(), null, new RequestTemplate());
        FeignException.FeignClientException feignClientException = new FeignException.FeignClientException(401, "bu hata mesajinin basilmasi yanlis degil. testte beklenen bir sey", request, null, null);

        Mockito.doThrow(feignClientException)
                .when(keycloakClient).getToken(Mockito.any(KeycloakAuthRequestDto.class), Mockito.any());

        Throwable thrown = catchThrowable(() -> service.getAccessToken("testClientId"));
        AssertionsForClassTypes.assertThat(thrown).isInstanceOf(InvalidCredentialsException.class);
    }
}
