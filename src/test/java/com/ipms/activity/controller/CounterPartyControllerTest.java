package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.activity.dto.*;
import com.ipms.activity.service.CounterPartyService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class CounterPartyControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CounterPartyService service;

    private CounterPartyDto dto;

    ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    @Before
    public void initialize() {
        dto = getCounterPartyDto();
        CounterPartyDto test = mock(CounterPartyDto.class);
        System.out.println();
    }

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        when(service.save(any(CounterPartyDto.class))).thenReturn(dto);

        mockMvc.perform(post("/counter-party")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

    }

    @Test
    public void whenSaveBulk_thenReturnBaseResponse() throws Exception {
        var dtos = List.of(dto);

        when(service.saveBulk(anyList())).thenReturn(dtos);

        mockMvc.perform(post("/counter-party/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dtos))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {
        when(service.save(any(CounterPartyDto.class))).thenReturn(dto);

        mockMvc.perform(put("/counter-party/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetAllByActivityId_thenReturnBaseResponse() throws Exception {
        when(service.save(any(CounterPartyDto.class))).thenReturn(dto);

        mockMvc.perform(get("/counter-party/by-activity/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());
        mockMvc.perform(delete("/counter-party/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    private CounterPartyDto getCounterPartyDto() {
        var counterRightOwnerDto = CounterRightOwnerDto.builder()
                .name("cro-1")
                .address("address-1")
                .version(0L)
                .id(1L)
                .build();

        var agencyDto = AgencyDto.builder()
                .name("agency-1")
                .email("email-1")
                .city("city-1")
                .country("country-1")
                .build();

        var lawyerDto = LawyerDto.builder()
                .name("lawyer-1")
                .type("type-1")
                .email("email-1")
                .phoneNumber("phone-number-1")
                .agency(agencyDto)
                .build();

        var suspectDto = SuspectDto.builder()
                .name("name-1")
                .surname("surname")
                .identityNumber("445**44")
                .build();

        return CounterPartyDto.builder()
                .id(1L)
                .activityId(123L)
                .suspect(suspectDto)
                .counterRightOwner(counterRightOwnerDto)
                .lawyers(List.of(lawyerDto))
                .build();
    }
}