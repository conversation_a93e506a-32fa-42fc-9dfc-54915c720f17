package com.ipms.activity.service.impl;

import com.ipms.activity.dto.CustomsSuspensionsSenderDto;
import com.ipms.activity.mapper.CustomsSuspensionSenderMapper;
import com.ipms.activity.repository.CustomSuspensionsSenderRepository;
import com.ipms.activity.service.CustomsSuspensionsSenderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class CustomsSuspensionsSenderServiceImpl implements CustomsSuspensionsSenderService {
    private final CustomSuspensionsSenderRepository repository;
    private final CustomsSuspensionSenderMapper mapper;


    @Override
    public List<CustomsSuspensionsSenderDto> findAllByName(String name) {
        var senders = repository.findAllByNameContainingIgnoreCase(name);
        List<CustomsSuspensionsSenderDto> dtos = new ArrayList<>();
        senders.forEach(receiver -> dtos.add(mapper.toDto(receiver)));
        return dtos;
    }
}
