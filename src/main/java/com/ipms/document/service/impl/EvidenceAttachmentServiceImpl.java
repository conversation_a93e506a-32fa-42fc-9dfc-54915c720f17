package com.ipms.document.service.impl;

import com.ipms.config.storage.service.StorageService;
import com.ipms.document.dto.EvidenceAttachmentDto;
import com.ipms.document.mapper.EvidenceAttachmentMapper;
import com.ipms.document.model.EvidenceAttachment;
import com.ipms.document.repository.EvidenceAttachmentRepository;
import com.ipms.document.service.EvidenceAttachmentService;
import com.ipms.document.service.EvidenceService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import com.ipms.config.storage.exception.StorageFileNotFoundException;
import java.util.UUID;

@RequiredArgsConstructor
@Service
public class EvidenceAttachmentServiceImpl implements EvidenceAttachmentService {

    private final EvidenceAttachmentRepository repository;
    private final StorageService storageService;
    private final EvidenceService evidenceService;
    private final EvidenceAttachmentMapper mapper;

    @Override
    public EvidenceAttachmentDto save(EvidenceAttachmentDto attachmentDto) {
        var evidence = evidenceService.getEvidenceById(attachmentDto.getEvidenceId());
        var attachment = mapper.toEvidenceAttachment(attachmentDto);
        attachment.setEvidence(evidence);
        attachment.setFileUniqueName(prepareDocumentName(attachment.getFileName()));
        var saved = repository.save(attachment);
        return toAttachmentDto(saved);
    }

    @Override
    public void delete(Long id, Long version) {
        var attachment = getAttachmentById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .version(version)
                .build();
        repository.save(attachment);
    }

    public static String prepareDocumentName(String fileName) {
        return "e" + UUID.randomUUID() + "." + FilenameUtils.getExtension(fileName);
    }

    private EvidenceAttachment getAttachmentById(Long id) {
        return repository.findById(id).orElseThrow(StorageFileNotFoundException::new);
    }

    private EvidenceAttachmentDto toAttachmentDto(EvidenceAttachment attachment) {
        var attachmentDto = mapper.toEvidenceAttachmentDto(attachment);
        var url = storageService.generateSas(mapper.toStorageFile(attachmentDto));
        attachmentDto.setFileSignedUrl(url);
        return attachmentDto;
    }
}
