package com.ipms.activity.controller;

import com.ipms.activity.dto.PreliminaryInjunctionDetailDto;
import com.ipms.activity.service.PreliminaryInjunctionDetailService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class PreliminaryInjunctionControllerTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private PreliminaryInjunctionDetailService service;

    @Test
    public void givenId_whenGetById_thenReturnBaseResponse() throws Exception {
        var detailDto = PreliminaryInjunctionDetailDto.builder().build();
        Mockito.when(service.getById(Mockito.anyLong()))
                .thenReturn(detailDto);
        mvc.perform(get("/preliminary-injunction/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}