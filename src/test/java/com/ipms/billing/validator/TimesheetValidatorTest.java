package com.ipms.billing.validator;

import com.ipms.billing.dto.TSICPackageDto;
import com.ipms.billing.dto.TimesheetDto;
import com.ipms.billing.enums.TimesheetResponseCode;
import com.ipms.billing.exception.TimesheetValidationException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatCode;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@RunWith(SpringRunner.class)
public class TimesheetValidatorTest {

    private TimesheetValidator validator;

    @Before
    public void setUp() {
        validator = new TimesheetValidator();
    }

    @Test
    public void givenTimesheetDateAfterPackageStartDate_whenValidate_thenNoException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-07-15"))
                .build();
        var tsicPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(null)
                .build();
        
        var packages = List.of(tsicPackage);
        assertThatCode(() -> validator.validateTimesheetDate(dto, packages))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenTimesheetDateEqualToPackageStartDate_whenValidate_thenNoException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-07-01"))
                .build();
        var tsicPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(null)
                .build();
        
        var packages = List.of(tsicPackage);
        assertThatCode(() -> validator.validateTimesheetDate(dto, packages))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenTimesheetDateBeforePackageStartDate_whenValidate_thenThrowException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-06-30"))
                .build();
        var tsicPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(null)
                .build();
        
        var packages = List.of(tsicPackage);
        assertThatThrownBy(() -> validator.validateTimesheetDate(dto, packages))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE)
                .hasFieldOrPropertyWithValue("params", new Object[]{"2024-07-01"});
    }

    @Test
    public void givenNoPackages_whenValidate_thenNoException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-07-15"))
                .build();
        
        assertThatCode(() -> validator.validateTimesheetDate(dto, Collections.emptyList()))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenOnlyClosedPackagesAndDateAfterLatestEndDate_whenValidate_thenNoException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-08-15"))
                .build();
        var closedPackage1 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var closedPackage2 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();

        var packages = List.of(closedPackage1, closedPackage2);
        assertThatCode(() -> validator.validateTimesheetDate(dto, packages))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenOnlyClosedPackagesAndDateEqualToLatestEndDate_whenValidate_thenThrowException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-07-31"))
                .build();
        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();

        var packages = List.of(closedPackage);
        assertThatThrownBy(() -> validator.validateTimesheetDate(dto, packages))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE)
                .hasFieldOrPropertyWithValue("params", new Object[]{"2024-07-31"});
    }

    @Test
    public void givenOnlyClosedPackagesAndDateBeforeLatestEndDate_whenValidate_thenThrowException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-07-15"))
                .build();
        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();

        var packages = List.of(closedPackage);
        assertThatThrownBy(() -> validator.validateTimesheetDate(dto, packages))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE)
                .hasFieldOrPropertyWithValue("params", new Object[]{"2024-07-31"});
    }

    @Test
    public void givenMultipleClosedPackagesAndDateAfterLatestEndDate_whenValidate_thenNoException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-08-15"))
                .build();
        var earlierPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var latestPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();

        var packages = List.of(earlierPackage, latestPackage);
        assertThatCode(() -> validator.validateTimesheetDate(dto, packages))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenActivePackageExists_whenValidate_thenSkipEndDateValidation() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-08-15"))
                .build();
        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var activePackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(null) // Active package
                .build();
        
        var packages = List.of(closedPackage, activePackage);
        assertThatCode(() -> validator.validateTimesheetDate(dto, packages))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenMultipleActivePackages_whenValidate_thenUseLatestActivePackageStartDate() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-07-15"))
                .build();
        var earlierActivePackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(null)
                .build();
        var latestActivePackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-08-01"))
                .endDate(null)
                .build();
        
        var packages = List.of(earlierActivePackage, latestActivePackage);
        assertThatThrownBy(() -> validator.validateTimesheetDate(dto, packages))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE)
                .hasFieldOrPropertyWithValue("params", new Object[]{"2024-08-01"});
    }

    @Test
    public void givenTimesheetDateBeforeActivePackageStartDate_whenValidate_thenThrowStartDateException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-06-30"))
                .build();
        var activePackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(null)
                .build();
        
        var packages = List.of(activePackage);
        assertThatThrownBy(() -> validator.validateTimesheetDate(dto, packages))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE)
                .hasFieldOrPropertyWithValue("params", new Object[]{"2024-07-01"});
    }

    @Test
    public void givenMixedPackagesWithActivePackage_whenValidate_thenValidateOnlyAgainstActivePackage() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-07-15"))
                .build();
        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var activePackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(null)
                .build();
        
        var packages = List.of(closedPackage, activePackage);
        assertThatCode(() -> validator.validateTimesheetDate(dto, packages))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenOnlyClosedPackagesAndDateBeforePackageStartDate_whenValidate_thenThrowException() {
        var dto = TimesheetDto.builder()
                .date(LocalDate.parse("2024-06-15"))
                .build();
        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();

        var packages = List.of(closedPackage);
        assertThatThrownBy(() -> validator.validateTimesheetDate(dto, packages))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE)
                .hasFieldOrPropertyWithValue("params", new Object[]{"2024-07-31"});
    }
}