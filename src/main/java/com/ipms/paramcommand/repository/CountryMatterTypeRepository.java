package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.CountryMatterType;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CountryMatterTypeRepository extends CrudRepository<CountryMatterType, Long>, JpaSpecificationExecutor<CountryMatterType> {
    Optional<CountryMatterType> findByCountryAndMatterType(String country, String matterType);
    List<CountryMatterType> findByCountry(String country);
}
