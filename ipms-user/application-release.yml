eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:release-ipms-user}.release-ipms-user
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

spring:
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-release/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web
  keycloak-web-client-secret: PNQ4ZaWfGlmKfr6RVaR8qXSDqr4Ve6X0
  keycloak-master-client-secret: 8AnUHFHcLTbGW1SQ7fLFpdMfBb6Z6TkK
  keycloak-master-realm: master
  keycloak-master-clientId: 2b8cc5b0-b839-4e28-b9f7-cd20a721c71e
  keycloak-master-clientNo: api-release

keycloak:
  realm: ipms-release
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk