package com.ipms.document.service.impl;


import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.storage.service.StorageService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.document.client.ActivityClient;
import com.ipms.document.dto.*;
import com.ipms.document.exception.DocumentCannotRemoveException;
import com.ipms.document.exception.DocumentNotFoundException;
import com.ipms.document.mapper.DocumentMapper;
import com.ipms.document.model.Document;
import com.ipms.document.repository.DocumentRepository;
import com.ipms.document.service.DocumentService;
import com.ipms.document.specification.DocumentSpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class DocumentServiceImpl implements DocumentService {
    private final DocumentRepository repository;
    private final DocumentMapper mapper;
    private final ActivityClient activityClient;
    private final StorageService storageService;
    private final ProducerService producerService;

    @Override
    @Transactional
    public DocumentDto save(DocumentCreateDto documentCreateDto) {
        var document = mapper.toDocument(documentCreateDto);
        document.setResult(documentCreateDto.getResult());
        var attachment = mapper.toAttachment(documentCreateDto);
        attachment.setDocument(document);
        document.setAttachments(List.of(attachment));
        var saved = repository.save(document);
        activityClient.addDocument(documentCreateDto.getActivityId(), saved.getId(), Boolean.TRUE);
        sendTransferEvent(saved, TransferType.INSERT);
        return toDocumentDto(saved);
    }

    @Override
    @Transactional
    public DocumentDto save(DocumentManualDto documentManualDto) {
        var document = mapper.toDocument(documentManualDto);
        document.setResult(documentManualDto.getResult());
        var saved = repository.save(document);
        activityClient.addDocument(documentManualDto.getActivityId(), saved.getId(), Boolean.TRUE);
        sendTransferEvent(saved, TransferType.INSERT);
        return mapper.toDto(saved);
    }

    @Override
    public DocumentDto update(DocumentDto documentDto, Long id) {
        var document = mapper.toDocumentFromDto(documentDto, getDocumentById(id).toBuilder().build());
        document.setResult(documentDto.getResult());
        var saved = repository.save(document);
        sendTransferEvent(saved, TransferType.UPDATE);
        return toDocumentDto(saved);
    }

    @Override
    public DocumentDto getById(Long id) {
        var document = getDocumentById(id);
        return toDocumentDto(document);
    }

    @Override
    public List<DocumentDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids)
                .stream()
                .map(this::toDocumentDto)
                .toList();
    }

    @Override
    public DocumentDto getByFileUniqueName(String fileUniqueName) {
        var document = repository.findFirstByAttachmentsFileUniqueName(fileUniqueName)
                .orElseThrow(DocumentNotFoundException::new);
        return toDocumentDto(document);
    }

    @Override
    public List<DocumentDto> getByFileUniqueName(List<String> fileUniqueNames) {
        return repository.findByAttachmentsFileUniqueNameIn(fileUniqueNames)
                .stream()
                .map(this::toDocumentDto)
                .toList();
    }

    @Override
    public void delete(Long id, Long version) {
        activityClient.checkDocumentLinked(id)
                .linked()
                .ifPresent(payload -> {
                    throw new DocumentCannotRemoveException();
                });
        var document = getDocumentById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .version(version)
                .build();
        repository.save(document);
        sendTransferEvent(document, TransferType.DELETE);
    }

    @Override
    public DocumentPageDto getAll(DocumentFilterRequest request, int page, int size) {
        var documentPage = repository.findAll(mapToSpecification(request), PageRequest.of(page, size));
        return DocumentPageDto.builder()
                .totalElements(documentPage.getTotalElements())
                .totalPages(documentPage.getTotalPages())
                .documents(documentPage.getContent()
                        .stream()
                        .map(this::toDocumentDto)
                        .toList())
                .build();
    }

    @Override
    public void processUpdatedEvent(DocumentEvent event) {
        var document = getDocumentById(event.getId());
        sendTransferEvent(document, TransferType.UPDATE);
    }

    @Override
    public Document getDocumentById(Long id) {
        return repository.findById(id).orElseThrow(DocumentNotFoundException::new);
    }

    private DocumentDto toDocumentDto(Document document) {
        var documentDto = mapper.toDto(document);
        documentDto.getAttachments().forEach(attachmentDto -> {
            var url = storageService.generateSas(mapper.toStorageFile(attachmentDto));
            attachmentDto.setFileSignedUrl(url);
        });
        documentDto.setHasMailAttachment(hasMailAttachment(documentDto.getAttachments()));
        return documentDto;
    }

    private DocumentSummaryDto toDocumentSummaryDto(Document document) {
        return mapper.toDocumentSummaryDto(document);
    }

    private boolean hasMailAttachment(List<AttachmentDto> attachments) {
        return attachments
                .stream()
                .anyMatch(attachmentDto -> attachmentDto.getFileUniqueName().startsWith("a"));
    }

    private void sendTransferEvent(Document document, TransferType transferType) {
        if(Optional.ofNullable(document.getNoTransfer()).orElse(false)) {
            return;
        }
        sendTransferEvent(document.getId(), transferType);

    }

    private void sendTransferEvent(Long id, TransferType transferType) {
        producerService.sendTransfer(TransferEvent.builder()
                .type(transferType)
                .domainObjectId(id)
                .domain(Domain.IPMS_DOCUMENT)
                .build());
    }

    @Override
    public List<DocumentSummaryDto> getDocumentSummaries(DocumentFilterRequest request) {
        return repository.findAll(mapToSpecification(request)).stream()
                .map(this::toDocumentSummaryDto)
                .toList();
    }

    private DocumentSpecification mapToSpecification(DocumentFilterRequest request) {
        return DocumentSpecification.builder()
                .ids(request.getIds())
                .results(request.getResults())
                .firmId(request.getFirmId())
                .types(request.getTypes())
                .expertId(request.getExpertId())
                .localAttorneyId(request.getLocalAttorneyId())
                .sortField(request.getSortField())
                .sortDirection(request.getSortDirection())
                .build();
    }
}
