package com.ipms.activity.converter;

import com.ipms.activity.enums.CriminalActionDeliveryType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class CriminalActionDeliveryConverter implements AttributeConverter<CriminalActionDeliveryType, String> {

    @Override
    public String convertToDatabaseColumn(CriminalActionDeliveryType deliveryType) {
        return deliveryType == null ? null : deliveryType.getValue();
    }

    @Override
    public CriminalActionDeliveryType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : CriminalActionDeliveryType.getEnum(s);
    }
}
