package com.ipms.document.mapper;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.document.dto.SampleAttachmentDto;
import com.ipms.document.model.SampleAttachment;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SampleAttachmentMapper {
    SampleAttachment toSampleAttachment(SampleAttachmentDto dto);
    SampleAttachmentDto toSampleAttachmentDto(SampleAttachment attachment);
    StorageFile toStorageFile(SampleAttachmentDto dto);
}
