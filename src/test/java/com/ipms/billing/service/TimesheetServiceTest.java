package com.ipms.billing.service;

import com.ipms.billing.client.ActivityClient;
import com.ipms.billing.client.MatterClient;
import com.ipms.billing.client.ParamCommandClient;
import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.ApprovalType;
import com.ipms.billing.enums.TimesheetResponseCode;
import com.ipms.billing.exception.TimesheetValidationException;
import com.ipms.billing.mapper.TimesheetMapper;
import com.ipms.billing.mapper.TimesheetMapperImpl;
import com.ipms.billing.model.Timesheet;
import com.ipms.billing.repository.TimesheetRepository;
import com.ipms.billing.service.impl.TimesheetServiceImpl;
import com.ipms.billing.validator.TimesheetValidator;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.security.utils.SecurityUtils;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.core.entity.Revision;
import com.ipms.core.service.RevisionService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class TimesheetServiceTest {

    @MockBean
    private TimesheetService service;

    @Mock
    private TimesheetRepository repository;

    @Mock
    private ProducerService producerService;

    @Mock
    private RevisionService<Timesheet> revisionService;

    @Mock
    private ActivityClient activityClient;

    @Mock
    private MatterClient matterClient;

    @Mock
    private ParameterClient parameterClient;

    @Mock
    private BillingAccountService billingAccountService;

    @Mock
    private ExpenseService expenseService;

    @Mock
    private BillingAccountExpenseCodeService billingAccountExpenseCodeService;

    @Mock
    private ParamCommandClient paramCommandClient;

    @Mock
    private TimesheetValidator timesheetValidator;


    private final TimesheetMapper mapper = new TimesheetMapperImpl();

    @Before
    public void setUp() {
        service = new TimesheetServiceImpl(repository, mapper, producerService, revisionService, activityClient,
                matterClient, paramCommandClient, parameterClient, billingAccountService, expenseService,
                billingAccountExpenseCodeService, timesheetValidator);
    }

    @Test
    public void givenTimesheetDto_whenSave_thenReturnTimesheetDto() {
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(Collections.emptyList())
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(any())).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class)))
                .thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(producerService).sendTransfer(any(TransferEvent.class));
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());
        assertThat(service.save(TimesheetDto.builder().id(1L).activityId(1L).build()).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetById_thenReturnTimesheetDto() {
        var timesheet = Timesheet.builder().id(1L).build();
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(timesheet));
        assertThat(service.getById(1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenDates_whenGetPageByDates_thenReturnTimesheetSummaryDto() {
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            var today = LocalDate.now();
            var yesterday = today.minusDays(1);
            var timesheet = Timesheet.builder().id(1L).loggedTime(BigDecimal.ONE).billableTime(BigDecimal.ONE).build();
            securityUtilsMock.when(SecurityUtils::getCurrentUsername)
                    .thenReturn(Optional.of("kerim.samimi"));
            when(repository.findByCreatedByAndDateBetweenOrderByDateDesc(Mockito.anyString(), any(), any()))
                    .thenReturn(List.of(timesheet));
            assertThat(service.getAllByDates(yesterday, today).getTotalTimes().getTotalLoggedTime())
                    .isEqualByComparingTo(BigDecimal.ONE);
        }
    }

    @Test
    public void givenIdAndPageAndSize_whenGetByActivity_thenReturnTimesheetPageDto() {
        var timesheet = Timesheet.builder().id(1L).loggedTime(BigDecimal.ONE).billableTime(BigDecimal.ONE).build();
        Page<Timesheet> page = new PageImpl<>(List.of(timesheet), Pageable.ofSize(1), 1);
        when(repository.findByActivityIdOrderByDateDesc(Mockito.anyLong(), any(Pageable.class)))
                .thenReturn(page);
        assertThat(service.getByActivity(1L, 2, 3).getTotalElements())
                .isEqualTo(1L);
    }

    @Test
    public void givenTimesheetDto_whenUpdate_thenReturnTimesheetDto() {
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(Collections.emptyList())
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        var timesheet = Timesheet.builder().id(1L).build();
        when(activityClient.getActivity(any())).thenReturn(activityResponse);
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(timesheet));
        when(repository.save(any(Timesheet.class)))
                .thenReturn(timesheet);
        Mockito.doNothing().when(producerService).sendTransfer(any(TransferEvent.class));
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());
        assertThat(service.update(TimesheetDto.builder().id(1L).matterId(1L).activityId(1L).build()).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenExistingTimesheet_whenDelete_thenReturnNothing() {
        var timesheet = Timesheet.builder().id(1L).build();
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(timesheet));
        assertThatCode(() -> service.delete(1L, 1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenTimesheetIds_whenGetByIdIn_thenReturnDtoList() {
        var timesheet = Timesheet.builder()
                .id(1L)
                .build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(timesheet));
        assertThat(service.getByIdIn(List.of(1L, 2L)))
                .isNotEmpty();
    }

    @Test
    public void givenActivityId_whenGetTimesByActivity_thenReturnCorrectTotalLoggedTime() {
        var timesheet = Timesheet.builder()
                .id(1L)
                .billableTime(BigDecimal.ZERO)
                .loggedTime(BigDecimal.valueOf(60L))
                .build();
        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet));
        assertThat(service.getTimesByActivity(1L).getTotalLoggedTime())
                .isEqualTo(BigDecimal.ONE);
    }

    @Test
    public void givenTimesheetId_whenGetRevisions_thenReturnRevisionList() {
        var revision = Revision.builder()
                .fieldName("status")
                .updatedAt(LocalDateTime.now())
                .build();
        when(revisionService.getAll(any(), anyLong()))
                .thenReturn(List.of(revision));
        assertThat(service.getRevisions(1L)).isNotEmpty();
    }

    @Test
    public void givenStatus_whenGetRevisionsByField_thenReturnRevisionList() {
        var revision = Revision.builder()
                .fieldName("status")
                .updatedAt(LocalDateTime.now())
                .build();
        when(revisionService.getByField(any(), anyString(), anyLong()))
                .thenReturn(List.of(revision));
        assertThat(service.getRevisionsByField(1L, "status")).isNotEmpty();
    }

    @Test
    public void givenFilterRequest_NoApprovalFilters_whenGetPage_thenReturnTimesheetPageDto() {
        var timesheet = Timesheet.builder().id(1L).build();
        Page<Timesheet> page = new PageImpl<>(List.of(timesheet), Pageable.ofSize(1), 1);
        when(repository.findAll(Mockito.any(), any(Pageable.class)))
                .thenReturn(page);
        when(activityClient.getTimesheetManuelApprovalActivityIds())
                .thenReturn(ActivityIdsResponse.builder().payload(new ArrayList<>(List.of(1L, 2L,3L))).build());
        when(matterClient.getMatterIds(any(), any(), any()))
                .thenReturn(IdsResponse.builder().payload(new ArrayList<>(List.of(1L, 2L, 3L))).build());
        var filterRequest = TimesheetFilterRequest.builder()
                .activityIds(new ArrayList<>(List.of(1L, 2L)))
                .matterIds(new ArrayList<>(List.of(1L)))
                .timespentApprovals(List.of("timespentApproval"))
                .build();

        var timesheetPageDto = service.getPage(filterRequest, 1, 1);

        assertThat(timesheetPageDto.getTotalElements())
                .isEqualTo(1L);
        assertThat(filterRequest.getActivityIds()).isEqualTo(List.of(1L, 2L));
        assertThat(filterRequest.getMatterIds()).isEqualTo(List.of(1L, 2L, 3L));
    }

    @Test
    public void givenFilterRequest_NoMatterSpecificFilters_whenGetPage_thenReturnTimesheetPageDto() {
        var timesheet = Timesheet.builder().id(1L).build();
        Page<Timesheet> page = new PageImpl<>(List.of(timesheet), Pageable.ofSize(1), 1);
        when(repository.findAll(Mockito.any(), any(Pageable.class)))
                .thenReturn(page);
        when(activityClient.getTimesheetManuelApprovalActivityIds())
                .thenReturn(ActivityIdsResponse.builder().payload(new ArrayList<>(List.of(1L, 2L,3L))).build());
        when(matterClient.getMatterIds(any(), any(), any()))
                .thenReturn(IdsResponse.builder().payload(new ArrayList<>(List.of(1L, 2L, 3L))).build());
        var filterRequest = TimesheetFilterRequest.builder()
                .activityIds(new ArrayList<>(List.of(1L, 2L)))
                .matterIds(new ArrayList<>(List.of(4L)))
                .build();

        var timesheetPageDto = service.getPage(filterRequest, 1, 1);

        assertThat(timesheetPageDto.getTotalElements())
                .isEqualTo(1L);
        assertThat(filterRequest.getActivityIds()).isEqualTo(List.of(1L, 2L));
        assertThat(filterRequest.getMatterIds()).isEqualTo(List.of(4L));
    }

    @Test
    public void givenFilterRequest_ManuelApprovalFilter_whenGetPage_thenReturnTimesheetPageDto() {
        var timesheet = Timesheet.builder().id(1L).build();
        Page<Timesheet> page = new PageImpl<>(List.of(timesheet), Pageable.ofSize(1), 1);
        when(repository.findAll(Mockito.any(), any(Pageable.class)))
                .thenReturn(page);
        when(activityClient.getTimesheetManuelApprovalActivityIds())
                .thenReturn(ActivityIdsResponse.builder().payload(new ArrayList<>(List.of(1L, 2L,3L))).build());
        when(matterClient.getMatterIds(any(), any(), any()))
                .thenReturn(IdsResponse.builder().payload(new ArrayList<>(List.of(1L, 2L, 3L))).build());
        var filterRequest = TimesheetFilterRequest.builder()
                .approvalType(ApprovalType.MANUEL)
                .partners(List.of("partner"))
                .activityIds(new ArrayList<>(List.of(1L, 2L)))
                .matterIds(new ArrayList<>(List.of(4L)))
                .build();

        var timesheetPageDto = service.getPage(filterRequest, 1, 1);

        assertThat(timesheetPageDto.getTotalElements())
                .isEqualTo(1L);
        assertThat(filterRequest.getActivityIds()).isEqualTo(List.of(1L, 2L));
        assertThat(filterRequest.getMatterIds()).isEqualTo(List.of(1L, 2L, 3L));
    }

    @Test
    public void givenFilterRequest_SystemApprovalFilter_whenGetPage_thenReturnTimesheetPageDto() {
        var timesheet = Timesheet.builder().id(1L).build();
        Page<Timesheet> page = new PageImpl<>(List.of(timesheet), Pageable.ofSize(1), 1);
        when(repository.findAll(Mockito.any(), any(Pageable.class)))
                .thenReturn(page);
        when(activityClient.getTimesheetManuelApprovalActivityIds())
                .thenReturn(ActivityIdsResponse.builder().payload(new ArrayList<>(List.of(1L, 2L,3L))).build());
        when(matterClient.getMatterIds(any(), any(), any()))
                .thenReturn(IdsResponse.builder().payload(new ArrayList<>(List.of(1L, 2L, 3L))).build());
        var filterRequest = TimesheetFilterRequest.builder()
                .approvalType(ApprovalType.SYSTEM)
                .activityIds(new ArrayList<>(List.of(1L, 2L)))
                .build();

        var timesheetPageDto = service.getPage(filterRequest, 1, 1);

        assertThat(timesheetPageDto.getTotalElements())
                .isEqualTo(1L);
        assertThat(filterRequest.getActivityIds()).isEqualTo(List.of(1L, 2L));
        assertThat(filterRequest.getActivityIdsNotIn()).isEqualTo(List.of(1L, 2L, 3L));
    }

    @Test
    public void givenIds_whenApproveBulk_thenReturnDtoList() {
        var timesheet1 = Timesheet.builder().id(1L).build();
        var timesheet2 = Timesheet.builder().id(2L).build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(timesheet1, timesheet2));
        when(repository.saveAll(Mockito.anyList()))
                .thenReturn(List.of(timesheet1, timesheet2));
        assertThat(service.approveBulk(List.of(1L, 2L)))
                .isNotEmpty();
        assertThat(timesheet1.getIsApproved()).isTrue();
        assertThat(timesheet2.getIsApproved()).isTrue();
    }

    @Test
    public void givenIds_whenApproveBillingBulk_thenReturnDtoList() {
        var timesheet1 = Timesheet.builder().id(1L).build();
        var timesheet2 = Timesheet.builder().id(2L).build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(timesheet1, timesheet2));
        when(repository.saveAll(Mockito.anyList()))
                .thenReturn(List.of(timesheet1, timesheet2));
        assertThat(service.approveBillingBulk(List.of(1L, 2L)))
                .isNotEmpty();
        assertThat(timesheet1.getIsBillingApproved()).isTrue();
        assertThat(timesheet2.getIsBillingApproved()).isTrue();
    }

    @Test
    public void givenActivityId_whenGetUnbilledTimesheetsByActivityId_thenReturnUnbilledTimesheets() {
        Long activityId = 123L;
        List<Timesheet> timesheets = List.of(
                Timesheet.builder()
                        .id(1L)
                        .activityId(activityId)
                        .expenseDate(null)
                        .loggedTime(BigDecimal.valueOf(60))
                        .billableTime(BigDecimal.valueOf(45))
                        .date(LocalDate.now())
                        .build(),
                Timesheet.builder()
                        .id(2L)
                        .activityId(activityId)
                        .expenseDate(null)
                        .loggedTime(BigDecimal.valueOf(120))
                        .billableTime(BigDecimal.valueOf(90))
                        .date(LocalDate.now().minusDays(1))
                        .build()
        );
        when(repository.findByActivityIdAndExpenseDateIsNull(activityId)).thenReturn(timesheets);

        List<TimesheetDto> result = service.getUnbilledTimesheetsByActivityId(activityId);

        assertThat(result).hasSize(2);
        verify(repository, times(1)).findByActivityIdAndExpenseDateIsNull(activityId);
    }

    @Test
    public void givenActivityIdWithNoTimesheets_whenGetUnbilledTimesheetsByActivityId_thenReturnEmptyList() {
        Long activityId = 456L;

        when(repository.findByActivityIdAndExpenseDateIsNull(activityId)).thenReturn(Collections.emptyList());

        List<TimesheetDto> result = service.getUnbilledTimesheetsByActivityId(activityId);

        assertThat(result).isEmpty();
        verify(repository, times(1)).findByActivityIdAndExpenseDateIsNull(activityId);
    }

    @Test
    public void givenActivityIdWithMixedTimesheets_whenGetUnbilledTimesheetsByActivityId_thenFilterOutTimesheetsWithExpenseDate() {
        Long activityId = 789L;
        List<Timesheet> timesheetsFromRepo = List.of(
                Timesheet.builder()
                        .id(1L)
                        .activityId(activityId)
                        .expenseDate(null)
                        .loggedTime(BigDecimal.valueOf(60))
                        .billableTime(BigDecimal.valueOf(45))
                        .date(LocalDate.now())
                        .build()
        );

        when(repository.findByActivityIdAndExpenseDateIsNull(activityId)).thenReturn(timesheetsFromRepo);

        List<TimesheetDto> result = service.getUnbilledTimesheetsByActivityId(activityId);

        assertThat(result).hasSize(1);
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(0).getExpenseDate()).isNull();

        verify(repository, times(1)).findByActivityIdAndExpenseDateIsNull(activityId);
    }

    @Test
    public void givenIdsAndIncludedInChargeValue_whenUpdateIncludedInChargeBulk_thenReturnUpdatedTimesheets() {
        var timesheet1 = Timesheet.builder().id(1L).build();
        var timesheet2 = Timesheet.builder().id(2L).build();
        when(repository.findByIdIn(anyList()))
                .thenReturn(List.of(timesheet1, timesheet2));
        when(repository.saveAll(anyList()))
                .thenReturn(List.of(timesheet1, timesheet2));

        var results = service.updateIncludedInChargeBulk(List.of(1L, 2L), true);

        assertThat(results).hasSize(2);
        assertThat(timesheet1.getIncludedInCharge()).isTrue();
        assertThat(timesheet2.getIncludedInCharge()).isTrue();
        verify(repository, times(1)).saveAll(anyList());
    }

    @Test
    public void givenIds_whenCancelApproveBillingBulk_thenReturnDtoList() {
        var timesheet1 = Timesheet.builder().id(1L).build();
        var timesheet2 = Timesheet.builder().id(2L).build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(timesheet1, timesheet2));
        when(repository.saveAll(Mockito.anyList()))
                .thenReturn(List.of(timesheet1, timesheet2));
        assertThat(service.cancelApprovelBillingBulk(List.of(1L, 2L)))
                .isNotEmpty();
        assertThat(timesheet1.getIsBillingApproved()).isFalse();
        assertThat(timesheet2.getIsBillingApproved()).isFalse();
    }

    @Test
    public void givenIdsAndStartDate_whenGetUnbilledOthers_thenReturnDtoList() {
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .includedInCharge(true)
                .loggedTime(BigDecimal.valueOf(5))
                .billableTime(BigDecimal.TEN)
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .loggedTime(BigDecimal.valueOf(7))
                .billableTime(BigDecimal.valueOf(5))
                .build();
        var activityResponse = ActivityIdsResponse.builder()
                .payload(List.of(2L))
                .build();
        when(activityClient.getOtherActivityIds(1L)).thenReturn(activityResponse);
        when(repository.findByActivityIdInAndExpenseDateIsNullAndDateLessThanEqual(Mockito.anyList(), Mockito.any(LocalDate.class)))
                .thenReturn(List.of(timesheet1, timesheet2));
        var timesheetTotalTimeDto = service.getOtherUnbilledTimes(1L, LocalDate.now());

        assertThat(timesheetTotalTimeDto.getTotalIncludedInCharge())
                .isEqualTo(BigDecimal.valueOf(10));
    }

    @Test
    public void givenActivityIdAndDateRange_whenGetPageByDates_thenReturnDtoList() {
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .date(LocalDate.parse("2024-07-03"))
                .includedInCharge(Boolean.TRUE)
                .billableTime(BigDecimal.TEN)
                .loggedTime(BigDecimal.ONE)
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .date(LocalDate.parse("2024-07-03"))
                .includedInCharge(Boolean.TRUE)
                .billableTime(BigDecimal.TEN)
                .loggedTime(BigDecimal.ONE)
                .build();
        when(repository.findByActivityIdAndDateBetweenOrderByDateDesc(Mockito.anyLong(),
                Mockito.any(LocalDate.class),
                Mockito.any(LocalDate.class)))
                .thenReturn(List.of(timesheet1, timesheet2));
        var totalTimeDto = service.getAllByDates(1L, LocalDate.now(), LocalDate.now());
        assertThat(totalTimeDto.getTotalBillableTime()).isEqualTo(BigDecimal.valueOf(20));
        assertThat(totalTimeDto.getTotalLoggedTime()).isEqualTo(BigDecimal.valueOf(2));
    }

    @Test
    public void givenActivityWithTimesheets_whenGetUnbilledTimesByActivity_thenReturnDtoList() {
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .date(LocalDate.parse("2024-07-05"))
                .includedInCharge(Boolean.TRUE)
                .billableTime(BigDecimal.TEN)
                .loggedTime(BigDecimal.ONE)
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .date(LocalDate.parse("2024-07-03"))
                .expenseDate(LocalDate.parse("2024-07-03"))
                .includedInCharge(Boolean.FALSE)
                .billableTime(BigDecimal.TEN)
                .loggedTime(BigDecimal.ONE)
                .build();
        when(repository.findByActivityIdAndExpenseDateIsNull(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2));
        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2));
        var tsicPackage1 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-02"))
                .endDate(LocalDate.parse("2024-07-03"))
                .includedCharge(BigDecimal.TEN)
                .build();
        var tsicPackage2 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-04"))
                .endDate(null)
                .includedCharge(BigDecimal.valueOf(30))
                .build();
        when(activityClient.getActivity(Mockito.anyLong()))
                .thenReturn(ActivityResponse.builder()
                        .payload(ActivityResponse.Payload.builder()
                                .billingPeriodEnds(LocalDate.parse("2024-07-04"))
                                .tsicPackages(List.of(tsicPackage1, tsicPackage2))
                                .build())
                        .build());

        var totalTimeDto = service.getUnbilledTimesByActivity(1L);

        assertThat(totalTimeDto.getTotalBillableTime()).isEqualTo(BigDecimal.valueOf(20));
        assertThat(totalTimeDto.getTotalLoggedTime()).isEqualTo(BigDecimal.valueOf(2));
        assertThat(totalTimeDto.getTotalIncludedInCharge()).isEqualTo(BigDecimal.valueOf(10));
        assertThat(totalTimeDto.getTsicMin()).isEqualTo(BigDecimal.valueOf(1800));
        assertThat(totalTimeDto.getTsicUsedBefore()).isEqualTo(BigDecimal.valueOf(0));
        assertThat(totalTimeDto.getTsicUsedInCurrentProcess()).isEqualTo(BigDecimal.valueOf(10));
        assertThat(totalTimeDto.getTsicRemaining()).isEqualTo(BigDecimal.valueOf(1790));
    }

    @Test
    public void givenDate_whenApproveBySystem_thenReturnDtoList() {
        var timesheet1 = Timesheet.builder().id(1L).build();
        var timesheet2 = Timesheet.builder().id(2L).build();
        var beforeDate = LocalDate.now();
        var activityResponse = ActivityIdsResponse.builder().payload(List.of(2L)).build();
        when(activityClient.getTimesheetManuelApprovalActivityIds()).thenReturn(activityResponse);
        when(repository.findByActivityIdNotInAndDateBefore(activityResponse.getPayload(), beforeDate))
                .thenReturn(List.of(timesheet1, timesheet2));
        service.approveBySystem(beforeDate);
        assertThat(timesheet1.getIsApproved()).isTrue();
        assertThat(timesheet2.getIsApproved()).isTrue();
    }

    @Test
    public void givenActivityIdAndPackageStartDate_whenCalculateIncludedInCharge_thenSetIncludedInChargeFlagCorrectly() {
        var activityId = 1L;
        var packageStartDate = LocalDate.parse("2024-07-02");

        var timesheet1 = Timesheet.builder()
                .id(1L)
                .date(LocalDate.parse("2024-07-03"))
                .expenseDate(LocalDate.now())
                .includedInCharge(Boolean.TRUE)
                .billableTime(BigDecimal.TEN)
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .date(LocalDate.parse("2024-07-03"))
                .includedInCharge(Boolean.TRUE)
                .billableTime(BigDecimal.TEN)
                .build();
        var timesheet3 = Timesheet.builder()
                .id(3L)
                .date(LocalDate.parse("2024-07-03"))
                .includedInCharge(Boolean.FALSE)
                .billableTime(BigDecimal.TEN)
                .build();
        var timesheet4 = Timesheet.builder()
                .id(4L)
                .date(LocalDate.parse("2024-07-03"))
                .includedInCharge(Boolean.FALSE)
                .expenseDate(LocalDate.now())
                .billableTime(BigDecimal.valueOf(20))
                .build();
        var timesheet5 = Timesheet.builder()
                .id(5L)
                .date(LocalDate.parse("2024-07-03"))
                .includedInCharge(Boolean.FALSE)
                .billableTime(BigDecimal.valueOf(20))
                .build();
        when(repository.findByActivityId(activityId))
                .thenReturn(List.of(timesheet1, timesheet2, timesheet3, timesheet4, timesheet5));
        service.calculateIncludedInCharge(activityId, packageStartDate, BigDecimal.valueOf(1));
        assertThat(timesheet3.getIncludedInCharge()).isTrue();
        assertThat(timesheet4.getIncludedInCharge()).isFalse();
        assertThat(timesheet5.getIncludedInCharge()).isTrue();
    }

    @Test
    public void givenInvalidTimesheets_whenApproveBilling_thenThrowException() {
        var billingPeriodEnds = LocalDate.parse("2024-07-18");
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(LocalDate.now())
                .date(LocalDate.parse("2024-07-19"))
                .expenseCode("a")
                .build();
        var timesheet2 = Timesheet.builder()
                .id(1L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(null)
                .date(LocalDate.parse("2024-07-19"))
                .expenseCode("a")
                .build();
        var timesheet3 = Timesheet.builder()
                .id(1L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(LocalDate.now())
                .date(billingPeriodEnds)
                .expenseCode("a")
                .build();
        var timesheet4 = Timesheet.builder()
                .id(1L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(LocalDate.now())
                .date(LocalDate.parse("2024-07-19"))
                .expenseCode("b")
                .build();
        var timesheet5 = Timesheet.builder()
                .id(1L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(null)
                .date(billingPeriodEnds)
                .expenseCode("b")
                .build();

        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2, timesheet3, timesheet4, timesheet5));
        var activityResponse = ActivityResponse.builder()
                .payload(ActivityResponse.Payload.builder()
                        .billingAccountId(1L)
                        .billingPeriodEnds(billingPeriodEnds)
                        .build())
                .build();
        when(activityClient.getActivity(Mockito.anyLong())).thenReturn(activityResponse);
        var billingAccountDto = BillingAccountDto.builder()
                .currency("USD")
                .build();
        when(billingAccountService.getById(1L)).thenReturn(billingAccountDto);

        var approveBillingRequest = ApproveBillingRequest.builder()
                .activityId(anyLong())
                .build();

        assertThatThrownBy(() -> service.approveBilling(approveBillingRequest))
                .isInstanceOf(TimesheetValidationException.class);
    }

    @Test
    public void givenValidTimesheets_whenApproveBilling_thenSetExpenseDateToBillingPeriodEnd() {
        var billingPeriodEnds = LocalDate.parse("2024-07-18");
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(LocalDate.now())
                .date(LocalDate.parse("2024-07-19"))
                .expenseCode("a")
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(null)
                .isBillingApproved(true)
                .date(LocalDate.parse("2024-07-19"))
                .expenseCode("a")
                .build();
        var timesheet3 = Timesheet.builder()
                .id(3L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(LocalDate.now())
                .date(billingPeriodEnds)
                .expenseCode("a")
                .build();
        var timesheet4 = Timesheet.builder()
                .id(4L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(LocalDate.now())
                .date(LocalDate.parse("2024-07-19"))
                .expenseCode("b")
                .build();
        var timesheet5 = Timesheet.builder()
                .id(5L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(null)
                .isBillingApproved(true)
                .date(billingPeriodEnds)
                .expenseCode("b")
                .build();
        var timesheet6 = Timesheet.builder()
                .id(6L)
                .billableTime(BigDecimal.ONE)
                .expenseDate(null)
                .isBillingApproved(true)
                .date(LocalDate.parse("2024-07-19"))
                .expenseCode("a")
                .build();


        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2, timesheet3, timesheet4, timesheet5, timesheet6));
        var activityResponse = ActivityResponse.builder()
                .payload(ActivityResponse.Payload.builder()
                        .billingAccountId(1L)
                        .billingPeriodEnds(billingPeriodEnds)
                        .build())
                .build();
        when(activityClient.getActivity(Mockito.anyLong())).thenReturn(activityResponse);
        var parameterResponse = ParameterResponse.builder()
                .payload(ParameterResponse.Payload.builder()
                        .value("6")
                        .build())
                .build();
        when(parameterClient.getByKey(Mockito.anyString())).thenReturn(parameterResponse);
        var billingAccountDto = BillingAccountDto.builder()
                .currency("USD")
                .build();
        when(billingAccountService.getById(Mockito.anyLong())).thenReturn(billingAccountDto);
        when(paramCommandClient.getUnitPrice(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(UnitPriceResponse.builder().payload(BigDecimal.TEN).build());
        when(expenseService.save(Mockito.any())).thenReturn(ExpenseDto.builder().build());

        var approveBillingRequest = ApproveBillingRequest.builder()
                .activityId(anyLong())
                .build();
        service.approveBilling(approveBillingRequest);

        assertThat(timesheet5.getExpenseDate()).isEqualTo(billingPeriodEnds);
    }

    @Test
    public void givenActivityWithoutTsicPackage_whenGetUnbilledTimesheets_thenReturnTimesheetList() {
        var billingPeriodEnds = LocalDate.parse("2024-08-18");
        var activityId = 1L;
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .expenseDate(null)
                .date(LocalDate.parse("2024-07-18"))
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .expenseDate(null)
                .date(billingPeriodEnds)
                .build();
        var timesheet3 = Timesheet.builder()
                .id(3L)
                .expenseDate(LocalDate.parse("2024-07-19"))
                .date(LocalDate.parse("2024-07-19"))
                .build();
        var timesheet4 = Timesheet.builder()
                .id(5L)
                .expenseDate(null)
                .date(billingPeriodEnds.plusDays(1))
                .build();
        var activityDto = ActivityResponse.Payload.builder().build();
        when(activityClient.getActivity(activityId)).thenReturn(ActivityResponse.builder().payload(activityDto).build());
        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2, timesheet3, timesheet4));

        var unbilledTimesheets = service.getUnbilledTimesheets(activityId, billingPeriodEnds);

        assertThat(unbilledTimesheets).hasSize(2);
    }

    @Test
    public void givenTsicPackageEndDateAfterBillingPeriodWithNoTimesheets_whenGetUnbilledTimesheets_thenReturnEmptyList() {
        var billingPeriodEnds = LocalDate.parse("2024-08-18");
        var activityId = 1L;
        var packageDto = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-18"))
                .endDate(LocalDate.parse("2024-09-18"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(packageDto))
                .build();
        when(activityClient.getActivity(activityId)).thenReturn(ActivityResponse.builder().payload(activityDto).build());

        var unbilledTimesheets = service.getUnbilledTimesheets(activityId, billingPeriodEnds);

        assertThat(unbilledTimesheets).isEmpty();
    }

    @Test
    public void givenTsicPackageEndDateAfterBillingPeriodWithTimesheets_whenGetUnbilledTimesheets_thenReturnTimesheetList() {
        var billingPeriodEnds = LocalDate.parse("2024-08-18");
        var activityId = 1L;
        var packageDto = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-18"))
                .endDate(LocalDate.parse("2024-09-18"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(packageDto))
                .build();
        when(activityClient.getActivity(activityId)).thenReturn(ActivityResponse.builder().payload(activityDto).build());
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .expenseDate(null)
                .date(LocalDate.parse("2024-07-17"))
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .expenseDate(null)
                .date(billingPeriodEnds)
                .build();
        var timesheet3 = Timesheet.builder()
                .id(3L)
                .expenseDate(LocalDate.parse("2024-07-19"))
                .date(LocalDate.parse("2024-07-19"))
                .build();
        var timesheet4 = Timesheet.builder()
                .id(5L)
                .expenseDate(null)
                .date(billingPeriodEnds.plusDays(1))
                .build();
        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2, timesheet3, timesheet4));

        var unbilledTimesheets = service.getUnbilledTimesheets(activityId, billingPeriodEnds);

        assertThat(unbilledTimesheets).hasSize(1);
        assertThat(unbilledTimesheets.get(0)).isEqualTo(timesheet2);
    }

    @Test
    public void givenTsicPackageEndDateBeforeBillingPeriodWithFixedFee_whenGetUnbilledTimesheets_thenReturnEmptyList() {
        var billingPeriodEnds = LocalDate.parse("2024-08-18");
        var activityId = 1L;
        var packageDto = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-18"))
                .endDate(LocalDate.parse("2024-07-20"))
                .fixedFee(true)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(packageDto))
                .build();
        when(activityClient.getActivity(activityId)).thenReturn(ActivityResponse.builder().payload(activityDto).build());

        var unbilledTimesheets = service.getUnbilledTimesheets(activityId, billingPeriodEnds);

        assertThat(unbilledTimesheets).isEmpty();
    }

    @Test
    public void givenTsicPackageEndDateBeforeBillingPeriodWithoutFixedFee_whenGetUnbilledTimesheets_thenReturnTimesheetList() {
        var billingPeriodEnds = LocalDate.parse("2024-08-18");
        var activityId = 1L;
        var packageDto1 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-18"))
                .endDate(LocalDate.parse("2024-07-20"))
                .build();
        var packageDto2 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-21"))
                .endDate(LocalDate.parse("2024-07-25"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(packageDto1, packageDto2))
                .build();
        when(activityClient.getActivity(activityId)).thenReturn(ActivityResponse.builder().payload(activityDto).build());
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .expenseDate(null)
                .date(LocalDate.parse("2024-07-17"))
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .expenseDate(null)
                .date(billingPeriodEnds)
                .build();
        var timesheet3 = Timesheet.builder()
                .id(3L)
                .expenseDate(LocalDate.parse("2024-07-19"))
                .date(LocalDate.parse("2024-07-19"))
                .build();
        var timesheet4 = Timesheet.builder()
                .id(5L)
                .expenseDate(null)
                .date(billingPeriodEnds.plusDays(1))
                .build();
        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2, timesheet3, timesheet4));

        var unbilledTimesheets = service.getUnbilledTimesheets(activityId, billingPeriodEnds);

        assertThat(unbilledTimesheets).hasSize(1);
        assertThat(unbilledTimesheets.get(0)).isEqualTo(timesheet2);
    }

    @Test
    public void givenActivityIdAndStartDate_whenGetTimesByActivityAndStartDate_thenReturnCorrectTotalLoggedTime() {
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .expenseDate(null)
                .billableTime(BigDecimal.ONE)
                .loggedTime(BigDecimal.valueOf(60))
                .date(LocalDate.parse("2024-07-17"))
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .expenseDate(null)
                .billableTime(BigDecimal.ONE)
                .loggedTime(BigDecimal.valueOf(120))
                .date(LocalDate.parse("2024-07-18"))
                .build();
        when(repository.findByActivityIdAndExpenseDateIsNull(anyLong())).thenReturn(List.of(timesheet1, timesheet2));

        var totalTimeDto = service.getTimesByActivityAndStartDate(1L, LocalDate.parse("2024-07-18"));

        assertThat(totalTimeDto.getTotalLoggedTime()).isEqualByComparingTo(BigDecimal.valueOf(2));
    }

    @Test
    public void givenActivityIdAndEndDate_whenGetTimesByActivityAndEndDate_thenReturnCorrectTotalBillableTime() {
        var endDate = LocalDate.parse("2024-12-01");
        var timesheet1 = Timesheet.builder()
                .date(LocalDate.parse("2024-11-25"))
                .billableTime(BigDecimal.valueOf(50))
                .loggedTime(BigDecimal.ONE)
                .build();
        var timesheet2 = Timesheet.builder()
                .date(LocalDate.parse("2024-12-01"))
                .billableTime(BigDecimal.valueOf(70))
                .loggedTime(BigDecimal.ONE)
                .build();
        var timesheet3 = Timesheet.builder()
                .date(LocalDate.parse("2024-12-03"))
                .billableTime(BigDecimal.valueOf(20))
                .loggedTime(BigDecimal.ONE)
                .build();
        when(repository.findByActivityIdAndExpenseDateIsNull(1L))
                .thenReturn(List.of(timesheet1, timesheet2, timesheet3));

        var times = service.getTimesByActivityAndEndDate(1L, endDate);

        assertThat(times.getTotalBillableTime()).isEqualByComparingTo(BigDecimal.valueOf(2));
    }

    @Test
    public void givenTimesheetsAfterPeriod_whenGetUnbilledTimesheetsAfterPeriod_thenReturnMatchingTimesheets() {
        var billingPeriodEnds = LocalDate.parse("2024-08-18");
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .expenseDate(null)
                .date(LocalDate.parse("2024-08-19"))
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .expenseDate(null)
                .date(LocalDate.parse("2024-08-20"))
                .build();
        var timesheet3 = Timesheet.builder()
                .id(3L)
                .expenseDate(null)
                .date(LocalDate.parse("2024-08-18"))
                .build();
        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2, timesheet3));

        var timesheetsAfterPeriod = service.getUnbilledTimesheetsAfterPeriod(1L, billingPeriodEnds);

        assertThat(timesheetsAfterPeriod).hasSize(2);
        assertThat(timesheetsAfterPeriod).extracting(Timesheet::getId)
                .containsExactly(1L, 2L);
    }

    @Test
    public void givenNoTimesheetsAfterPeriod_whenGetUnbilledTimesheetsAfterPeriod_thenReturnEmptyList() {
        var billingPeriodEnds = LocalDate.parse("2024-12-31");
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .expenseDate(null)
                .date(LocalDate.parse("2024-12-31"))
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .expenseDate(null)
                .date(LocalDate.parse("2024-08-17"))
                .build();
        when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(timesheet1, timesheet2));

        var timesheetsAfterPeriod = service.getUnbilledTimesheetsAfterPeriod(1L, billingPeriodEnds);

        assertThat(timesheetsAfterPeriod).isEmpty();
    }

    @Test
    public void givenUnapprovedTimesheets_whenHasUnapproved_thenReturnTrue() {
        var timesheet1 = Timesheet.builder().id(1L).date(LocalDate.now()).isApproved(false).build();
        var timesheet2 = Timesheet.builder().id(2L).date(LocalDate.now().minusDays(1)).isApproved(true).build();
        when(repository.findByActivityId(Mockito.anyLong())).thenReturn(List.of(timesheet1, timesheet2));

        boolean result = service.hasAnyUnapproved(1L, LocalDate.now());

        assertThat(result).isTrue();
    }

    @Test
    public void givenAllApprovedTimesheets_whenHasUnapproved_thenReturnFalse() {
        var activityId = 1L;
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .date(LocalDate.parse("2025-01-01"))
                .isBillingApproved(true)
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .date(LocalDate.parse("2025-01-02"))
                .isBillingApproved(true)
                .build();
        when(repository.findByActivityId(activityId)).thenReturn(List.of(timesheet1, timesheet2));

        boolean result = service.hasAnyUnapproved(activityId, LocalDate.parse("2025-01-02"));

        assertThat(result).isFalse();
    }

    @Test
    public void givenNoTimesheets_whenHasUnapproved_thenReturnFalse() {
        when(repository.findByActivityId(Mockito.anyLong())).thenReturn(Collections.emptyList());

        boolean result = service.hasAnyUnapproved(1L, LocalDate.now());

        assertThat(result).isFalse();
    }

    @Test
    public void givenSomeUnapprovedTimesheets_whenHasUnapproved_thenReturnTrue() {
        var timesheet1 = Timesheet.builder()
                .id(1L)
                .date(LocalDate.parse("2025-01-01"))
                .isBillingApproved(true)
                .build();
        var timesheet2 = Timesheet.builder()
                .id(2L)
                .date(LocalDate.parse("2025-01-02"))
                .isBillingApproved(false)
                .build();
        when(repository.findByActivityId(Mockito.anyLong())).thenReturn(List.of(timesheet1, timesheet2));

        boolean result = service.hasAnyUnapproved(1L, LocalDate.parse("2025-01-02"));

        assertThat(result).isTrue();
    }

    @Test
    public void givenTimesheetsWithExpenseDate_whenCancelBillingOrder_thenSetExpenseDateNull() {
        var timesheet1 = Timesheet.builder().id(1L).expenseDate(LocalDate.now()).build();
        var timesheet2 = Timesheet.builder().id(2L).expenseDate(LocalDate.now()).build();
        var timesheetIds = List.of(1L, 2L);
        when(repository.findByIdIn(timesheetIds)).thenReturn(List.of(timesheet1, timesheet2));
        when(repository.saveAll(Mockito.anyList())).thenReturn(List.of(timesheet1, timesheet2));

        service.cancelBillingOrder(timesheetIds);

        assertThat(timesheet1.getExpenseDate()).isNull();
        assertThat(timesheet2.getExpenseDate()).isNull();
    }

    @Test
    public void givenEligibleTimesheets_whenUpdateTimesheetsForBillingCancellation_thenUpdateExpenseDates() {
        Long activityId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2025, 5, 1);
        var activityDto = ActivityResponse.Payload.builder()
                .id(activityId)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        ActivityResponse activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();
        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);

        Timesheet eligibleTimesheet = createTimesheet(1L, true, true, null);
        Timesheet notIncludedInChargeTimesheet = createTimesheet(2L, true, false, null);
        Timesheet notApprovedTimesheet = createTimesheet(3L, false, true, null);
        Timesheet alreadyHasExpenseDateTimesheet = createTimesheet(4L, true, true, LocalDate.now());

        List<Timesheet> timesheets = List.of(
                eligibleTimesheet,
                notIncludedInChargeTimesheet,
                notApprovedTimesheet,
                alreadyHasExpenseDateTimesheet
        );

        when(repository.findByActivityId(activityId)).thenReturn(timesheets);
        when(repository.saveAll(any())).thenAnswer(invocation -> invocation.getArgument(0));

        service.updateTimesheetsForBillingCancellation(activityId, billingPeriodEnds);

        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<Timesheet>> savedTimesheetsCaptor = ArgumentCaptor.forClass(List.class);
        verify(repository).saveAll(savedTimesheetsCaptor.capture());

        List<Timesheet> savedTimesheets = savedTimesheetsCaptor.getValue();

        assertThat(savedTimesheets)
                .hasSize(1)
                .extracting("id")
                .containsExactly(1L);

        assertThat(savedTimesheets.get(0).getExpenseDate())
                .isNotNull()
                .isEqualTo(LocalDate.now());

        ArgumentCaptor<TransferEvent> transferEventCaptor = ArgumentCaptor.forClass(TransferEvent.class);
        verify(producerService).sendTransfer(transferEventCaptor.capture());

        TransferEvent capturedEvent = transferEventCaptor.getValue();
        assertThat(capturedEvent.getType()).isEqualTo(TransferType.UPDATE);
        assertThat(capturedEvent.getDomainObjectId()).isEqualTo(1L);
        assertThat(capturedEvent.getDomain()).isEqualTo(Domain.IPMS_TIMESHEET);
    }

    @Test
    public void givenNoEligibleTimesheets_whenUpdateTimesheetsForBillingCancellation_thenDoNotUpdateAnyTimesheets() {
        Long activityId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2025, 5, 1);
        var activityDto = ActivityResponse.Payload.builder()
                .id(activityId)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        ActivityResponse activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();
        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);

        Timesheet notApprovedTimesheet = createTimesheet(1L, false, true, null);
        Timesheet notIncludedInChargeTimesheet = createTimesheet(2L, true, false, null);
        Timesheet alreadyHasExpenseDateTimesheet = createTimesheet(3L, true, true, LocalDate.now());

        List<Timesheet> timesheets = List.of(
                notApprovedTimesheet,
                notIncludedInChargeTimesheet,
                alreadyHasExpenseDateTimesheet
        );
        when(repository.findByActivityId(activityId)).thenReturn(timesheets);

        service.updateTimesheetsForBillingCancellation(activityId, billingPeriodEnds);

        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<Timesheet>> savedTimesheetsCaptor = ArgumentCaptor.forClass(List.class);
        verify(repository).saveAll(savedTimesheetsCaptor.capture());
        List<Timesheet> savedTimesheets = savedTimesheetsCaptor.getValue();
        assertThat(savedTimesheets).isEmpty();

        verify(producerService, never()).sendTransfer(any());
    }

    @Test
    public void givenEmptyTimesheetList_whenUpdateTimesheetsForBillingCancellation_thenHandleGracefully() {
        Long activityId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2025, 5, 1);
        var activityDto = ActivityResponse.Payload.builder()
                .id(activityId)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        ActivityResponse activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();
        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.findByActivityId(activityId)).thenReturn(Collections.emptyList());

        service.updateTimesheetsForBillingCancellation(activityId, billingPeriodEnds);

        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<Timesheet>> savedTimesheetsCaptor = ArgumentCaptor.forClass(List.class);
        verify(repository).saveAll(savedTimesheetsCaptor.capture());
        List<Timesheet> savedTimesheets = savedTimesheetsCaptor.getValue();
        assertThat(savedTimesheets).isEmpty();

        verify(producerService, never()).sendTransfer(any());
    }

    @Test
    public void givenMultipleEligibleTimesheets_whenUpdateTimesheetsForBillingCancellation_thenUpdateAllExpenseDates() {
        Long activityId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2025, 5, 1);
        var activityDto = ActivityResponse.Payload.builder()
                .id(activityId)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        ActivityResponse activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();
        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);

        Timesheet eligibleTimesheet1 = createTimesheet(1L, true, true, null);
        Timesheet eligibleTimesheet2 = createTimesheet(2L, true, true, null);
        Timesheet ineligibleTimesheet = createTimesheet(3L, false, true, null);
        List<Timesheet> timesheets = List.of(
                eligibleTimesheet1,
                eligibleTimesheet2,
                ineligibleTimesheet
        );
        when(repository.findByActivityId(activityId)).thenReturn(timesheets);
        when(repository.saveAll(any())).thenAnswer(invocation -> invocation.getArgument(0));

        service.updateTimesheetsForBillingCancellation(activityId, billingPeriodEnds);

        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<Timesheet>> savedTimesheetsCaptor = ArgumentCaptor.forClass(List.class);
        verify(repository).saveAll(savedTimesheetsCaptor.capture());

        List<Timesheet> savedTimesheets = savedTimesheetsCaptor.getValue();

        assertThat(savedTimesheets)
                .hasSize(2)
                .extracting("id")
                .containsExactlyInAnyOrder(1L, 2L);

        assertThat(savedTimesheets)
                .allMatch(ts -> ts.getExpenseDate() != null && ts.getExpenseDate().equals(LocalDate.now()));

        verify(producerService, times(2)).sendTransfer(any(TransferEvent.class));
    }

    private Timesheet createTimesheet(Long id, Boolean isBillingApproved, Boolean includedInCharge, LocalDate expenseDate) {
        return Timesheet.builder()
                .id(id)
                .date(LocalDate.of(2025, 3, 20))
                .isBillingApproved(isBillingApproved)
                .includedInCharge(includedInCharge)
                .expenseDate(expenseDate)
                .activityId(123L)
                .build();
    }

    @Test
    public void givenTimesheetWithDateAfterPackageStartDate_whenSave_thenSaveSuccessfully() {
        var activityId = 1L;
        var packageStartDate = LocalDate.parse("2024-07-01");
        var timesheetDate = LocalDate.parse("2024-07-15");

        var tsicPackage = TSICPackageDto.builder()
                .startDate(packageStartDate)
                .endDate(null)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(tsicPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenTimesheetWithDateEqualToPackageStartDate_whenSave_thenSaveSuccessfully() {
        var activityId = 1L;
        var packageStartDate = LocalDate.parse("2024-07-01");

        var tsicPackage = TSICPackageDto.builder()
                .startDate(packageStartDate)
                .endDate(null)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(tsicPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(packageStartDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenTimesheetWithDateBeforePackageStartDate_whenSave_thenThrowValidationException() {
        var activityId = 1L;
        var packageStartDate = LocalDate.parse("2024-07-01");
        var timesheetDate = LocalDate.parse("2024-06-30");

        var tsicPackage = TSICPackageDto.builder()
                .startDate(packageStartDate)
                .endDate(null)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(tsicPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        Mockito.doThrow(new TimesheetValidationException(
                        TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE,
                        packageStartDate.toString()))
                .when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatThrownBy(() -> service.save(timesheetDto))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE);
    }

    @Test
    public void givenActivityWithNoTsicPackages_whenSave_thenSaveSuccessfully() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-15");

        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(Collections.emptyList())
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenActivityWithOnlyInactiveTsicPackages_whenSave_thenSaveSuccessfully() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-15");

        var tsicPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(tsicPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenActivityWithMultipleTsicPackages_whenSave_thenValidateAgainstActiveTsicPackage() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-08-15");

        var oldTsicPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var activeTsicPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-08-01"))
                .endDate(null)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(oldTsicPackage, activeTsicPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenTimesheetWithDateBeforeActiveTsicPackageStartDate_whenSave_thenThrowValidationException() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-31");

        var oldTsicPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var activeTsicPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-08-01"))
                .endDate(null)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(oldTsicPackage, activeTsicPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        Mockito.doThrow(new TimesheetValidationException(
                        TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE,
                        LocalDate.parse("2024-08-01").toString()))
                .when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatThrownBy(() -> service.save(timesheetDto))
                .isInstanceOf(TimesheetValidationException.class);
    }

    @Test
    public void givenTimesheetWithDateBeforePackageStartDate_whenUpdate_thenThrowValidationException() {
        var activityId = 1L;
        var packageStartDate = LocalDate.parse("2024-07-01");
        var timesheetDate = LocalDate.parse("2024-06-30");

        var tsicPackage = TSICPackageDto.builder()
                .startDate(packageStartDate)
                .endDate(null)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(tsicPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.findById(1L)).thenReturn(Optional.of(Timesheet.builder().id(1L).build()));
        Mockito.doThrow(new TimesheetValidationException(
                        TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE,
                        packageStartDate.toString()))
                .when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatThrownBy(() -> service.update(timesheetDto))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE);
    }

    @Test
    public void givenTimesheetWithValidDate_whenUpdate_thenUpdateSuccessfully() {
        var activityId = 1L;
        var packageStartDate = LocalDate.parse("2024-07-01");
        var timesheetDate = LocalDate.parse("2024-07-15");

        var tsicPackage = TSICPackageDto.builder()
                .startDate(packageStartDate)
                .endDate(null)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(tsicPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        var existingTimesheet = Timesheet.builder().id(1L).activityId(activityId).build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.findById(1L)).thenReturn(Optional.of(existingTimesheet));
        when(repository.save(any(Timesheet.class))).thenReturn(existingTimesheet);
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.update(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenActivityWithNoPackages_whenSave_thenSaveSuccessfullyWithoutEndDateValidation() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-15");

        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(Collections.emptyList())
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenActivityWithActivePackage_whenSave_thenSaveSuccessfullyWithoutEndDateValidation() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-15");

        var activePackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(null)
                .build();
        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(closedPackage, activePackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenActivityWithOnlyClosedPackages_whenSaveTimesheetAfterLatestEndDate_thenSaveSuccessfully() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-08-15");

        var closedPackage1 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var closedPackage2 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(closedPackage1, closedPackage2))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenActivityWithOnlyClosedPackages_whenSaveTimesheetEqualToLatestEndDate_thenSaveSuccessfully() {
        var activityId = 1L;
        var latestEndDate = LocalDate.parse("2024-07-31");

        var closedPackage1 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var closedPackage2 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(latestEndDate)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(closedPackage1, closedPackage2))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(latestEndDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
    }

    @Test
    public void givenActivityWithOnlyClosedPackages_whenSaveTimesheetBeforeLatestEndDate_thenThrowValidationException() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-30");

        var closedPackage1 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var closedPackage2 = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(closedPackage1, closedPackage2))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        Mockito.doThrow(new TimesheetValidationException(
                        TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE,
                        LocalDate.parse("2024-07-31").toString()))
                .when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatThrownBy(() -> service.save(timesheetDto))
                .isInstanceOf(TimesheetValidationException.class);
    }

    @Test
    public void givenActivityWithOnlyClosedPackages_whenUpdateTimesheetBeforeLatestEndDate_thenThrowValidationException() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-15");

        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(closedPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.findById(1L)).thenReturn(Optional.of(Timesheet.builder().id(1L).build()));
        Mockito.doThrow(new TimesheetValidationException(
                        TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE,
                        LocalDate.parse("2024-07-31").toString()))
                .when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatThrownBy(() -> service.update(timesheetDto))
                .isInstanceOf(TimesheetValidationException.class);
    }

    @Test
    public void givenActivityWithOnlyClosedPackages_whenUpdateTimesheetAfterLatestEndDate_thenUpdateSuccessfully() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-08-15");

        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(closedPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        var existingTimesheet = Timesheet.builder().id(1L).activityId(activityId).build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.findById(1L)).thenReturn(Optional.of(existingTimesheet));
        when(repository.save(any(Timesheet.class))).thenReturn(existingTimesheet);

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.update(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
    }

    @Test
    public void givenActivityWithMultipleClosedPackages_whenSave_thenValidateAgainstLatestClosedPackageEndDate() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-09-15");

        var oldClosedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-06-01"))
                .endDate(LocalDate.parse("2024-06-30"))
                .build();
        var latestClosedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-08-01"))
                .endDate(LocalDate.parse("2024-08-31"))
                .build();
        var earlierClosedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(LocalDate.parse("2024-07-31"))
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(oldClosedPackage, earlierClosedPackage, latestClosedPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.save(any(Timesheet.class))).thenReturn(Timesheet.builder().id(1L).build());
        Mockito.doNothing().when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatCode(() -> service.save(timesheetDto))
                .doesNotThrowAnyException();

        verify(activityClient).getActivity(activityId);
        verify(repository).save(any(Timesheet.class));
        verify(timesheetValidator).validateTimesheetDate(eq(timesheetDto), anyList());
    }

    @Test
    public void givenActivityWithOnlyClosedPackages_whenSaveTimesheetBeforeLatestEndDate_thenThrowCorrectValidationException() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-30");
        var packageEndDate = LocalDate.parse("2024-07-31");

        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(packageEndDate)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(closedPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        Mockito.doThrow(new TimesheetValidationException(
                        TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE,
                        packageEndDate.toString()))
                .when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatThrownBy(() -> service.save(timesheetDto))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE)
                .hasFieldOrPropertyWithValue("params", new Object[]{packageEndDate.toString()});
    }

    @Test
    public void givenActivityWithOnlyClosedPackages_whenUpdateTimesheetBeforeLatestEndDate_thenThrowCorrectValidationException() {
        var activityId = 1L;
        var timesheetDate = LocalDate.parse("2024-07-15");
        var packageEndDate = LocalDate.parse("2024-07-31");

        var closedPackage = TSICPackageDto.builder()
                .startDate(LocalDate.parse("2024-07-01"))
                .endDate(packageEndDate)
                .build();
        var activityDto = ActivityResponse.Payload.builder()
                .tsicPackages(List.of(closedPackage))
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(activityDto)
                .build();

        when(activityClient.getActivity(activityId)).thenReturn(activityResponse);
        when(repository.findById(1L)).thenReturn(Optional.of(Timesheet.builder().id(1L).build()));
        Mockito.doThrow(new TimesheetValidationException(
                        TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE,
                        packageEndDate.toString()))
                .when(timesheetValidator).validateTimesheetDate(any(TimesheetDto.class), anyList());

        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .activityId(activityId)
                .date(timesheetDate)
                .build();

        assertThatThrownBy(() -> service.update(timesheetDto))
                .isInstanceOf(TimesheetValidationException.class)
                .hasFieldOrPropertyWithValue("code", TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE)
                .hasFieldOrPropertyWithValue("params", new Object[]{packageEndDate.toString()});
    }

}
