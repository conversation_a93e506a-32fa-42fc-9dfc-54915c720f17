package com.ipms.activity.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "customs_training_dates")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class CustomsTrainingDate extends VersionedEntity {

    @Column
    private LocalDate trainingInstructionDate;

    @Column
    private Long regionalCustoms;

    @Column
    private String city;

    @Column
    private Boolean onlineTraining;

    @Column
    private LocalDate trainingDate;

    @Column
    private Long activityId;
}
