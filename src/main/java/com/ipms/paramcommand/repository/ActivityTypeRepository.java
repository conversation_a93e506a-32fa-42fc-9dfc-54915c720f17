package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.ActivityType;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ActivityTypeRepository extends CrudRepository<ActivityType, Long>, JpaSpecificationExecutor<ActivityType> {
    Optional<ActivityType> findByCountryAndMatterTypeAndActivity(String country, String matterType, String activity);
}
