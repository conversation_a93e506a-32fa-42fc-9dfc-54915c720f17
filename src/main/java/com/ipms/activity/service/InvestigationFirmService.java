package com.ipms.activity.service;

import com.ipms.activity.dto.InvestigationFirmDto;
import com.ipms.activity.model.InvestigationFirm;

import java.util.List;
import java.util.Set;

public interface InvestigationFirmService {
    InvestigationFirmDto update(InvestigationFirmDto investigationFirmDto, Long id);
    Set<InvestigationFirm> saveList(List<InvestigationFirmDto> dtoList);
    List<InvestigationFirmDto> getByName(String name);
    InvestigationFirmDto getById(Long id);
    void delete(Long id);
}
