package com.ipms.activity.specification;

import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.CourtActionType;
import com.ipms.activity.enums.OverTimeChoice;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.CourtAction;
import com.ipms.activity.model.TSICPackage;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class ActivitySpecification extends AbstractSpecification<Activity> {
    private static final String PACKAGE_END_DATE = "endDate";
    private static final String PACKAGE_FIXED_FEE = "fixedFee";
    private static final String PACKAGE_OVER_TIME = "overTime";

    private final List<String> types;
    private final List<String> statuses;
    private List<Long> matterIds;
    private List<Long> firmIds;
    private List<Long> documentIds;
    private String agentReference;
    private List<LocalDateTime> instructionDates;
    private String courtActionStage;
    private String decisionNumber;
    private List<LocalDateTime> completionDates;
    private String postalTrackingNumber;
    private List<LocalDateTime> cdLetterDates;
    private List<LocalDateTime> notificationDates;
    private String officialDocumentNumber;
    private List<String> courtActions;
    private List<LocalDateTime> complaintDates;
    private String prosecutionNumber;
    private List<LocalDate> filingDate;
    private List<Long> quotationIds;
    private List<Long> expenseIds;
    private List<Long> billingAccountIds;
    private OverTimeChoice overTimeChoice;
    private Long matterId;
    private LocalDate statusDueDateStart;
    private LocalDate statusDueDateEnd;

    @Override
    public Predicate toPredicate(Root<Activity> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Optional.ofNullable(types)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("type").in(types.stream().map(ActivityType::valueOf).toList()))));
        Optional.ofNullable(statuses)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("status").in(statuses.stream().map(ActivityStatus::valueOf).toList()))));
        Optional.ofNullable(matterIds)
                .ifPresent(value -> predicate.getExpressions().add(root.get("matterId").in(value)));
        Optional.ofNullable(firmIds)
                .ifPresent(value -> predicate.getExpressions().add(root.get("firmId").in(value)));
        Optional.ofNullable(documentIds)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.join("documents").in(value))));
        Optional.ofNullable(agentReference)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(cb.upper(root.get("agentReference")), value.toUpperCase())));
        Optional.ofNullable(instructionDates)
                .ifPresent(value -> {
                    if (value.size() == 2) {
                        predicate.getExpressions().add(cb.between(root.get("instructionDate"), value.get(0), value.get(1)));
                    }
                });
        Optional.ofNullable(courtActionStage)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("courtAction"), value)));
        Optional.ofNullable(decisionNumber)
                .ifPresent(value ->
                    predicate.getExpressions().add(cb.and(root.get("customsSuspension").get("decisionNumber").in(value))));
        Optional.ofNullable(completionDates)
                .ifPresent(value -> {
                    if (value.size() == 2) {
                        predicate.getExpressions().add(cb.between(root.get("completionDate"), value.get(0), value.get(1)));
                    }
                });
        Optional.ofNullable(postalTrackingNumber)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("postalTrackingNumber"), value)));
        Optional.ofNullable(cdLetterDates)
                .ifPresent(value -> {
                    if (value.size() == 2) {
                        predicate.getExpressions().add(cb.between(root.get("date"), value.get(0), value.get(1)));
                    }
                });
        Optional.ofNullable(notificationDates)
                .ifPresent(value -> {
                    if (value.size() == 2) {
                        predicate.getExpressions().add(cb.between(root.get("notificationDate"), value.get(0), value.get(1)));
                    }
                });
        Optional.ofNullable(officialDocumentNumber)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("officialDocumentNumber"), value)));
        Optional.ofNullable(courtActions)
                .ifPresent(value -> {
                    Join<Activity, CourtAction> courtActionJoin = root.join("courtAction");
                    Join<CourtAction, CourtActionType> courtActionTypeJoin = courtActionJoin.join("types", JoinType.LEFT);
                    predicate.getExpressions().add(courtActionTypeJoin.get("type").in(value));
                });
        Optional.ofNullable(complaintDates)
                .ifPresent(value -> predicate.getExpressions().add(root.get("complaintDate").in(value)));
        Optional.ofNullable(prosecutionNumber)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("prosecutionNumber"), value)));
        Optional.ofNullable(filingDate)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("filingDate"), value)));
        Optional.ofNullable(quotationIds)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.join("quotations").in(value))));
        Optional.ofNullable(expenseIds)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.join("expenses").in(value))));
        Optional.ofNullable(billingAccountIds)
                .ifPresent(value -> predicate.getExpressions().add(root.get("billingAccountId").in(value)));
        Optional.ofNullable(overTimeChoice)
                .ifPresent(value -> {
                    Join<Activity, TSICPackage> tsicPackageJoin = root.join("tsicPackages", JoinType.LEFT);

                    switch (value) {
                        case YES -> applyOverTimeYesFilter(tsicPackageJoin, cb, predicate);
                        case NO -> applyOverTimeNoFilter(tsicPackageJoin, cb, predicate);
                        case NONE -> applyOverTimeNoneFilter(root, tsicPackageJoin, cb, predicate);
                    }
                });
        Optional.ofNullable(matterId)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("matterId"), value)));
        Optional.ofNullable(statusDueDateStart)
                .ifPresent(value ->
                        predicate.getExpressions().add(cb.greaterThanOrEqualTo(root.get("statusDueDate"), value)));
        Optional.ofNullable(statusDueDateEnd)
                .ifPresent(value ->
                        predicate.getExpressions().add(cb.lessThanOrEqualTo(root.get("statusDueDate"), value)));

        return predicate;
    }

    private void applyOverTimeYesFilter(Join<Activity, TSICPackage> tsicPackageJoin,
                                        CriteriaBuilder cb,
                                        Predicate predicate) {
        predicate.getExpressions().add(
                cb.and(
                        cb.isNull(tsicPackageJoin.get(PACKAGE_END_DATE)),
                        cb.isTrue(tsicPackageJoin.get(PACKAGE_FIXED_FEE)),
                        cb.isTrue(tsicPackageJoin.get(PACKAGE_OVER_TIME))
                )
        );
    }

    private void applyOverTimeNoFilter(Join<Activity, TSICPackage> tsicPackageJoin,
                                       CriteriaBuilder cb,
                                       Predicate predicate) {
        predicate.getExpressions().add(
                cb.and(
                        cb.isNull(tsicPackageJoin.get(PACKAGE_END_DATE)),
                        cb.isTrue(tsicPackageJoin.get(PACKAGE_FIXED_FEE)),
                        cb.or(
                                cb.isFalse(tsicPackageJoin.get(PACKAGE_OVER_TIME)),
                                cb.isNull(tsicPackageJoin.get(PACKAGE_OVER_TIME))
                        )
                )
        );
    }

    private void applyOverTimeNoneFilter(Root<Activity> root,
                                         Join<Activity, TSICPackage> tsicPackageJoin,
                                         CriteriaBuilder cb,
                                         Predicate predicate) {
        predicate.getExpressions().add(
                cb.or(
                        cb.isEmpty(root.get("tsicPackages")),
                        cb.and(
                                cb.isNull(tsicPackageJoin.get(PACKAGE_END_DATE)),
                                cb.or(
                                        cb.isFalse(tsicPackageJoin.get(PACKAGE_FIXED_FEE)),
                                        cb.isNull(tsicPackageJoin.get(PACKAGE_FIXED_FEE))
                                )
                        )
                )
        );
    }
}
