package com.ipms.activity.specification;

import com.ipms.activity.model.CustomsTrainingDate;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class CustomsTrainingDateSpecification extends AbstractSpecification<CustomsTrainingDate> {

    private Long activityId;

    private final List<String> cities;

    private final List<Long> regionalCustoms;

    @Override
    public Predicate toPredicate(Root<CustomsTrainingDate> root, CriteriaQuery<?> query, CriteriaBuilder cb){
        var predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(activityId)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("activityId"), value)));
        Optional.ofNullable(cities)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("city").in(cities))));
        Optional.ofNullable(regionalCustoms)
                .ifPresent(value -> predicate.getExpressions().add(root.get("regionalCustoms").in(value)));

        return predicate;
    }

}
