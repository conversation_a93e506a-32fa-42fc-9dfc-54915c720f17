package com.ipms.document.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.document.enums.DocumentResult;
import com.ipms.document.enums.DocumentType;
import com.ipms.document.model.Document;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.*;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class DocumentSpecification extends AbstractSpecification<Document> {
    private final List<String> types;
    private final Long firmId;
    private final Long expertId;
    private final String localAttorneyId;
    private List<DocumentResult> results;

    @Override
    public Predicate toPredicate(Root<Document> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        Join<Document, Long> firmsJoin = root.join("firms");
        Join<Document, Long> expertsJoin = root.join("experts", JoinType.LEFT);
        Join<Document, Long> localAttorneyJoin = root.join("localAttorneys", JoinType.LEFT);

        var predicate = super.toPredicate(root, query, cb);
        Optional.ofNullable(results)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("result").in(results.stream().toList()))));
        Optional.ofNullable(types)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("type").in(types.stream().map(DocumentType::valueOf).toList()))));
        Optional.ofNullable(firmId)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(firmsJoin.in(value))));
        Optional.ofNullable(expertId)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(expertsJoin.in(value))));
        Optional.ofNullable(localAttorneyId)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(localAttorneyJoin.in(value))));
        query.distinct(true);
        return predicate;
    }

}
