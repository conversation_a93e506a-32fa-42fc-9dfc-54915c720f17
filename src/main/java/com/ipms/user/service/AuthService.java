package com.ipms.user.service;

import com.ipms.user.dto.auth.AuthDto;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;

import java.util.List;

public interface AuthService {

    AuthDto getAccessToken(String username, String password, String clientId, String otp);

    AuthDto getRefreshToken(String refreshToken, String clientId);

    void logout(String accessToken, String refreshToken, String clientId);

    UserInfoDto getUserInfo(String clientId);

    AuthDto getAccessToken();

    List<UserInfoDto> getUserList(String accessToken);

    List<RoleDto> getRoleList(String accessToken);

    List<UserInfoDto> getUserListByRole(String accessToken, List<String> roles);

    List<UserInfoDto> getUsersByRole(String accessToken, String role);

    List<UserInfoDto> getUserListWithRole(String accessToken);

    UserInfoDto getByEmail(String email, String accessToken);

    UserInfoDto getByUsername(String username, String accessToken);

    UserInfoDto getExpenseCodesByUsername(String username, String accessToken, List<String> expenseCodes);
}
