package com.ipms.integration.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.integration.dto.PriceDto;
import com.ipms.integration.service.PriceService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;


@RequiredArgsConstructor
@RestController
@RequestMapping("/price")
@Tag(name = "price", description = "This endpoint contains jde price APIs ")
public class PriceController {
    private final PriceService service;

    @PutMapping("/{fromUpdateDate}/{toUpdateDate}")
    public BaseResponse<List<PriceDto>> fetchAllPrices(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromUpdateDate,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toUpdateDate) {
        return BaseResponse.<List<PriceDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.fetchPrices(fromUpdateDate, toUpdateDate))
                .build();
    }
}
