package com.ipms.paramcommand.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.paramcommand.model.CountryMatterType;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.*;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class CountryMatterTypeSpecification extends AbstractSpecification<CountryMatterType> {
    private final List<String> countries;
    private final List<String> matterTypes;

    @Override
    public Predicate toPredicate(Root<CountryMatterType> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(matterTypes)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("matterType").in(values))));
        Optional.ofNullable(countries)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("country").in(values))));
        return predicate;
    }
}
