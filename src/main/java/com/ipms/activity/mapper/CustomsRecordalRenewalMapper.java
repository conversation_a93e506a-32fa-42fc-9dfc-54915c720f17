package com.ipms.activity.mapper;

import com.ipms.activity.dto.CustomsRecordalRenewalDto;
import com.ipms.activity.model.CustomsRecordalRenewal;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface CustomsRecordalRenewalMapper {
    CustomsRecordalRenewal toCustomsRecordalRenewal(CustomsRecordalRenewalDto customsRecordalRenewalDto);
    CustomsRecordalRenewal toCustomsRecordalRenewalFromDto(CustomsRecordalRenewalDto customsRecordalRenewalDto, @MappingTarget CustomsRecordalRenewal customsRecordalRenewal);
    CustomsRecordalRenewalDto toCustomsRecordalRenewalDto(CustomsRecordalRenewal customsRecordalRenewal);
}
