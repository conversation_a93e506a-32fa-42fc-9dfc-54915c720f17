package com.ipms.activity.repository;

import com.ipms.activity.model.TSICPackage;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface TSICPackageRepository extends PagingAndSortingRepository<TSICPackage, Long> {
    List<TSICPackage> findAllByActivityId(Long activityId);
    
    @Query("SELECT DISTINCT t.activity.id FROM TSICPackage t")
    List<Long> findDistinctActivityIds();
}
