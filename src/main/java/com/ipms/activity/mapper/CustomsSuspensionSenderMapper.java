package com.ipms.activity.mapper;

import com.ipms.activity.dto.CustomsSuspensionsSenderDto;
import com.ipms.activity.model.CustomsSuspensionsSender;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomsSuspensionSenderMapper {

    CustomsSuspensionsSenderDto toDto(CustomsSuspensionsSender suspensionsSender);

    CustomsSuspensionsSender toCustomsSuspensionsSender(CustomsSuspensionsSenderDto dto);
}
