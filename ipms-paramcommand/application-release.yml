eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:release-ipms-paramcommand}.release-ipms-paramcommand
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  parameter-create-topic: parameterCreateReleaseTopic
  parameter-update-topic: parameterUpdateReleaseTopic
  parameter-delete-topic: parameterDeleteReleaseTopic
  notification-topic: notificationReleaseTopic
  data-transfer-topic: dataTransferReleaseTopic
  exchange-rate-topic: exchangeRateReleaseTopic
  jde-price-topic: jdePriceReleaseTopic

spring:
  datasource:
    url: ************************************************************************************
    username: ipms_release_user
    password: zwk9w4TpjzG7TVX9
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-release/protocol/openid-connect/token
  cache:
    type: redis
    redis:
      time-to-live: 21600000
  redis:
    database: 0
    password: pbX!Vf!749
    sentinel:
      master: mymaster
      password: pbX!Vf!749
      nodes:
        - redis-node-0.redis-headless.redis.svc.cluster.local:26379
        - redis-node-1.redis-headless.redis.svc.cluster.local:26379
        - redis-node-2.redis-headless.redis.svc.cluster.local:26379
    lettuce:
      shutdown-timeout: 200ms

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-release
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk