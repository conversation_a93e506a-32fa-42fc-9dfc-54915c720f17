package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.ExpenseCode;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ExpenseCodeRepository extends CrudRepository<ExpenseCode, Long>, JpaSpecificationExecutor<ExpenseCode> {
    Optional<ExpenseCode> findByCode(String expenseCode);
    List<ExpenseCode> findByCodeIn(List<String> expenseCodes);
}
