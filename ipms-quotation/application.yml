server:
  port: 8061
  servlet:
    context-path: /api/quotation
  max-http-header-size: 100KB

app:
  allowed-origins: >
    *
  allowed-headers: >
    *
  allowed-methods: >
    GET,
    POST,
    PUT,
    PATCH,
    DELETE,
    OPTIONS,
    HEAD

spring:
  application:
    name: ipms-quotation

springdoc:
  api-docs:
    path: /docs
  swagger-ui:
    path: /docs/ui
    tagsSorter: alpha
    operationsSorter: alpha

management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health,info,metrics"
  endpoint:
    health:
      probes:
        enabled: true
      show-details: when-authorized
  health:
    livenessState:
      enabled: true
    readinessState:
      enabled: true
    db:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.zaxxer.hikari: DEBUG
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN