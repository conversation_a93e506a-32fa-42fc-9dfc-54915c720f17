package com.ipms.activity.controller;

import com.ipms.activity.dto.CustomsSuspensionsShippingDto;
import com.ipms.activity.service.CustomsSuspensionsShippingService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/customs-suspensions/shippings")
public class CustomsSuspensionsShippingController {
    private final CustomsSuspensionsShippingService service;

    @GetMapping("/search/{name}")
    public BaseResponse<List<CustomsSuspensionsShippingDto>> search(@PathVariable @NotEmpty @Size(min = 3) String name) {
        var shippings = service.findAllByName(name);
        return BaseResponse.<List<CustomsSuspensionsShippingDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(shippings)
                .build();
    }
}
