package com.ipms.activity.model;

import com.ipms.activity.enums.PIResult;
import com.ipms.activity.enums.PIType;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "preliminary_injunction_detail")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class PreliminaryInjunctionDetail extends VersionedEntity {

    @Column
    private PIType type;

    @Column
    private LocalDate filingDate;

    @Column
    private PIResult result;

    @Column
    private LocalDate notificationDate;

    @Column
    private LocalDate executionDate;

    @Column
    private String executionOffice;

    @Column
    private String executionNo;

    @Column
    private LocalDate executionOpeningDate;

    @Column
    private String instructionFileNo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "preliminary_injunction_id")
    private PreliminaryInjunction preliminaryInjunction;
}
