package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.Customs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CustomsRepository extends JpaRepository<Customs, Long>, JpaSpecificationExecutor<Customs> {
    Optional<Customs> findByName(String name);
}
