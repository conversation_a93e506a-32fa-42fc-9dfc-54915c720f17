package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ipms.activity.enums.UseInvestigationType;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.Set;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "use_investigation")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class UseInvestigation extends VersionedEntity {
    @Column
    private UseInvestigationType type;

    @Column
    private LocalDate completionDate;

    @Column
    private String researcher;

    @ManyToMany(cascade = CascadeType.ALL)
    @JoinTable(
            name = "use_investigation_firm",
            joinColumns = @JoinColumn(name = "use_investigation_id"),
            inverseJoinColumns = @JoinColumn(name = "investigation_firm_id"))
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private Set<InvestigationFirm> investigationFirms;

    @OneToOne(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinColumn(name = "activity_id", referencedColumnName = "id")
    @JsonBackReference
    @NotAudited
    private Activity activity;
}
