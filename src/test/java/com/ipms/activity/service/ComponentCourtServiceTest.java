package com.ipms.activity.service;

import com.ipms.activity.dto.ComponentCourtDto;
import com.ipms.activity.exception.ComponentCourtNotFoundException;
import com.ipms.activity.mapper.ComponentCourtMapper;
import com.ipms.activity.mapper.ComponentCourtMapperImpl;
import com.ipms.activity.model.ComponentCourt;
import com.ipms.activity.repository.ComponentCourtRepository;
import com.ipms.activity.service.impl.ComponentCourtServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;

@RunWith(SpringRunner.class)
public class ComponentCourtServiceTest {
    @MockBean
    private ComponentCourtService service;

    @Mock
    private ComponentCourtRepository repository;

    private final ComponentCourtMapper mapper = new ComponentCourtMapperImpl();

    private ComponentCourt componentCourt;
    private ComponentCourtDto componentCourtDto;

    @Before
    public void setUp() {
        service = new ComponentCourtServiceImpl(repository, mapper);

        componentCourt = new ComponentCourt();
        componentCourt.setActivityId(1L);
        componentCourt.setCourtId(1L);
        componentCourt.setFilingDate(LocalDate.now());
        componentCourt.setDocketNo("12345");

        componentCourtDto = new ComponentCourtDto();
        componentCourtDto.setActivityId(1L);
        componentCourtDto.setCourtId(1L);
        componentCourtDto.setFilingDate(LocalDate.now());
        componentCourtDto.setDocketNo("12345");
    }

    @Test
    public void whenSave_thenReturnComponentCourtDto() {
        var componentCourt = ComponentCourt.builder()
                .id(1L)
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        var componentCourtDto = ComponentCourtDto.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(repository.save(Mockito.any(ComponentCourt.class)))
                .thenReturn(componentCourt);
        assertThat(service.save(componentCourtDto).getId())
                .isEqualTo(componentCourt.getId());
    }

    @Test
    public void givenNotFoundCourt_whenUpdate_thenThrowComponentCourtNotFoundException() {
        var componentCourtDto = ComponentCourtDto.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(()->service.update(componentCourtDto,1L));
        assertThat(thrown).isInstanceOf(ComponentCourtNotFoundException.class);
    }

    @Test
    public void whenUpdate_thenReturnComponentCourtDto() {
        var componentCourt = ComponentCourt.builder()
                .id(1L)
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        var componentCourtDto = ComponentCourtDto.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(componentCourt));
        Mockito.when(repository.save(Mockito.any(ComponentCourt.class)))
                .thenReturn(componentCourt);
        assertThat(service.update(componentCourtDto, 1L).getId())
                .isEqualTo(componentCourt.getId());
    }

    @Test
    public void whenGetByActivityId_thenReturnComponentCourtDtoList() {
        var componentCourt = ComponentCourt.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(repository.findByActivityId(Mockito.anyLong()))
                .thenReturn(List.of(componentCourt));
        assertThat(service.getByActivityId(1L))
                .isNotEmpty();
    }

    @Test
    public void whenGetByActivityIds_thenReturnComponentCourtDtoList() {
        var componentCourt = ComponentCourt.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(repository.findByActivityIdIn(Mockito.anyList()))
                .thenReturn(List.of(componentCourt));
        assertThat(service.getByActivityIds(List.of(1L)))
                .isNotEmpty();
    }

    @Test
    public void givenId_whenDelete_thenDoesNotThrowAnyException() {
        var componentCourt = ComponentCourt.builder()
                .courtId(1L)
                .activityId(1L)
                .decisionNotificationDate(LocalDate.now())
                .docketNo("123")
                .filingDate(LocalDate.now())
                .decisionNumber("123")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(componentCourt));
        Mockito.doNothing().when(repository).delete(Mockito.any(ComponentCourt.class));
        assertThatCode(() -> service.delete(1L, 1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void testGetByDocketNoWhenDocketNoExistsThenReturnComponentCourt() {
        var docketNo = "12345";

        Mockito.when(repository.findFirstByDocketNo(docketNo)).thenReturn(componentCourt);

        ComponentCourt result = service.getByDocketNo(docketNo);

        Mockito.verify(repository).findFirstByDocketNo(docketNo);
        assertThat(docketNo).isEqualTo(result.getDocketNo());
    }

}
