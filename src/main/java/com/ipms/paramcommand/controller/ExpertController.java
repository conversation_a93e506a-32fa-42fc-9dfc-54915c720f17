package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.*;
import com.ipms.paramcommand.service.ExpertService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("expert")
@Tag(name = "expert", description = "This endpoints contains expert APIs")
public class ExpertController {
    private final ExpertService service;

    @PostMapping
    public BaseResponse<ExpertDto> save(@Valid @RequestBody ExpertDto dto) {
        return BaseResponse.<ExpertDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<ExpertDto> getById(@PathVariable Long id) {
        return BaseResponse.<ExpertDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<ExpertPageDto> getAll(ExpertFilterRequest filterRequest, @PathVariable int page, @PathVariable int size) {
        return BaseResponse.<ExpertPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAll(filterRequest, page, size))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<ExpertDto>> getByIds(@PathVariable List<Long> ids) {
        return BaseResponse.<List<ExpertDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.findByIdIn(ids))
                .build();
    }
}
