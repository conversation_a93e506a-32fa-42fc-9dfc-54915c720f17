package com.ipms.activity.mapper;

import com.ipms.activity.dto.CustomsRecordalDto;
import com.ipms.activity.model.CustomsRecordal;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface CustomsRecordalMapper {
    CustomsRecordal toCustomsRecordal(CustomsRecordalDto customsRecordalDto);

    CustomsRecordalDto toCustomsRecordalDto(CustomsRecordal customsRecordal);

    void toCustomsRecordalFromDto(CustomsRecordalDto customsRecordalDto, @MappingTarget CustomsRecordal customsRecordal);
}
