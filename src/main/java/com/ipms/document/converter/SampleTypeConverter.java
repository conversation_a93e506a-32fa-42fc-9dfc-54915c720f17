package com.ipms.document.converter;

import com.ipms.document.enums.SampleType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class SampleTypeConverter implements AttributeConverter<SampleType, String> {

    @Override
    public String convertToDatabaseColumn(SampleType sampleType) {
        return sampleType == null ? null : sampleType.getValue();
    }

    @Override
    public SampleType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : SampleType.getEnum(s);
    }
}
