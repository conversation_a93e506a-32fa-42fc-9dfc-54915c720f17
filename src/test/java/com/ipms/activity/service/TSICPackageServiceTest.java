package com.ipms.activity.service;

import com.ipms.activity.dto.TSICPackageDto;
import com.ipms.activity.dto.TSICPackageUpdatedEvent;
import com.ipms.activity.exception.TSICPackageCannotUpdateException;
import com.ipms.activity.mapper.TSICPackageMapper;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.TSICPackage;
import com.ipms.activity.repository.TSICPackageRepository;
import com.ipms.activity.service.impl.TSICPackageServiceImpl;
import com.ipms.activity.validator.TSICPackageValidator;
import com.ipms.config.kafka.service.ProducerService;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mapstruct.factory.Mappers;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class TSICPackageServiceTest {

    @MockBean
    private TSICPackageService service;

    @Mock
    private ActivityService activityService;

    @Mock
    private TSICPackageRepository repository;

    @Mock
    private TSICPackageValidator validator;

    @Mock
    private ProducerService producerService;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @Mock
    private TSICOvertimeCalculationService tsicOvertimeCalculationService;
    
    private final TSICPackageMapper mapper = Mappers.getMapper(TSICPackageMapper.class);
    
    @Before
    public void setUp() {
        service = new TSICPackageServiceImpl(repository, mapper, activityService, validator, producerService,
                eventPublisher, tsicOvertimeCalculationService);
    }

    @Test
    public void givenActivityId_whenGetAllByActivityId_thenReturnTSICPackageList() {
        Long activityId = 1L;
        when(repository.findAllByActivityId(activityId))
                .thenReturn(List.of(mock(TSICPackage.class), mock(TSICPackage.class)));

        var tsicPackageDtos = service.getAllByActivityId(activityId);

        verify(repository).findAllByActivityId(activityId);
        Assertions.assertThat(tsicPackageDtos).hasSize(2);
    }

    @Test
    public void givenActivityIdAndTSICPackageDto_whenSave_lastEndDateNull_thenReturnsTSICPackageDto() {
        var tsicPackageDto = TSICPackageDto.builder()
                .startDate(LocalDate.of(2024, 1, 10))
                .includedCharge(BigDecimal.TEN)
                .build();
        var tsicPackage1 = TSICPackage.builder()
                .id(1L)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 1, 2))
                .includedCharge(BigDecimal.ONE)
                .build();
        var tsicPackage2 = TSICPackage.builder()
                .id(2L)
                .startDate(LocalDate.of(2024, 1, 5))
                .endDate(null)
                .includedCharge(BigDecimal.TEN)
                .build();
        var activity = Activity.builder()
                .id(1L)
                .tsicPackages(List.of(tsicPackage1, tsicPackage2))
                .build();

        when(repository.save(any(TSICPackage.class))).thenReturn(createTsicPackage());
        when(activityService.getActivityById(anyLong())).thenReturn(activity);

        var saved = service.save(1L, tsicPackageDto);

        verify(repository, times(2)).save(any(TSICPackage.class));
        verify(eventPublisher).publishEvent(any(TSICPackageUpdatedEvent.class));
        assertThat(saved).isEqualTo(createTSICPackageDto());
        assertThat(tsicPackage2.getEndDate()).isEqualTo(tsicPackageDto.getStartDate().minusDays(1));
    }

    @Test
    public void givenActivityIdAndTSICPackageDto_whenSave_thenReturnsTSICPackageDto() {
        var tsicPackageDto = TSICPackageDto.builder()
                .startDate(LocalDate.of(2024, 1, 10))
                .includedCharge(BigDecimal.TEN)
                .build();
        var tsicPackage1 = TSICPackage.builder()
                .id(1L)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 1, 2))
                .includedCharge(BigDecimal.ONE)
                .build();
        var tsicPackage2 = TSICPackage.builder()
                .id(2L)
                .startDate(LocalDate.of(2024, 1, 5))
                .endDate(LocalDate.of(2024, 1, 6))
                .includedCharge(BigDecimal.TEN)
                .build();
        var activity = Activity.builder()
                .tsicPackages(List.of(tsicPackage1, tsicPackage2))
                .build();

        when(repository.save(any(TSICPackage.class))).thenReturn(createTsicPackage());
        when(activityService.getActivityById(anyLong())).thenReturn(activity);

        var saved = service.save(1L, tsicPackageDto);

        verify(repository).save(any(TSICPackage.class));
        verify(eventPublisher).publishEvent(any(TSICPackageUpdatedEvent.class));
        assertThat(saved).isEqualTo(createTSICPackageDto());
        assertThat(tsicPackage2.getEndDate()).isEqualTo(LocalDate.of(2024, 1, 6));
    }

    @Test
    public void givenTSICPackageDto_whenUpdate_thenReturnsTSICPackageDto() {
        Long id = 2L;
        var tsicPackage = createTsicPackage();
        var tsicPackageDto = TSICPackageDto.builder()
                .startDate(LocalDate.of(2024, 1, 10))
                .includedCharge(BigDecimal.TEN)
                .build();
        var tsicPackage1 = TSICPackage.builder()
                .id(1L)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 1, 2))
                .includedCharge(BigDecimal.ONE)
                .build();
        var tsicPackage2 = TSICPackage.builder()
                .id(2L)
                .startDate(LocalDate.of(2024, 1, 5))
                .endDate(LocalDate.of(2024, 1, 6))
                .includedCharge(BigDecimal.TEN)
                .build();
        var activity = Activity.builder()
                .tsicPackages(List.of(tsicPackage1, tsicPackage2))
                .build();
        tsicPackage2.setActivity(activity);

        when(repository.save(any(TSICPackage.class))).thenReturn(tsicPackage);
        when(repository.findById(id)).thenReturn(Optional.of(tsicPackage2));

        var saved = service.update(id, tsicPackageDto);

        verify(repository).save(any(TSICPackage.class));
        verify(eventPublisher).publishEvent(any(TSICPackageUpdatedEvent.class));
        verify(validator).validateUpdate(any(), any(), any(), any());
        assertThat(saved).isEqualTo(createTSICPackageDto());
    }

    @Test
    public void givenTSICPackageDto_whenUpdate_thenThrowTSICPackageCannotUpdateException() {
        Long id = 1L;
        var tsicPackage = createTsicPackage();
        var tsicPackageDto = TSICPackageDto.builder()
                .id(1L)
                .startDate(LocalDate.of(2024, 1, 10))
                .includedCharge(BigDecimal.TEN)
                .build();
        var tsicPackage1 = TSICPackage.builder()
                .id(1L)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 1, 2))
                .includedCharge(BigDecimal.ONE)
                .build();
        var tsicPackage2 = TSICPackage.builder()
                .id(2L)
                .startDate(LocalDate.of(2024, 1, 5))
                .endDate(LocalDate.of(2024, 1, 6))
                .includedCharge(BigDecimal.TEN)
                .build();
        var activity = Activity.builder()
                .tsicPackages(List.of(tsicPackage1, tsicPackage2))
                .build();
        tsicPackage2.setActivity(activity);

        when(repository.save(any(TSICPackage.class))).thenReturn(tsicPackage);
        when(repository.findById(id)).thenReturn(Optional.of(tsicPackage2));

        assertThatExceptionOfType(TSICPackageCannotUpdateException.class)
            .isThrownBy(() -> service.update(id, tsicPackageDto));
    }

    @Test
    public void givenId_whenDelete_thenDoesNotThrowAnyException() {
        Long id = 2L;
        var tsicPackage1 = TSICPackage.builder()
                .id(1L)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 1, 2))
                .includedCharge(BigDecimal.ONE)
                .build();
        var tsicPackage2 = TSICPackage.builder()
                .id(2L)
                .startDate(LocalDate.of(2024, 1, 5))
                .endDate(LocalDate.of(2024, 1, 6))
                .includedCharge(BigDecimal.TEN)
                .build();
        var activity = Activity.builder()
                .tsicPackages(List.of(tsicPackage1, tsicPackage2))
                .build();
        tsicPackage1.setActivity(activity);
        tsicPackage2.setActivity(activity);
        when(repository.findById(id)).thenReturn(Optional.of(tsicPackage2));

        ArgumentCaptor<TSICPackage> tsicPackageCaptor = ArgumentCaptor.forClass(TSICPackage.class);
        service.delete(id, 0L);

        verify(repository).findById(id);
        verify(repository).save(tsicPackageCaptor.capture());
        var savedTsicPackage = tsicPackageCaptor.getValue();
        assertThat(savedTsicPackage.isDeleted()).isTrue();
    }

    @Test
    public void givenId_whenDelete_thenThrowTSICPackageCannotUpdateException() {
        Long id = 1L;
        var tsicPackage1 = TSICPackage.builder()
                .id(1L)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 1, 2))
                .includedCharge(BigDecimal.ONE)
                .build();
        var tsicPackage2 = TSICPackage.builder()
                .id(2L)
                .startDate(LocalDate.of(2024, 1, 5))
                .endDate(LocalDate.of(2024, 1, 6))
                .includedCharge(BigDecimal.TEN)
                .build();
        var activity = Activity.builder()
                .tsicPackages(List.of(tsicPackage1, tsicPackage2))
                .build();
        tsicPackage1.setActivity(activity);
        tsicPackage2.setActivity(activity);
        when(repository.findById(id)).thenReturn(Optional.of(tsicPackage2));

        assertThatExceptionOfType(TSICPackageCannotUpdateException.class)
            .isThrownBy(() -> service.delete(id, 0L));

    }

    private TSICPackageDto createTsicPackageDtoWithoutId() {
        return TSICPackageDto.builder()
                .startDate(LocalDate.now())
                .endDate(LocalDate.now())
                .includedCharge(BigDecimal.TEN)
                .build();
    }

    private TSICPackageDto createTSICPackageDto() {
        var dto = createTsicPackageDtoWithoutId();
        dto.setId(1L);
        dto.setVersion(0L);
        return dto;
    }

    private TSICPackage createTsicPackage() {
        return TSICPackage.builder()
                .id(1L)
                .version(0L)
                .startDate(LocalDate.now())
                .endDate(LocalDate.now())
                .includedCharge(BigDecimal.TEN)
                .build();
    }

    @Test
    public void givenActivityId_whenGetActivePackageByActivityId_thenReturnActiveTSICPackageDto() {
        Long activityId = 1L;
        var tsicPackages = List.of(
                TSICPackage.builder()
                        .id(1L)
                        .startDate(LocalDate.of(2024, 1, 1))
                        .endDate(LocalDate.of(2024, 1, 2))
                        .includedCharge(BigDecimal.ONE)
                        .build(),
                TSICPackage.builder()
                        .id(2L)
                        .startDate(LocalDate.of(2024, 1, 5))
                        .endDate(null) // Active package
                        .includedCharge(BigDecimal.TEN)
                        .build()
        );
        when(repository.findAllByActivityId(activityId)).thenReturn(tsicPackages);

        var activePackage = service.getActivePackageByActivityId(activityId);

        assertThat(activePackage).isNotNull();
        assertThat(activePackage.getId()).isEqualTo(2L);
    }

    @Test
    public void givenActivityId_whenGetActivePackageByActivityId_thenReturnNullIfNoActivePackage() {
        Long activityId = 1L;
        var tsicPackages = List.of(
                TSICPackage.builder()
                        .id(1L)
                        .startDate(LocalDate.of(2024, 1, 1))
                        .endDate(LocalDate.of(2024, 1, 2))
                        .includedCharge(BigDecimal.ONE)
                        .build(),
                TSICPackage.builder()
                        .id(2L)
                        .startDate(LocalDate.of(2024, 1, 5))
                        .endDate(LocalDate.of(2024, 1, 6)) // No active package
                        .includedCharge(BigDecimal.TEN)
                        .build()
        );
        when(repository.findAllByActivityId(activityId)).thenReturn(tsicPackages);

        var activePackage = service.getActivePackageByActivityId(activityId);

        assertThat(activePackage).isNull();
    }
}
