package com.ipms.activity.controller;

import com.ipms.activity.dto.AgencyDto;
import com.ipms.activity.service.AgencyService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("agency")
@Tag(name = "agency", description = "This endpoint contains agency APIs ")
public class AgencyController {

    private final AgencyService service;

    @GetMapping("/search")
    public BaseResponse<List<AgencyDto>> getByName(@RequestParam(name = "name") String name) {
        return BaseResponse.<List<AgencyDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByName(name))
                .build();
    }
}
