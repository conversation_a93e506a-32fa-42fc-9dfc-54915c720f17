package com.ipms.activity.mapper;

import com.ipms.activity.dto.CounterRightOwnerDto;
import com.ipms.activity.model.CounterRightOwner;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", uses = {LawyerMapper.class, AgencyMapper.class}, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CounterRightOwnerMapper {
    CounterRightOwner toCounterRightOwner(CounterRightOwnerDto counterRightOwnerDto);
    CounterRightOwner toCounterRightOwner(CounterRightOwnerDto counterRightOwnerDto, @MappingTarget CounterRightOwner counterRightOwner);
    CounterRightOwnerDto toCounterRightOwnerDto(CounterRightOwner counterRightOwner);
}
