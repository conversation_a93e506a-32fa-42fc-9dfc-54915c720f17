package com.ipms.activity.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum PaymentStatus {
    PAID("01"),
    PARTIALLY_PAID("02"),
    PAYMENT_IS_WAITING("03");

    private final String value;

    PaymentStatus(String value) {
        this.value = value;
    }

    public static PaymentStatus getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
