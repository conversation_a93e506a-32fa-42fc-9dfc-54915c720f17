package com.ipms.activity.mapper;


import com.ipms.activity.dto.CriminalActionDto;
import com.ipms.activity.model.CriminalAction;
import org.mapstruct.*;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
public interface CriminalActionMapper {
    CriminalAction toCriminalAction(CriminalActionDto criminalActionDto);
    void toCriminalActionFromDto(CriminalActionDto criminalActionDto, @MappingTarget CriminalAction criminalAction);
    CriminalActionDto toCriminalActionDto(CriminalAction criminalAction);
}
