eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:test-ipms-billing}.test-ipms-billing
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

spring:
  datasource:
    url: *********************************************************************************
    username: ipms_test_user
    password: he9JNxwRb3GxZrAb
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-default/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-default
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn

azure:
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=ipmstestsa;AccountKey=****************************************************************************************;
    container-name: ipmstestcontainer
    sas-host-url: https://ipmstestsa.blob.core.windows.net/ipmstestcontainer/

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  exchange-rate-topic: exchangeRateTopic
  billing-order-integration-saved-topic: billingOrderIntegrationSavedTopic
  billing-order-integration-success-topic: billingOrderIntegrationSuccessTopic
  billing-order-integration-error-topic: billingOrderIntegrationErrorTopic
  billing-order-integration-update-topic: billingOrderIntegrationUpdateTopic
  invoice-fetched-topic: invoiceFetchedTopic
  billing-order-cancelled-topic: billingOrderCancelledTopic
  jde-price-topic: jdePriceTopic

jde:
  url: https://ow.deris.com.tr:444/api
  price-url: https://ow.deris.com.tr/api
  main-company: "00004"
  main-companies: >
    00001,
    00002,
    00004

integration:
  minute: 30