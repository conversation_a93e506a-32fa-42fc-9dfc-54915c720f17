package com.ipms.paramcommand.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.paramcommand.model.Customs;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class CustomsSpecification extends AbstractSpecification<Customs> {
    private final List<String> cities;
    private final String name;

    @Override
    public Predicate toPredicate(Root<Customs> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Optional.ofNullable(cities)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("city").in(value))));
        Optional.ofNullable(name)
                .ifPresent(value -> predicate.getExpressions().add(
                        cb.or(cb.like(root.get("name"), "%" + value + "%"),
                                cb.or(cb.like(cb.upper(root.get("name")), "%"+value.toUpperCase()+"%")))
                        )
                );
        return predicate;
    }

}

