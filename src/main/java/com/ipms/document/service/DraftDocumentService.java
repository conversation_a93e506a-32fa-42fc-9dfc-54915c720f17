package com.ipms.document.service;

import com.ipms.document.dto.DraftDocumentDto;
import com.ipms.document.dto.DraftDocumentFilterRequest;
import com.ipms.document.dto.DraftDocumentPageDto;
import com.ipms.document.dto.MailEvent;
import com.ipms.document.model.DraftDocument;

public interface DraftDocumentService {
    DraftDocumentPageDto getAll(DraftDocumentFilterRequest request, int page, int size);
    DraftDocument processMailEvent(MailEvent mailEvent);
    DraftDocumentDto assignToParalegal(Long id, DraftDocumentDto dto);
    DraftDocumentDto linkToDocument(Long id, DraftDocumentDto dto);
    void delete(Long id, Long version);
}
