package com.ipms.activity.controller;

import com.ipms.activity.dto.CustomsTrainingDateDto;
import com.ipms.activity.dto.CustomsTrainingDateFilterRequest;
import com.ipms.activity.dto.CustomsTrainingDatePageDto;
import com.ipms.activity.service.CustomsTrainingDateService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("customs-training-date")
public class CustomsTrainingDateController {

    private final CustomsTrainingDateService service;

    @PostMapping
    public BaseResponse<CustomsTrainingDateDto> save(@RequestBody @Valid CustomsTrainingDateDto dto) {
        return BaseResponse.<CustomsTrainingDateDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<CustomsTrainingDateDto> update(@RequestBody @Valid CustomsTrainingDateDto dto, @PathVariable Long id) {
        return BaseResponse.<CustomsTrainingDateDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.update(dto, id))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<CustomsTrainingDateDto> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<CustomsTrainingDateDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<CustomsTrainingDateDto> getCustomsTrainingDateById(@PathVariable Long id) {
        return BaseResponse.<CustomsTrainingDateDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

    @GetMapping("/{activityId}/{page}/{size}")
    public BaseResponse<CustomsTrainingDatePageDto> getAllByActivity(CustomsTrainingDateFilterRequest filterRequest,
                                                                     @PathVariable int page,
                                                                     @PathVariable int size) {
        return BaseResponse.<CustomsTrainingDatePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAll(filterRequest, page, size))
                .build();
    }

    @GetMapping("/customs/{customsId}")
    public BaseResponse<List<Long>> getByCustoms(@PathVariable Long customsId) {
        return BaseResponse.<List<Long>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByCustoms(customsId).stream().map(activityId -> activityId.id()).toList())
                .build();
    }
}
