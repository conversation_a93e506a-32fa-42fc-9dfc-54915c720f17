package com.ipms.user.controller;

import com.ipms.config.security.utils.SecurityUtils;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.user.dto.auth.AccessTokenRequest;
import com.ipms.user.dto.auth.AuthDto;
import com.ipms.user.dto.auth.LogoutRequest;
import com.ipms.user.dto.auth.RefreshTokenRequest;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;
import com.ipms.user.exception.InvalidAccessTokenException;
import com.ipms.user.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RequestMapping("/auth")
@RestController
@Tag(name = "authentication", description = "This endpoints contain authentication APIs access token and refresh token.")
public class AuthController {

    private final AuthService authService;

    private static final String ACCESS_TOKEN_NOT_FOUND = "Access token is not found.";

    @PostMapping("/access-token/{clientId}")
    @Operation(
            summary = "Get the tokens with username and password for login or access the secured APIs.",
            parameters = {
                    @Parameter(
                            name = "clientId",
                            description = "Keycloak client id for client.",
                            in = ParameterIn.PATH,
                            required = true
                    )
            })
    public BaseResponse<AuthDto> getAccessToken(@PathVariable("clientId") String clientId,
                                                @Valid @RequestBody AccessTokenRequest accessTokenDto) {
        var accessToken = authService.getAccessToken(accessTokenDto.getUsername(), accessTokenDto.getPassword(), clientId, accessTokenDto.getOtp());
        return BaseResponse.<AuthDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(accessToken)
                .build();
    }


    @PostMapping("/refresh-token/{clientId}")
    @Operation(
            summary = "Get the new tokens with valid refresh token.",
            parameters = {
                    @Parameter(
                            name = "clientId",
                            description = "Keycloak client id for client.",
                            in = ParameterIn.PATH,
                            required = true
                    )
            })
    public BaseResponse<AuthDto> getRefreshToken(@PathVariable("clientId") String clientId,
                                                 @Valid @RequestBody RefreshTokenRequest refreshTokenDto) {

        var refreshToken = authService.getRefreshToken(refreshTokenDto.getRefreshToken(), clientId);

        return BaseResponse.<AuthDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(refreshToken)
                .build();
    }

    @PostMapping("/logout/{clientId}")
    @Operation(
            summary = "Log outs authenticated user.",
            parameters = {
                    @Parameter(
                            name = "clientId",
                            description = "Keycloak client id for client.",
                            in = ParameterIn.PATH,
                            required = true
                    )
            })
    public BaseResponse<Void> logout(@PathVariable("clientId") String clientId,
                                     @Valid @RequestBody LogoutRequest logoutRequest) {

        var accessToken = SecurityUtils.getCurrentAccessToken()
                .orElseThrow(() -> new InvalidAccessTokenException(ACCESS_TOKEN_NOT_FOUND));

        authService.logout(accessToken, logoutRequest.getRefreshToken(), clientId);

        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }


    @PostMapping("/user-info/{clientId}")
    @Operation(
            summary = "Get the tokens with username and password for login or access the secured APIs.",
            parameters = {
                    @Parameter(
                            name = "clientId",
                            description = "Keycloak client id for client.",
                            in = ParameterIn.PATH,
                            required = true
                    )
            })
    public BaseResponse<UserInfoDto> getUserInfo(@PathVariable("clientId") String clientId) {
        return BaseResponse.<UserInfoDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(authService.getUserInfo(clientId))
                .build();
    }

    @GetMapping("/role-list")
    public BaseResponse<List<RoleDto>> getRoleList() {
        var accessToken = SecurityUtils.getCurrentAccessToken()
                .orElseThrow(() -> new InvalidAccessTokenException(ACCESS_TOKEN_NOT_FOUND));

        return BaseResponse.<List<RoleDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(authService.getRoleList(accessToken))
                .build();
    }

    @GetMapping("/user-list/roles/{roles}")
    public BaseResponse<List<UserInfoDto>> getUserListByRole(@PathVariable("roles") List<String> roles) {
        var accessToken = SecurityUtils.getCurrentAccessToken()
                .orElseThrow(() -> new InvalidAccessTokenException(ACCESS_TOKEN_NOT_FOUND));

        return BaseResponse.<List<UserInfoDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(authService.getUserListByRole(accessToken, roles))
                .build();
    }

    @GetMapping("/user-list")
    public BaseResponse<List<UserInfoDto>> getUserListWithRoles() {
        var accessToken = SecurityUtils.getCurrentAccessToken()
                .orElseThrow(() -> new InvalidAccessTokenException(ACCESS_TOKEN_NOT_FOUND));
        return BaseResponse.<List<UserInfoDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(authService.getUserListWithRole(accessToken))
                .build();
    }

    @GetMapping("/user-by-email/{email}")
    public BaseResponse<UserInfoDto> getUserRoleByEmail(@PathVariable String email) {
        var accessToken = SecurityUtils.getCurrentAccessToken()
                .orElseThrow(() -> new InvalidAccessTokenException(ACCESS_TOKEN_NOT_FOUND));
        return BaseResponse.<UserInfoDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(authService.getByEmail(email, accessToken))
                .build();
    }

    @GetMapping("/user-by-username/{username}")
    public BaseResponse<UserInfoDto> getUserRoleByUsername(@PathVariable String username) {
        var accessToken = SecurityUtils.getCurrentAccessToken()
                .orElseThrow(() -> new InvalidAccessTokenException(ACCESS_TOKEN_NOT_FOUND));
        return BaseResponse.<UserInfoDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(authService.getByUsername(username, accessToken))
                .build();
    }

    @GetMapping("/expense-codes-by-username/{username}")
    public BaseResponse<UserInfoDto> getExpenseCodesByUsername(
            @PathVariable String username,
            @RequestParam List<String> expenseCodes) {

        var accessToken = SecurityUtils.getCurrentAccessToken()
                .orElseThrow(() -> new InvalidAccessTokenException(ACCESS_TOKEN_NOT_FOUND));

        return BaseResponse.<UserInfoDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(authService.getExpenseCodesByUsername(username, accessToken, expenseCodes))
                .build();
    }
}
