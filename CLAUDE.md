# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Test Commands

### Maven Build
```bash
# Clean compile
mvn clean compile -DskipTests=true

# Run all tests
mvn clean test -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Run single test class
mvn clean test -Dtest=ActivityServiceTest -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Run specific test method
mvn clean test -Dtest=ActivityServiceTest#testMethodName -Djacoco.skip=true -Dnet.bytebuddy.experimental=true
```

### JaCoCo Coverage
```bash
# Generate coverage report
mvn clean test jacoco:report

# Coverage excludes: model/**, dto/** (configured in pom.xml)
```

### Spring Boot Application
```bash
# Run application locally
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=test

# Application runs on port defined in external config server
```

## Architecture Overview

### Technology Stack
- **Java 17** with Spring Boot 2.7.0
- **Spring Cloud** (Eureka, OpenFeign, Config)
- **PostgreSQL** with Hibernate/JPA and Envers auditing
- **Kafka** for event-driven messaging
- **MapStruct** for object mapping
- **Lombok** for boilerplate reduction

### Core Domain
This is the **ipms-activity** service - the most complex microservice in the IPMS platform handling:
- **Activity Management**: Central business logic for legal activities
- **50+ Controllers**: Specialized endpoints for different activity types
- **Complex Entity Relationships**: OneToOne relationships with 11 specialized activity types
- **Junction Tables**: @ElementCollection patterns for issues, expenses, documents, etc.

### Entity Architecture
```java
// Base inheritance pattern
Activity extends VersionedEntity
  -> DatedEntity 
  -> BaseEntity (id, audit fields)

// Specialized activity types (OneToOne with @NotAudited)
- CourtAction, CriminalAction, CustomsSuspension
- PreliminaryInjunction, NotaryDetermination
- CDLetter, UseInvestigation, etc.

// Junction table patterns
@ElementCollection mappings for:
- activity_issues, activity_expenses, activity_documents
- activity_disbursements, activity_evidences, activity_samples
```

### Key Components

#### Controllers (50+)
- **ActivityController**: Main CRUD and relationship management
- **Specialized Controllers**: CourtAction, CriminalAction, Customs, etc.
- **Attachment Controllers**: Various document attachment types

#### Service Layer
- **ServiceImpl classes**: Business logic implementation
- **Feign Clients**: Inter-service communication (Matter, Billing, Document, etc.)
- **Validators**: Private methods within ServiceImpl classes

#### Data Layer
- **JPA Repositories**: Standard Spring Data JPA
- **Specifications**: Dynamic query building (ActivitySpecification)
- **Converters**: Custom JPA attribute converters for enums

### External Dependencies
- **IPMS Shared Libraries**: Core entity, common utilities, i18n, exception handling
- **IPMS Config Libraries**: Security, Kafka, storage, API documentation
- **Spring Cloud Config**: External configuration management
- **Eureka**: Service discovery and registration

## Configuration Management

### Profile-Based Configuration
- **application.yml**: Base configuration (Spring app name, active profile)
- **application-test.yml**: Test profile with H2 database and disabled services
- **External Config**: Production configs managed by Spring Cloud Config Server

### Spring Cloud Config Integration
```yaml
spring:
  cloud:
    config:
      uri: ${IPMS_CONFIG_URI}
      username: ${IPMS_CONFIG_USERNAME}
      password: ${IPMS_CONFIG_PASSWORD}
```

## Testing Strategy

### Test Structure
- **Unit Tests**: Service layer testing with mocks
- **Integration Tests**: Controller tests with Spring Boot Test
- **Test Database**: H2 in-memory for isolated testing
- **Coverage**: JaCoCo with exclusions for model/dto packages

### Test Configuration
- **H2 Database**: `jdbc:h2:file:~/test`
- **Disabled Services**: Eureka client, Config server for test isolation
- **Mock Authentication**: Keycloak test configuration

### Common Test Patterns
```java
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class ActivityServiceTest {
    
    @MockBean
    private ActivityRepository repository;
    
    @Test
    @DisplayName("Test description")
    public void testMethod() {
        // Test implementation
    }
}
```

## Development Guidelines

### Code Style
- **Modern Java**: Use `stream.toList()`, `List.of()`, `var` declarations
- **Lombok**: Extensive use for boilerplate reduction
- **MapStruct**: Object mapping with annotation processors
- **Structured Logging**: `@Slf4j` with parameterized messages

### Entity Patterns
- **Hibernate Envers**: @Audited with @AuditOverride for inheritance
- **Soft Delete**: @Where(clause = "is_deleted='0'") on entities
- **Junction Tables**: @ElementCollection for many-to-many relationships
- **@NotAudited**: Required on OneToOne relationships to prevent audit errors

### Exception Handling
- **Custom Exceptions**: Activity-specific exceptions in exception/ package
- **Validation**: Private validator methods in ServiceImpl classes
- **Response Codes**: Centralized error codes and i18n messages

## Integration Patterns

### Feign Clients
- **Matter Client**: Core business entity relationships
- **Billing Client**: Financial integration and billing processes
- **Document Client**: Document management and attachments
- **Issue Client**: Task and workflow management

### Kafka Integration
- **Event Publishing**: Activity lifecycle events
- **Event Consumption**: React to external system changes
- **Topics**: Billing, document, issue, and matter integration events

### Common Integration Issues
- **Empty Collections**: Guard against empty lists in Feign path variables
- **Audit Errors**: Use @NotAudited on OneToOne relationships
- **Entity Creation**: Verify success by checking ID changes before/after

## Troubleshooting

### Common Issues
- **Test Failures**: Always use `mvn clean test` instead of `mvn test`
- **Audit Exceptions**: Add @NotAudited to OneToOne relationships
- **MapStruct Compilation**: Ensure annotation processors are configured correctly
- **H2 Database**: Test database file location: `~/test.mv.db`

### Performance Considerations
- **N+1 Queries**: Use @EntityGraph or JOIN FETCH for related entities
- **Pagination**: Implement for large dataset endpoints
- **Caching**: Consider for frequently accessed reference data

This service is central to the IPMS platform's activity management capabilities and requires careful consideration of its complex domain model and extensive integration requirements.