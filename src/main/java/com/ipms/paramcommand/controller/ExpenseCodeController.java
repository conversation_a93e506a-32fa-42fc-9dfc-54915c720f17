package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.*;
import com.ipms.paramcommand.dto.groups.CreateGroup;
import com.ipms.paramcommand.dto.groups.UpdateGroup;
import com.ipms.paramcommand.service.ExpenseCodeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Locale;

@RequiredArgsConstructor
@RestController
@RequestMapping("expense-code")
@Tag(name = "expense-code", description = "This endpoints contains expense-code APIs")
public class ExpenseCodeController {

    private final ExpenseCodeService service;

    @PostMapping
    public BaseResponse<ExpenseCodeDto> save(@Validated(CreateGroup.class) @RequestBody ExpenseCodeDto dto) {
        return BaseResponse.<ExpenseCodeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<ExpenseCodeDto> update(@PathVariable Long id,
                                               @Validated(UpdateGroup.class) @RequestBody ExpenseCodeDto dto) {
        return BaseResponse.<ExpenseCodeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.update(dto, id))
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<ExpenseCodeDto> get(@PathVariable Long id) {
        return BaseResponse.<ExpenseCodeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getById(id))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/unit-price/{code}/{currency}")
    public BaseResponse<BigDecimal> getUnitPriceByCode(@PathVariable String code, @PathVariable String currency) {
        return BaseResponse.<BigDecimal>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getUnitPrice(code, currency))
                .build();
    }

    @GetMapping("/unit-price/is-defined/{code}/{currency}")
    public BaseResponse<Boolean> isUnitPriceDefined(@PathVariable String code, @PathVariable String currency) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.isUnitPricedDefined(code, currency))
                .build();
    }

    @GetMapping("/unit-price/is-defined/{code}")
    public BaseResponse<Boolean> isUnitPriceDefined(@PathVariable String code) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.isUnitPricedDefined(code))
                .build();
    }

    @GetMapping("/validate/{codes}/{currency}")
    public BaseResponse<Boolean> validateExpenseCodes(@PathVariable List<String> codes, @PathVariable String currency) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.validateExpenseCodes(codes, currency))
                .build();
    }

    @GetMapping("/summary/{code}/{currency}")
    public BaseResponse<ExpenseCodeSummaryDto> getExpenseCodeSummaryByCode(@PathVariable String code,
                                                                           @PathVariable String currency) {
        return BaseResponse.<ExpenseCodeSummaryDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getExpenseCodeSummary(code, currency))
                .build();
    }

    @PostMapping("/eligible-for-discount")
    public BaseResponse<List<Long>> getDiscountEligibleExpenseIds(@RequestBody List<ExpenseDto> expenses) {
        return BaseResponse.<List<Long>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getDiscountEligibleExpenseIds(expenses))
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<ExpenseCodePageDto> getAll(ExpenseCodeFilterRequest request,
                                                   @PathVariable int page,
                                                   @PathVariable int size,
                                                   @RequestParam(name = "sortField") String property,
                                                   @RequestParam(name = "sortDirection") String direction) {
        return BaseResponse.<ExpenseCodePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(request, page, size, Sort.by(Sort.Direction.valueOf(direction.toUpperCase(Locale.ROOT)), property)))
                .build();
    }
}
