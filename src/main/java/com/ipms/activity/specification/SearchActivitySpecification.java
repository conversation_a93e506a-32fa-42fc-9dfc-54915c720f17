package com.ipms.activity.specification;

import com.ipms.activity.enums.*;
import com.ipms.activity.model.*;
import com.ipms.core.specification.AbstractSpecification;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.*;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@SuperBuilder
public class SearchActivitySpecification extends AbstractSpecification<SearchActivity> {
    private final List<String> types;
    private final List<String> statuses;
    private List<Long> ids;
    private List<Long> matterIds;
    private List<Long> firmIds;
    private String agentReference;
    private List<LocalDateTime> instructionDates;

    private CourtActionFilter courtActionFilter;
    private CDLetterFilter cdLetterFilter;
    private CriminalActionFilter criminalActionFilter;

    private List<LocalDate> useInvestigationCompletionDate;

    private List<String> customsDecisionNumber;


    @SneakyThrows
    @Override
    public Predicate toPredicate(Root<SearchActivity> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        if (Objects.nonNull(courtActionFilter) && !checkNull(courtActionFilter)) {
            Join<CourtAction, SearchActivity> activityCourtAction = root.join("courtAction", JoinType.INNER);
            Optional.ofNullable(courtActionFilter.getStages())
                    .ifPresent(value -> predicate.getExpressions().add(activityCourtAction.get("stage").in(value.stream().map(CourtActionStage::valueOf).toList())));

            Optional.ofNullable(courtActionFilter.getTypes())
                            .ifPresent(value -> predicate.getExpressions().add(activityCourtAction.join("types").in(value.stream().map(CourtActionType::valueOf).toList())));
        }

        if (Objects.nonNull(cdLetterFilter) && !checkNull(cdLetterFilter)) {
            Join<CDLetter, SearchActivity> activityCdLetter = root.join("cdLetter", JoinType.INNER);
            Optional.ofNullable(cdLetterFilter.getPostalTrackingNumbers())
                    .ifPresent(value -> predicate.getExpressions().add(activityCdLetter.get("postalTrackingNumber").in(value)));
            Optional.ofNullable(cdLetterFilter.getNotificationDates())
                    .ifPresent(value -> {
                        if (value.size() == 2) {
                            predicate.getExpressions().add(cb.between(activityCdLetter.get("notificationDate"), value.get(0), value.get(1)));
                        }
                    });
            Optional.ofNullable(cdLetterFilter.getDates())
                    .ifPresent(value -> {
                        if (value.size() == 2) {
                            predicate.getExpressions().add(cb.between(activityCdLetter.get("date"), value.get(0), value.get(1)));
                        }
                    });
            Optional.ofNullable(cdLetterFilter.getOfficialDocumentNumbers())
                    .ifPresent(value -> predicate.getExpressions().add(activityCdLetter.get("officialDocumentNumber").in(value)));
        }

        if (Objects.nonNull(useInvestigationCompletionDate) && !useInvestigationCompletionDate.isEmpty()) {
            Join<UseInvestigation, SearchActivity> activityUseInvestigation = root.join("useInvestigation", JoinType.INNER);
            Optional.ofNullable(useInvestigationCompletionDate)
                    .ifPresent(value -> {
                        if (value.size() == 2) {
                            predicate.getExpressions().add(cb.between(activityUseInvestigation.get("completionDate"), value.get(0), value.get(1)));
                        }
                    });
        }

        if (Objects.nonNull(criminalActionFilter) && !checkNull(criminalActionFilter)) {
            Join<CriminalAction, SearchActivity> activityCriminalAction = root.join("criminalAction", JoinType.INNER);
            Optional.ofNullable(criminalActionFilter.getComplaintDate())
                    .ifPresent(value -> {
                        if (value.size() == 2) {
                            predicate.getExpressions().add(cb.between(activityCriminalAction.get("complaintDate"), value.get(0), value.get(1)));
                        }
                    });
            
            Optional.ofNullable(criminalActionFilter.getProsecutionNumber())
                    .ifPresent(value -> predicate.getExpressions().add(
                            cb.trim(activityCriminalAction.get("prosecutionNumber")).in(value)));
        }

        if (customsDecisionNumber != null) {
            Join<CustomsSuspension, SearchActivity> activityCustomSuspension = root.join("customsSuspension", JoinType.INNER);
            Optional.ofNullable(customsDecisionNumber)
                    .ifPresent(value -> predicate.getExpressions().add(activityCustomSuspension.get("decisionNumber").in(value)));
        }

        Optional.ofNullable(types)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("type").in(types.stream().map(ActivityType::valueOf).toList()))));
        Optional.ofNullable(statuses)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("status").in(statuses.stream().map(ActivityStatus::valueOf).toList()))));
        Optional.ofNullable(ids)
                .ifPresent(value -> predicate.getExpressions().add(root.get("id").in(value)));
        Optional.ofNullable(matterIds)
                .ifPresent(value -> predicate.getExpressions().add(root.get("matterId").in(value)));
        Optional.ofNullable(firmIds)
                .ifPresent(value -> predicate.getExpressions().add(root.get("firmId").in(value)));
        Optional.ofNullable(agentReference)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(cb.upper(root.get("agentReference")), value.toUpperCase())));
        Optional.ofNullable(instructionDates)
                .ifPresent(value -> {
                    if (value.size() == 2) {
                        predicate.getExpressions().add(cb.between(root.get("instructionDate"), value.get(0), value.get(1)));
                    }
                });

        return predicate;
    }

    public boolean checkNull(Object o) throws IllegalAccessException {
        for (Field f : o.getClass().getDeclaredFields()) {
            f.setAccessible(true);
            if (f.get(o) != null)
                return false;
        }
        return true;
    }
}
