package com.ipms.activity.converter;

import com.ipms.activity.enums.PaymentStatus;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Objects;

@Converter(autoApply = true)
public class PaymentStatusConverter implements AttributeConverter<PaymentStatus, String> {
    @Override
    public String convertToDatabaseColumn(PaymentStatus paymentStatus) {
        return Objects.isNull(paymentStatus) ? null : paymentStatus.getValue();
    }

    @Override
    public PaymentStatus convertToEntityAttribute(String s) {
        return Objects.isNull(s) || s.isEmpty() ? null : PaymentStatus.getEnum(s);
    }
}
