package com.ipms.activity.service;

import com.ipms.activity.dto.GuaranteePaymentDto;
import com.ipms.activity.mapper.GuaranteePaymentMapper;
import com.ipms.activity.model.Guarantee;
import com.ipms.activity.model.GuaranteePayment;
import com.ipms.activity.repository.GuaranteePaymentRepository;
import com.ipms.activity.service.impl.GuaranteePaymentServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
class GuaranteePaymentServiceTest {
    @InjectMocks
    private GuaranteePaymentServiceImpl guaranteePaymentService;

    @Mock
    private GuaranteePaymentRepository repository;

    @Mock
    private GuaranteePaymentMapper mapper;

    @Mock
    private GuaranteeService guaranteeService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void save() {
        GuaranteePaymentDto inputDto = new GuaranteePaymentDto();
        inputDto.setGuaranteeId(1L);

        GuaranteePayment entityToSave = new GuaranteePayment();
        entityToSave.setGuarantee(new Guarantee());

        Guarantee savedGuarantee = new Guarantee();
        savedGuarantee.setId(1L);

        GuaranteePayment savedEntity = new GuaranteePayment();
        savedEntity.setId(1L);
        savedEntity.setGuarantee(savedGuarantee);

        GuaranteePaymentDto expectedDto = new GuaranteePaymentDto();
        expectedDto.setGuaranteeId(savedEntity.getGuarantee().getId());

        when(mapper.toGuaranteePayment(inputDto)).thenReturn(entityToSave);
        when(guaranteeService.getEntityById(1L)).thenReturn(savedGuarantee);
        when(repository.save(entityToSave)).thenReturn(savedEntity);
        when(mapper.toGuaranteePaymentDto(savedEntity)).thenReturn(expectedDto);

        GuaranteePaymentDto result = guaranteePaymentService.save(inputDto);

        assertNotNull(result);
        assertEquals(expectedDto.getGuaranteeId(), result.getGuaranteeId());
    }

    @Test
    void getById() {
        Long paymentId = 1L;
        GuaranteePayment entity = new GuaranteePayment();
        entity.setId(paymentId);

        GuaranteePaymentDto expectedDto = new GuaranteePaymentDto();
        expectedDto.setGuaranteeId(paymentId);

        when(repository.findById(paymentId)).thenReturn(Optional.of(entity));
        when(mapper.toGuaranteePaymentDto(entity)).thenReturn(expectedDto);

        GuaranteePaymentDto result = guaranteePaymentService.getById(paymentId);

        assertNotNull(result);
        assertEquals(expectedDto.getGuaranteeId(), result.getGuaranteeId());
    }

    @Test
    void getAllByGuaranteeId() {
        Long guaranteeId = 1L;
        Guarantee guarantee = new Guarantee();
        guarantee.setId(1L);
        GuaranteePayment entity = new GuaranteePayment();
        entity.setGuarantee(guarantee);

        List<GuaranteePayment> entities = new ArrayList<>();
        entities.add(entity);

        GuaranteePaymentDto expectedDto = new GuaranteePaymentDto();
        expectedDto.setGuaranteeId(guaranteeId);

        when(repository.findGuaranteePaymentByGuarantee_Id(guaranteeId)).thenReturn(entities);
        when(mapper.toGuaranteePaymentDto(entity)).thenReturn(expectedDto);

        List<GuaranteePaymentDto> result = guaranteePaymentService.getAllByGuaranteeId(guaranteeId);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedDto.getGuaranteeId(), result.get(0).getGuaranteeId());
    }

    @Test
    void delete() {
        Long guaranteeId = 1L;
        GuaranteePayment entity = new GuaranteePayment();

        when(repository.findById(guaranteeId)).thenReturn(Optional.of(entity));
        when(repository.save(any())).thenReturn(new GuaranteePayment());

        assertDoesNotThrow(() -> guaranteePaymentService.delete(guaranteeId));
    }
}
