package com.ipms.activity.service.impl;

import com.ipms.activity.dto.InvestigationFirmDto;
import com.ipms.activity.enums.InvestigationFirmResponseCode;
import com.ipms.activity.exception.InvestigationFirmException;
import com.ipms.activity.mapper.InvestigationFirmMapper;
import com.ipms.activity.model.InvestigationFirm;
import com.ipms.activity.repository.InvestigationFirmRepository;
import com.ipms.activity.service.InvestigationFirmService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class InvestigationFirmServiceImpl implements InvestigationFirmService {
    private final InvestigationFirmRepository repository;
    private final InvestigationFirmMapper mapper;

    @Override
    public InvestigationFirmDto update(InvestigationFirmDto investigationFirmDto, Long id) {
        var firm = getInvestigationFirmById(id).toBuilder()
                .build();
        checkByName(investigationFirmDto.getName(), id);
        var updated = mapper.toInvestigationFirmFromDto(investigationFirmDto,firm);
        var saved = repository.save(updated);
        return mapper.toInvestigationFirmDto(saved);
    }

    @Override
    public Set<InvestigationFirm> saveList(List<InvestigationFirmDto> dtoList) {
        return dtoList.stream().map(dto -> {
            if (dto.getId()==null) {
                var firm = mapper.toInvestigationFirm(dto);
                return repository.save(firm);
            } else {
                return getInvestigationFirmById(dto.getId());
            }
        }).collect(Collectors.toSet());
    }

    @Override
    public List<InvestigationFirmDto> getByName(String name) {
        return repository.findTop10ByNameContainsIgnoreCaseOrderByName(name)
                .stream()
                .map(mapper::toInvestigationFirmDto)
                .toList();
    }

    @Override
    public InvestigationFirmDto getById(Long id) {
        var investigationFirm = getInvestigationFirmById(id);
        return mapper.toInvestigationFirmDto(investigationFirm);
    }

    @Override
    public void delete(Long id) {
        var investigationFirm = getInvestigationFirmById(id);
        if (investigationFirm.getUseInvestigations().isEmpty()) {
            investigationFirm.setDeleted(Boolean.TRUE);
            repository.save(investigationFirm);
        }
    }

    private InvestigationFirm getInvestigationFirmById(Long id) {
        return repository.findById(id)
                .orElseThrow(() -> new InvestigationFirmException(InvestigationFirmResponseCode.NOT_FOUND));
    }

    private void checkByName(String name, Long id) {
        repository.findByNameAndIdNot(name, id)
                .ifPresent(investigationFirm -> {throw new InvestigationFirmException(InvestigationFirmResponseCode.ALREADY_EXIST);});
    }
}
