package com.ipms.activity.service;
import com.ipms.activity.client.MatterClient;
import com.ipms.activity.client.ParamCommandClient;
import com.ipms.activity.dto.*;
import com.ipms.activity.dto.trademark.MarkDto;
import com.ipms.activity.dto.trademark.RegistrationDto;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.PeriodicOperationBaseDateType;
import com.ipms.activity.enums.PeriodicOperationPeriodType;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.Registration;
import com.ipms.activity.service.impl.RenewalServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RenewalServiceTest {
    @Mock
    private MatterClient matterClient;

    @Mock
    private ParamCommandClient paramCommandClient;

    private RenewalServiceImpl service;

    @Before
    public void setUp() {
        service = new RenewalServiceImpl(matterClient, paramCommandClient);
    }

    @Test
    public void whenGetRenewalDate_withEmptyPeriodicOperations_thenReturnsNull() {
        var activity = Activity.builder()
                .matterId(100L)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(MarkDto.builder()
                                .countryId(1L)
                                .build())
                        .build())
                .build();

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(Collections.emptyList())
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByCountry(1L)).thenReturn(periodicOperationResponse);

        var result = service.getRenewalDate(activity);

        assertThat(result).isNull();
    }

    @Test
    public void whenGetRenewalDate_withSinglePeriodicOperation_thenCalculatesRenewalDate() {
        var activity = Activity.builder()
                .matterId(100L)
                .build();

        var registrationDto = RegistrationDto.builder()
                .applicationDate(LocalDate.of(2024, 1, 15))
                .build();

        var markDto = MarkDto.builder()
                .countryId(1L)
                .registration(registrationDto)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(markDto)
                        .build())
                .build();

        var periodicOperation = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.YEAR)
                .value(10)
                .build();

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(List.of(periodicOperation))
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByCountry(1L)).thenReturn(periodicOperationResponse);

        var result = service.getRenewalDate(activity);

        assertThat(result).isEqualTo(LocalDate.of(2034, 1, 15));
    }

    @Test
    public void whenGetRenewalDate_withMonthPeriod_thenCalculatesCorrectRenewalDate() {
        var activity = Activity.builder()
                .matterId(100L)
                .build();

        var registrationDto = RegistrationDto.builder()
                .applicationDate(LocalDate.of(2024, 1, 15))
                .build();

        var markDto = MarkDto.builder()
                .countryId(1L)
                .registration(registrationDto)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(markDto)
                        .build())
                .build();

        var periodicOperation = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.MONTH)
                .value(6)
                .build();

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(List.of(periodicOperation))
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByCountry(1L)).thenReturn(periodicOperationResponse);

        var result = service.getRenewalDate(activity);

        assertThat(result).isEqualTo(LocalDate.of(2024, 7, 15));
    }

    @Test
    public void whenGetRenewalDate_withIpOfficeId_thenUsesIpOfficeEndpoint() {
        var activity = Activity.builder()
                .matterId(100L)
                .build();

        var registrationDto = RegistrationDto.builder()
                .applicationDate(LocalDate.of(2024, 1, 15))
                .build();

        var markDto = MarkDto.builder()
                .countryId(1L)
                .ipOfficeId(5L)
                .registration(registrationDto)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(markDto)
                        .build())
                .build();

        var periodicOperation = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.YEAR)
                .value(10)
                .build();

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(List.of(periodicOperation))
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByIpOffice(5L)).thenReturn(periodicOperationResponse);

        var result = service.getRenewalDate(activity);

        assertThat(result).isEqualTo(LocalDate.of(2034, 1, 15));
        verify(paramCommandClient).getPeriodicOperationByIpOffice(5L);
        verify(paramCommandClient, never()).getPeriodicOperationByCountry(anyLong());
    }

    @Test
    public void whenGetRenewalDate_withMultipleOperationsAndValidConditionDate_thenSelectsLatestValidOperation() {
        var activity = Activity.builder()
                .matterId(100L)
                .build();

        var registrationDto = RegistrationDto.builder()
                .applicationDate(LocalDate.of(2024, 1, 15))
                .build();

        var markDto = MarkDto.builder()
                .countryId(1L)
                .registration(registrationDto)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(markDto)
                        .build())
                .build();

        var operation1 = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.YEAR)
                .value(5)
                .conditionOfValidityDate(LocalDate.of(2020, 1, 1))
                .build();

        var operation2 = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.YEAR)
                .value(10)
                .conditionOfValidityDate(LocalDate.of(2023, 1, 1))
                .build();

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(List.of(operation1, operation2))
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByCountry(1L)).thenReturn(periodicOperationResponse);

        var result = service.getRenewalDate(activity);

        assertThat(result).isEqualTo(LocalDate.of(2034, 1, 15)); // Uses operation2 (10 years)
    }

    @Test
    public void whenGetRenewalDate_withMultipleOperationsAndInvalidConditionDate_thenSelectsOperationWithoutCondition() {
        var activity = Activity.builder()
                .matterId(100L)
                .build();

        var registrationDto = RegistrationDto.builder()
                .applicationDate(LocalDate.of(2024, 1, 15))
                .build();

        var markDto = MarkDto.builder()
                .countryId(1L)
                .registration(registrationDto)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(markDto)
                        .build())
                .build();

        var operation1 = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.YEAR)
                .value(5)
                .conditionOfValidityDate(LocalDate.of(2025, 1, 1)) // Future date - invalid
                .build();

        var operation2 = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.YEAR)
                .value(10)
                .build(); // No condition date

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(List.of(operation1, operation2))
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByCountry(1L)).thenReturn(periodicOperationResponse);

        var result = service.getRenewalDate(activity);

        assertThat(result).isEqualTo(LocalDate.of(2034, 1, 15)); // Uses operation2 (10 years)
    }

    @Test
    public void whenGetRenewalDate_withNullApplicationDate_thenReturnsNull() {
        var activity = Activity.builder()
                .matterId(100L)
                .build();

        var markDto = MarkDto.builder()
                .countryId(1L)
                .registration(RegistrationDto.builder().build())
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(markDto)
                        .build())
                .build();

        var periodicOperation = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.YEAR)
                .value(10)
                .build();

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(List.of(periodicOperation))
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByCountry(1L)).thenReturn(periodicOperationResponse);

        var result = service.getRenewalDate(activity);

        assertThat(result).isNull();
    }

    @Test
    public void whenCreateRenewalActivityFromRegistrationActivity_thenCreatesCorrectRenewalActivity() {
        var registrationActivity = Activity.builder()
                .matterId(100L)
                .firmId(50L)
                .billingAccountId(25L)
                .agentReference("AG-123")
                .localAgent(123L)
                .registration(Registration.builder()
                        .bulletinNumber("BN-2024-001")
                        .bulletinPublicationDate(LocalDate.of(2024, 1, 15))
                        .decisionDate(LocalDate.of(2024, 1, 10))
                        .notificationDate(LocalDate.of(2024, 1, 20))
                        .build())
                .build();

        var registrationDto = RegistrationDto.builder()
                .applicationDate(LocalDate.of(2024, 1, 15))
                .build();

        var markDto = MarkDto.builder()
                .countryId(1L)
                .registration(registrationDto)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(markDto)
                        .build())
                .build();

        var periodicOperation = PeriodicOperationItemDto.builder()
                .baseDate(PeriodicOperationBaseDateType.APPLICATION_DATE)
                .period(PeriodicOperationPeriodType.YEAR)
                .value(10)
                .build();

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(List.of(periodicOperation))
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByCountry(1L)).thenReturn(periodicOperationResponse);

        var result = service.createRenewalActivityFromRegistrationActivity(registrationActivity);

        assertThat(result.getType()).isEqualTo(ActivityType.RENEWAL);
        assertThat(result.getStatus()).isEqualTo(ActivityStatus.PENDING);
        assertThat(result.getMatterId()).isEqualTo(100L);
        assertThat(result.getFirmId()).isEqualTo(50L);
        assertThat(result.getBillingAccountId()).isEqualTo(25L);
        assertThat(result.getAgentReference()).isEqualTo("AG-123");
        assertThat(result.getLocalAgent()).isEqualTo(123L);

        assertThat(result.getRenewal()).isNotNull();
        assertThat(result.getRenewal().getBulletinNumber()).isEqualTo("BN-2024-001");
        assertThat(result.getRenewal().getBulletinPublicationDate()).isEqualTo(LocalDate.of(2024, 1, 15));
        assertThat(result.getRenewal().getDecisionDate()).isEqualTo(LocalDate.of(2024, 1, 10));
        assertThat(result.getRenewal().getNotificationDate()).isEqualTo(LocalDate.of(2024, 1, 20));
        assertThat(result.getRenewal().getRenewalDate()).isEqualTo(LocalDate.of(2034, 1, 15));
    }

    @Test
    public void whenCreateRenewalActivityFromRegistrationActivity_withNullRenewalDate_thenCreatesActivityWithNullRenewalDate() {
        var registrationActivity = Activity.builder()
                .matterId(100L)
                .firmId(50L)
                .billingAccountId(25L)
                .agentReference("AG-123")
                .localAgent(123L)
                .registration(Registration.builder()
                        .bulletinNumber("BN-2024-001")
                        .bulletinPublicationDate(LocalDate.of(2024, 1, 15))
                        .decisionDate(LocalDate.of(2024, 1, 10))
                        .notificationDate(LocalDate.of(2024, 1, 20))
                        .build())
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .mark(MarkDto.builder()
                                .countryId(1L)
                                .build())
                        .build())
                .build();

        var periodicOperationResponse = PeriodicOperationResponse.builder()
                .payload(Collections.emptyList())
                .build();

        when(matterClient.getById(100L)).thenReturn(matterResponse);
        when(paramCommandClient.getPeriodicOperationByCountry(1L)).thenReturn(periodicOperationResponse);

        var result = service.createRenewalActivityFromRegistrationActivity(registrationActivity);

        assertThat(result.getType()).isEqualTo(ActivityType.RENEWAL);
        assertThat(result.getStatus()).isEqualTo(ActivityStatus.PENDING);
        assertThat(result.getRenewal().getRenewalDate()).isNull();
    }
}
