package com.ipms.activity.service;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.dto.PIAttachmentDto;
import com.ipms.activity.model.PreliminaryInjunction;

import java.util.List;

public interface ReleasedAttachmentService {
    String getSignedUrl(Long id);
    void delete(Long id);
    AttachmentDto save(PIAttachmentDto piAttachmentDto);
    void saveAttachments(List<AttachmentDto> attachmentDtos, PreliminaryInjunction preliminaryInjunction);
    void setFileSignedUrls(List<AttachmentDto> attachmentDtos);
}
