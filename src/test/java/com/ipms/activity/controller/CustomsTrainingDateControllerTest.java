package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.activity.dto.CustomsTrainingDateDto;
import com.ipms.activity.dto.CustomsTrainingDatePageDto;
import com.ipms.activity.service.CustomsTrainingDateService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
class CustomsTrainingDateControllerTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private CustomsTrainingDateService service;

    ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    @Test
    void save_success() throws Exception {
        when(service.save(any(CustomsTrainingDateDto.class)))
                .thenReturn(generateDummyCustomsTrainingDateDto());
        mvc.perform(post("/customs-training-date")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(generateDummyCustomsTrainingDateDto()))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void update_success() throws Exception {
        when(service.update(any(CustomsTrainingDateDto.class), anyLong()))
                .thenReturn(generateDummyCustomsTrainingDateDto());
        mvc.perform(put("/customs-training-date/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(generateDummyCustomsTrainingDateDto()))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void delete_success() throws Exception {
        doNothing().when(service).delete(anyLong());
        mvc.perform(delete("/customs-training-date/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(generateDummyCustomsTrainingDateDto()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void getCustomsTrainingDateById() throws Exception {
        when(service.getById(anyLong()))
                .thenReturn(generateDummyCustomsTrainingDateDto());
        mvc.perform(get("/customs-training-date/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(generateDummyCustomsTrainingDateDto()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void getAllByActivity() throws Exception {
        when(service.getAll(any(), anyInt(), anyInt()))
                .thenReturn(generateDummyCustomsTrainingDatePageDto());

        mvc.perform(get("/customs-training-date/1/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(generateDummyCustomsTrainingDateDto()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    private CustomsTrainingDateDto generateDummyCustomsTrainingDateDto() {
        return CustomsTrainingDateDto.builder()
                .id(1L)
                .trainingInstructionDate(LocalDate.of(2023, 10, 18))
                .regionalCustoms(123L)
                .city("456")
                .onlineTraining(true)
                .trainingDate(LocalDate.of(2023, 11, 1))
                .activityId(789L)
                .build();
    }

    private CustomsTrainingDatePageDto generateDummyCustomsTrainingDatePageDto(){
        return CustomsTrainingDatePageDto.builder()
                .customsTrainingDateDtos(List.of(generateDummyCustomsTrainingDateDto()))
                .totalPages(1)
                .totalElements(1)
                .build();
    }
}