package com.ipms.activity.enums;

import java.util.Arrays;

public enum CriminalActionDeliveryType {
    TRUSTEE("1"),
    JUDICIAL_CUSTODY("2"),
    COUNTER_PARTY("3"),
    NATIONAL_REAL_ESTATE_OFFICE("4"),
    POLICE_GENDARME("5"),
    CUSTOM("6"),
    WAREHOUSE("7");

    private final String value;
    CriminalActionDeliveryType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static CriminalActionDeliveryType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(value));
    }
}
