# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Build & Test
```bash
# Run all tests
mvn clean test -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Run specific test class
mvn clean test -Dtest=DocumentServiceTest -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Build without tests
mvn clean compile -DskipTests=true

# Build with tests (full)
mvn clean package -Djacoco.skip=true -Dnet.bytebuddy.experimental=true
```

### Development
```bash
# Start application locally
mvn spring-boot:run

# Start with specific profile
SPRING_PROFILE=test mvn spring-boot:run
```

## Architecture Overview

**IPMS Document Service** is a Spring Boot microservice that manages legal documents, evidence, and samples with file attachments. Part of the larger IPMS (Intellectual Property Management System) microservices architecture.

### Core Domain Models

- **Document**: Primary entity for legal documents (agreements, reports, certificates, court documents)
  - 95+ document types defined in `DocumentType` enum
  - Supports attachments, firm associations, expert assignments
  - Tracks dates (creation, expiry, notification)
  - Power of Attorney and delegation features

- **Evidence**: Legal evidence entities with attachments
  - Types: audio, video, photo, document, sample, other
  - Associated with activities through Feign client calls

- **Sample**: Physical sample tracking 
  - Collection places, locations, types
  - Sample attachments and documentation

- **DraftDocument**: Temporary document storage before finalization

### Key Architecture Patterns

#### Entity Hierarchy
All entities extend `VersionedEntity` providing:
- Audit trails (createdAt, updatedAt, createdBy, updatedBy)
- Soft deletion with `@Where(clause = "is_deleted='0'")`
- Optimistic locking with `@Version`
- Hibernate Envers audit history with `@Audited`

#### Junction Tables Pattern
Uses `@ElementCollection` for many-to-many relationships:
```java
@ElementCollection
@CollectionTable(name = "document_firms", joinColumns = @JoinColumn(name = "document_id"))
@Column(name = "firm")
private List<Long> firms;
```

#### Microservices Integration
- **ActivityClient**: Links documents/evidence/samples to activities
- **MatterClient**: Retrieves matter information
- **MailClient**: Email notifications
- Cross-service communication via OpenFeign

#### File Storage
- Integrated with `ipms-config-storage` for file management
- Attachment entities store file metadata
- Actual files handled by centralized storage service

### Service Layer Pattern
- Interface + Implementation separation
- `@Transactional` methods for data consistency
- MapStruct for DTO/Entity mapping
- Custom specifications for complex queries

### Technology Stack
- **Java 17** with Spring Boot 2.7.0
- **Spring Cloud** (Eureka, OpenFeign, Config)
- **PostgreSQL** with Hibernate/JPA
- **Kafka** messaging via `ipms-config-kafka`
- **MapStruct** + **Lombok** for code generation
- **Hibernate Envers** for audit trails

### Bill of Materials (BOM) Pattern
The **ipms-bom** module provides centralized dependency management:

#### BOM Import
```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.ipms</groupId>
            <artifactId>ipms-bom</artifactId>
            <version>1.0.0</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

#### Managed Dependencies
- **IPMS Core Libraries**: Entity, Common, i18n, Exception Handling
- **IPMS Config Libraries**: Security, Kafka, Storage, API Documentation  
- **Third-Party Libraries**: MapStruct, Lombok, Commons utilities

#### Benefits
- **Version Consistency**: Single source of truth for all dependency versions
- **Simplified Maintenance**: Update versions in one place
- **Conflict Resolution**: Prevents version conflicts between services
- **Developer Experience**: No need to specify versions for managed dependencies

### Configuration
- **Profile-based**: local, test, release, prod
- **Spring Cloud Config** integration
- **Externalized configuration** via `CONFIG_REPO_PATH`
- **Centralized security** via `ipms-config-security`

### Package Structure
```
com.ipms.document/
├── client/          # Feign clients for inter-service communication
├── controller/      # REST endpoints
├── dto/            # Data transfer objects with validation groups
├── enums/          # Domain enums and response codes
├── exception/      # Domain-specific exceptions
├── mapper/         # MapStruct mappers
├── model/          # JPA entities with audit support
├── repository/     # Spring Data JPA repositories
├── service/impl/   # Service implementations
└── specification/  # JPA Criteria API specifications
```

### Testing Strategy
- **Integration Tests**: Full Spring context with H2 database
- **Unit Tests**: Mockito for service layer testing
- **JaCoCo Coverage**: Excludes model and DTO packages
- **Test Profiles**: Separate configuration for testing

### Key Integration Points
- Documents link to Activities via `ActivityClient.addDocument()`
- Transfer events published to Kafka for external system sync
- File attachments managed through centralized storage service
- Audit trail integration with broader IPMS audit system