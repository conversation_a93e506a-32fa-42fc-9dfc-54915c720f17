package com.ipms.document.service;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.document.client.ActivityClient;
import com.ipms.document.client.MailClient;
import com.ipms.document.client.MatterClient;
import com.ipms.document.dto.*;
import com.ipms.document.mapper.DraftDocumentMapper;
import com.ipms.document.mapper.DraftDocumentMapperImpl;
import com.ipms.document.model.DraftDocument;
import com.ipms.document.repository.DraftDocumentRepository;
import com.ipms.document.service.impl.DraftDocumentServiceImpl;
import com.ipms.document.specification.DraftDocumentSpecification;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class DraftDocumentServiceTest {

    @MockBean
    private DraftDocumentService service;

    @Mock
    private DraftDocumentRepository repository;

    @Mock
    private ActivityClient activityClient;

    @Mock
    private MatterClient matterClient;

    @Mock
    private MailClient mailClient;

    @Mock
    private ProducerService producerService;

    DraftDocumentMapper mapper = new DraftDocumentMapperImpl();


    @Before
    public void setUp() {
        service = new DraftDocumentServiceImpl(repository,matterClient, activityClient, mapper, producerService, mailClient);
    }

    @Test
    public void getAll_withValidRequestAndData_returnsExpectedPageDto() {
        List<DraftDocument> mockDraftDocuments = List.of(new DraftDocument(), new DraftDocument());
        List<DraftDocumentDto> draftDocumentDtos = mockDraftDocuments.stream().map(mapper::toDto).toList();
        Page<DraftDocument> page = new PageImpl<>(mockDraftDocuments);
        DraftDocumentPageDto expectedDto = new DraftDocumentPageDto(draftDocumentDtos,2, 1);

        when(repository.findAll(Mockito.any(DraftDocumentSpecification.class),  Mockito.any(Pageable.class)))
                .thenReturn(page);

        DraftDocumentPageDto actualDto = service.getAll(new DraftDocumentFilterRequest(), 0, 10);

        assertEquals(expectedDto, actualDto);
    }

    @Test
    public void processMailEvent() {
        String docketNo = "1234";
        var mailEvent = mock(MailEvent.class);
        when(mailEvent.getSubject()).thenReturn("This is a sample text #ES-1234 -");
        var activityResponse = ActivityResponse.builder()
                .code(1)
                .payload(ActivityResponse.Payload.builder()
                        .id(1L)
                        .matterId(2L)
                        .build())
                .build();
        when(activityClient.getMatterIdByDocketNo(docketNo)).thenReturn(activityResponse);
        var matterResponse = new MatterResponse(1, "paralegal-1");
        when(matterClient.getParalegalByMatterId(activityResponse.getPayload().getMatterId())).thenReturn(matterResponse);

        ArgumentCaptor<DraftDocument> savedDraftDocument = ArgumentCaptor.forClass(DraftDocument.class);
        Mockito.when(repository.save(savedDraftDocument.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));

        DraftDocument actualDraftDocument = service.processMailEvent(mailEvent);
        assertEquals("paralegal-1", actualDraftDocument.getAssignee());
    }

    @Test
    public void testAssignToParalegal_updatesAssigneeCorrectly() {
        String expectedAssignee = "paralegal-1";

        var existingDocument = DraftDocumentDto.builder()
                .assignee(expectedAssignee)
                .build();
        when(repository.findById(1L)).thenReturn(Optional.of(new DraftDocument()));

        ArgumentCaptor<DraftDocument> draftDocumentArgumentCaptor = ArgumentCaptor.forClass(DraftDocument.class);
        Mockito.when(repository.save(draftDocumentArgumentCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));

        var actualDraftDocumentDto = service.assignToParalegal(1L, existingDocument);

        assertEquals(expectedAssignee, actualDraftDocumentDto.getAssignee());
        verify(repository).findById(1L);
    }

    @Test
    public void whenlinkToDocument_thenDocumentIdNotNull() {
        Long id = 1L;
        Long exceptedDocumentId = 123L;

        DraftDocumentDto updatedDraftDocumentDto = DraftDocumentDto.builder()
                .documentId(exceptedDocumentId)
                .build();

        DraftDocument existingDocument = DraftDocument.builder()
                .id(id)
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(existingDocument));

        ArgumentCaptor<DraftDocument> draftDocumentArgumentCaptor = ArgumentCaptor.forClass(DraftDocument.class);
        Mockito.when(repository.save(draftDocumentArgumentCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));

        var actualDraftDocumentDto = service.linkToDocument(id, updatedDraftDocumentDto);

        assertEquals(exceptedDocumentId, actualDraftDocumentDto.getDocumentId());
        verify(repository, times(1)).findById(id);
        verify(repository, times(1)).save(any());
    }

    @Test
    public void whenDelete_thenDoesNotThrowAnyException() {
        Long id = 1L;
        Long version = 2L;

        DraftDocument draftDocument = new DraftDocument();
        draftDocument.setId(id);

        when(repository.findById(id)).thenReturn(Optional.of(draftDocument));
        when(repository.save(any(DraftDocument.class))).thenReturn(draftDocument);

        ArgumentCaptor<DraftDocument> draftDocumentArgumentCaptor = ArgumentCaptor.forClass(DraftDocument.class);
        Mockito.when(repository.save(draftDocumentArgumentCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));

        // Act
        service.delete(id, version);

        // Assert
        assertTrue(draftDocumentArgumentCaptor.getValue().isDeleted());
        assertEquals(version, draftDocumentArgumentCaptor.getValue().getVersion());
        verify(repository, times(1)).findById(id);
        verify(repository, times(1)).save(draftDocument);
    }
}