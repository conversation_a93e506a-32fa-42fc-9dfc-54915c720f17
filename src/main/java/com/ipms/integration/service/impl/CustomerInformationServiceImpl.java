package com.ipms.integration.service.impl;

import com.ipms.integration.client.JdeClient;
import com.ipms.integration.dto.CustomerInformation;
import com.ipms.integration.dto.CustomerInformationRequestParam;
import com.ipms.integration.service.CustomerInformationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

@RequiredArgsConstructor
@Service
public class CustomerInformationServiceImpl implements CustomerInformationService {
    private final JdeClient jdeClient;
    @Value("${jde.main-company}")
    private String mainCompany;

    @Override
    public CustomerInformation getCustomerInformation(String billingAccountNo) {
        var requestParam = CustomerInformationRequestParam.builder()
                .mainCompany(mainCompany)
                .customerorSupplierNumber(billingAccountNo)
                .build();
        var customerInformationResponse = jdeClient.getCustomerInformation(requestParam);
        var customerInformation = customerInformationResponse.getCustomerInformations().get(0);
        divideDiscountRate(customerInformation);
        return customerInformation;
    }

    private void divideDiscountRate(CustomerInformation customerInformation) {
        var discountRate = customerInformation.getDiscountRate();
        if (discountRate != null) {
            discountRate = discountRate.divide(BigDecimal.valueOf(10_000), RoundingMode.HALF_UP);
        }
        customerInformation.setDiscountRate(discountRate);
    }
}
