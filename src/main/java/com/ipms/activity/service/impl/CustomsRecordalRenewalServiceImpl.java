package com.ipms.activity.service.impl;

import com.ipms.activity.dto.CustomsRecordalRenewalDto;
import com.ipms.activity.dto.CustomsRecordalRenewalPageDto;
import com.ipms.activity.exception.CustomerRecordalNotFoundException;
import com.ipms.activity.mapper.CustomsRecordalRenewalMapper;
import com.ipms.activity.model.CustomsRecordalRenewal;
import com.ipms.activity.repository.CustomsRecordalRenewalRepository;
import com.ipms.activity.service.CustomsRecordalRenewalService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class CustomsRecordalRenewalServiceImpl implements CustomsRecordalRenewalService {
    private final CustomsRecordalRenewalRepository repository;
    private final CustomsRecordalRenewalMapper mapper;

    @Override
    public CustomsRecordalRenewalDto save(CustomsRecordalRenewalDto renewalDto) {
        var renewal = mapper.toCustomsRecordalRenewal(renewalDto);
        return mapper.toCustomsRecordalRenewalDto(repository.save(renewal));
    }

    @Override
    public CustomsRecordalRenewalDto update(CustomsRecordalRenewalDto renewalDto, Long id) {
        var renewal = repository.findById(id).orElseThrow(CustomerRecordalNotFoundException::new);
        var updated = mapper.toCustomsRecordalRenewalFromDto(renewalDto, renewal);
        return mapper.toCustomsRecordalRenewalDto(repository.save(updated));
    }

    @Override
    public void delete(Long id, Long version) {
        CustomsRecordalRenewal customsRecordalRenewal = getRenewalById(id).toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(customsRecordalRenewal);
    }

    public CustomsRecordalRenewalDto getById(Long id) {
        return mapper.toCustomsRecordalRenewalDto(getRenewalById(id));
    }
    private CustomsRecordalRenewal getRenewalById(Long id) {
        return repository.findById(id).orElseThrow(CustomerRecordalNotFoundException::new);
    }

    @Override
    public CustomsRecordalRenewalPageDto getAllByActivityId(Long activityId, int page, int size) {
        var renewalPage = repository.findAllByActivityId(activityId, PageRequest.of(page, size));
        return CustomsRecordalRenewalPageDto.builder()
                .customsRecordalRenewalDtos(renewalPage.getContent().stream()
                        .map(mapper::toCustomsRecordalRenewalDto)
                        .toList())
                .totalElements(renewalPage.getTotalElements())
                .totalPages(renewalPage.getTotalPages())
                .build();
    }
}
