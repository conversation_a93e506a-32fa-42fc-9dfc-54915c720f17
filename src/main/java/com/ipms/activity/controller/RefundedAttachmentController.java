package com.ipms.activity.controller;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.dto.PIAttachmentDto;
import com.ipms.activity.service.RefundedAttachmentService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("attachment/refunded")
@Tag(name = "attachment", description = "This endpoints contain refunded attachment APIs attachment details.")
public class RefundedAttachmentController {
    private final RefundedAttachmentService service;

    @PostMapping
    public BaseResponse<AttachmentDto> save(@RequestBody PIAttachmentDto piAttachmentDto) {
        return BaseResponse.<AttachmentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.save(piAttachmentDto))
                .build();
    }

    @GetMapping("/signed-url/{id}")
    public BaseResponse<String> signedUrl(@PathVariable Long id) {
        return BaseResponse.<String>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getSignedUrl(id))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
