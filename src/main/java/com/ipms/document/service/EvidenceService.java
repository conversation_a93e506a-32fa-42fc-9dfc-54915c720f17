package com.ipms.document.service;

import com.ipms.document.dto.*;
import com.ipms.document.model.Evidence;

import java.util.List;

public interface EvidenceService {
    EvidenceDto save(EvidenceCreateDto evidenceCreateDto);
    EvidenceDto useEvidence(EvidenceCreateDto evidenceCreateDto);
    EvidenceDto save(EvidenceDto evidenceDto);
    EvidenceDto update(EvidenceDto evidenceDto, Long id);
    Evidence getEvidenceById(Long id);
    List<EvidenceDto> getByIdIn(List<Long> ids);
    EvidenceDto getById(Long id);
    void delete(Long id, Long version);
    EvidenceDto getByFileUniqueName(String fileUniqueName);
    List<EvidenceDto> getByFileUniqueName(List<String> fileUniqueNames);
    EvidencePageDto getAll(EvidenceFilterRequest request, int page, int size);
}
