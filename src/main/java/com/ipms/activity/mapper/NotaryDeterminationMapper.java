package com.ipms.activity.mapper;

import com.ipms.activity.dto.NotaryDeterminationDto;
import com.ipms.activity.model.NotaryDetermination;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface NotaryDeterminationMapper {

    NotaryDetermination toNotaryDetermination(NotaryDeterminationDto notaryDeterminationDto);

    NotaryDeterminationDto toNotaryDeterminationDto(NotaryDetermination notaryDetermination);

    void toNotaryDeterminationFromDto(NotaryDeterminationDto dto, @MappingTarget NotaryDetermination notaryDetermination);
}
