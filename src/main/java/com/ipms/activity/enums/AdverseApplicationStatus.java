package com.ipms.activity.enums;

import java.util.Arrays;

public enum AdverseApplicationStatus {
    PUBLICATION("Yayın Kararı"),
    PARTIAL_PUBLICATION("Kısmi Yayın"),
    REJECTION("Red"),
    AWAITING_PUBLICATION("Yayın Bekleniyor"),
    UNKNOWN("Bilinmeyen");

    private final String value;

    AdverseApplicationStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static AdverseApplicationStatus getEnum(String value) {
        if (value == null || value.equals("-")) {
            return null;
        }

        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElse(UNKNOWN);
    }

}
