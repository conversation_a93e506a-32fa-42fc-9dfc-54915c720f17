package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ipms.activity.enums.PIPartyRequestingPIOrder;
import com.ipms.activity.enums.PIType;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "preliminary_injunction")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class PreliminaryInjunction extends VersionedEntity {
    @Column
    private PIPartyRequestingPIOrder partyRequestingPIOrder;

    @Column
    private Boolean guaranteeRefunded;

    @Column
    private Boolean guaranteeReleased;

    @ElementCollection (targetClass = PIType.class)
    @CollectionTable(name = "preliminary_injunction_types",
            joinColumns = @JoinColumn(name = "preliminary_injunction_id", referencedColumnName = "id"))
    @Column(name = "action_type")
    private List<PIType> types;

    @JsonIgnore
    @OneToOne(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinColumn(name = "activity_id", referencedColumnName = "id")
    @NotAudited
    private Activity activity;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "preliminaryInjunction")
    private List<PreliminaryInjunctionDetail> details;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "preliminaryInjunction")

    private List<RefundedAttachment> refundedAttachments;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "preliminaryInjunction")
    @JsonBackReference
    private List<ReleasedAttachment> releasedAttachments;


}
