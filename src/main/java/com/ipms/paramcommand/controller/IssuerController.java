package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.IssuerDto;
import com.ipms.paramcommand.service.IssuerService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("issuer")
@Tag(name = "issuer", description = "This endpoints contains issuer APIs")
public class IssuerController {
    private final IssuerService service;

    @PostMapping
    public BaseResponse<IssuerDto> save(@Valid @RequestBody IssuerDto dto) {
        return BaseResponse.<IssuerDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<IssuerDto> update(@Valid @RequestBody IssuerDto dto, @PathVariable Long id) {
        return BaseResponse.<IssuerDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(id, dto))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping
    public BaseResponse<List<IssuerDto>> getAll() {
        return BaseResponse.<List<IssuerDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAll())
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<IssuerDto> getById(@PathVariable Long id) {
        return BaseResponse.<IssuerDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

}
