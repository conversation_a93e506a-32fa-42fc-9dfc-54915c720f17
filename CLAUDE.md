# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

IPMS User is a Spring Boot microservice that handles user authentication, authorization, and user management within the IPMS (Intellectual Property Management System) platform. It integrates with Keycloak for OAuth2/OpenID Connect authentication, manages user roles and permissions, and provides secure endpoints for user operations.

## Build & Test Commands

```bash
# Build the project
mvn clean compile -DskipTests=true

# Run all tests
mvn clean test -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Run single test class
mvn clean test -Dtest=AuthServiceTest -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Package the application
mvn clean package -DskipTests=true

# Run application locally
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

**Important**: Always use `mvn clean test` instead of `mvn test` when tests fail mysteriously. The `-Djacoco.skip=true` and `-Dnet.bytebuddy.experimental=true` flags are required for proper test execution.

## Architecture & Domain Model

### Core Components

#### Authentication Service (`AuthService`)
- **Primary interface** for user authentication operations
- **OAuth2 token management**: Access token, refresh token, logout
- **User information retrieval**: User profiles, roles, permissions
- **Keycloak integration**: Delegates to `KeycloakAuthService` for external auth operations

#### Keycloak Integration
- **KeycloakAuthService**: Direct interface to Keycloak authentication server
- **Token management**: Username/password authentication, token refresh, logout
- **User management**: User list retrieval, role-based filtering
- **Permission handling**: Role and resource-based access control

#### REST Controller (`AuthController`)
- **8 main endpoints** for authentication and user management
- **OpenAPI documentation**: Swagger annotations for API documentation
- **Security integration**: Uses `SecurityUtils` for current user context
- **Standardized responses**: Uses `BaseResponse` wrapper for consistent API responses

### Key DTOs

#### Authentication DTOs
- **AuthDto**: Token response containing access token, refresh token, and expiration times
- **AccessTokenRequest**: Username/password authentication request with OTP support
- **RefreshTokenRequest**: Token refresh request
- **LogoutRequest**: User logout request

#### User Management DTOs
- **UserInfoDto**: Comprehensive user profile with roles and permissions
- **RoleDto**: Role definition for authorization
- **KeycloakTokenDto**: Internal representation of Keycloak token responses

### Exception Handling

#### Custom Exceptions
- **AuthenticationFailedException**: Login/authentication failures
- **InvalidAccessTokenException**: Invalid or expired access tokens
- **InvalidRefreshTokenException**: Invalid refresh token scenarios
- **UserNotFoundException**: User lookup failures
- **UserAnyRoleException**: Insufficient role permissions

#### Response Codes (`UserResponseCode`)
- **AUTH_FAILED (1000)**: Authentication failed
- **INVALID_CREDENTIALS (1001)**: Invalid username/password
- **INVALID_REFRESH_TOKEN (1002)**: Invalid refresh token
- **INVALID_ACCESS_TOKEN (1003)**: Invalid access token
- **USER_NOT_ACTIVE (1004)**: User account inactive
- **USER_ANY_ROLE (1005)**: User has no roles
- **USER_NOT_FOUND (1006)**: User not found
- **OTP_REQUIRED_ACTION (1007)**: OTP required for authentication

### Bill of Materials (BOM) Pattern

The **ipms-bom** module provides centralized dependency management:

#### BOM Import
```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.ipms</groupId>
            <artifactId>ipms-bom</artifactId>
            <version>1.0.0</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

#### Managed Dependencies
- **IPMS Core Libraries**: Common utilities, i18n, exception handling
- **IPMS Config Libraries**: Security (Keycloak), API documentation
- **Third-Party Libraries**: MapStruct, Lombok, Spring Cloud components

#### Benefits
- **Version Consistency**: Single source of truth for all dependency versions
- **Simplified Maintenance**: Update versions in one place
- **Conflict Resolution**: Prevents version conflicts between services
- **Developer Experience**: No need to specify versions for managed dependencies

## Configuration Management

### Spring Profiles
- **local**: Development environment with local configuration
- **test**: Test environment configuration
- **release**: Pre-production environment
- **prod**: Production environment

### External Configuration
- **Spring Cloud Config**: Centralized configuration with `${CONFIG_REPO_PATH}`
- **Environment variables**: `SPRING_PROFILE` for profile selection
- **Profile-specific**: `application-{profile}.yml` files for environment-specific settings

### Keycloak Configuration
```yaml
# Key configuration properties
keycloak:
  realm: ipms-default
  
ipms:
  keycloak-master-clientId: ipms-web
  keycloak-master-clientNo: client-credentials-id
```

## Key Development Patterns

### Modern Java Usage
```java
// Prefer these patterns
var list = stream.toList();                    // Instead of collect(Collectors.toList())
var emptyList = List.of();                     // Instead of new ArrayList<>()
var users = userList.stream().toList();       // Modern collection handling
```

### Null Safety & Validation
```java
// Always validate input parameters
ObjectUtils.requireNonNull(username, "username cannot be null.");
ObjectUtils.requireNonNull(password, "password cannot be null.");
ObjectUtils.requireNonNull(clientId, "clientId cannot be null.");
```

### Security Context Integration
```java
// Use SecurityUtils for current user context
var accessToken = SecurityUtils.getCurrentAccessToken()
    .orElseThrow(() -> new InvalidAccessTokenException("Access token is not found."));
```

### Exception Handling Pattern
```java
// Use custom exceptions with proper error codes
if (tokenDto == null) {
    throw new AuthenticationFailedException();
}

// Chain exceptions for better error context
.orElseThrow(() -> new InvalidAccessTokenException(ACCESS_TOKEN_NOT_FOUND));
```

### Token Processing Pattern
```java
// Safe token verification with Keycloak
var accessToken = TokenVerifier.create(permissionToken.getAccessToken(), AccessToken.class)
    .getToken();

// Extract user roles safely
var roles = Optional.ofNullable(accessToken.getResourceAccess(clientId))
    .orElseThrow(UserAnyRoleException::new)
    .getRoles();
```

## Testing Strategy

### Test Structure
- **Integration tests**: `*ControllerIntegrationTest` classes
- **Unit tests**: `*ServiceTest` classes for business logic
- **Mock integration**: Mockito for external dependencies (Keycloak)

### Test Configuration
- **Separate test configuration**: `/src/test/resources/ipms-user/application.yml`
- **JUnit 5**: Primary testing framework with JUnit 4 vintage support
- **Mockito**: Mock Keycloak service interactions

### Key Test Scenarios
- **Authentication flows**: Login, token refresh, logout
- **Authorization checks**: Role-based access control
- **Error handling**: Invalid tokens, authentication failures
- **User management**: User lookup, role filtering

## Package Structure

```
com.ipms.user/
├── config/          # Exception handling configuration
├── controller/      # REST endpoints (1 main controller)
├── dto/             # Data Transfer Objects
│   ├── auth/        # Authentication DTOs
│   ├── keycloak/    # Keycloak-specific DTOs
│   └── role/        # User role DTOs
├── exception/       # Custom exceptions and error codes
├── mapper/          # MapStruct mappers
└── service/         # Business logic interfaces
    ├── impl/        # Service implementations
    └── keycloak/    # Keycloak integration services
```

## Docker & Deployment

### Docker Configuration
- **Base image**: `openjdk:17.0.2`
- **Port**: 8010 (exposed)
- **JAR location**: `target/*.jar`
- **Working directory**: `/app`

### Maven Configuration
- **Java 17**: Required runtime version
- **Spring Boot 2.7.0**: Framework version
- **Spring Cloud 2021.0.2**: Microservice stack
- **IPMS BOM 1.0.0**: Shared dependency management

## Shared Libraries

The service depends on several IPMS shared libraries:
- **ipms-core-common**: Shared utilities and validation
- **ipms-core-i18n**: Internationalization support
- **ipms-core-exception-handling**: Centralized exception handling
- **ipms-config-security**: Keycloak security configuration
- **ipms-config-apidoc**: OpenAPI documentation

## Common Operations

### Token Management
```java
// Get access token with username/password
AuthDto authDto = authService.getAccessToken(username, password, clientId, otp);

// Refresh token
AuthDto refreshedAuth = authService.getRefreshToken(refreshToken, clientId);

// Logout user
authService.logout(accessToken, refreshToken, clientId);
```

### User Management
```java
// Get user by username
UserInfoDto user = authService.getByUsername(username, accessToken);

// Get users by role
List<UserInfoDto> users = authService.getUserListByRole(accessToken, roles);

// Get user with expense codes
UserInfoDto user = authService.getExpenseCodesByUsername(username, accessToken, expenseCodes);
```

### Role-Based Operations
```java
// Get all roles
List<RoleDto> roles = authService.getRoleList(accessToken);

// Filter users by roles
List<UserInfoDto> filteredUsers = userList.stream()
    .filter(user -> roles.stream().anyMatch(role -> user.getRoles().contains(role)))
    .toList();
```

## API Endpoints

### Authentication Endpoints
- **POST /auth/access-token/{clientId}**: Login with username/password
- **POST /auth/refresh-token/{clientId}**: Refresh access token
- **POST /auth/logout/{clientId}**: Logout user
- **POST /auth/user-info/{clientId}**: Get current user information

### User Management Endpoints
- **GET /auth/role-list**: Get all available roles
- **GET /auth/user-list**: Get all users with roles
- **GET /auth/user-list/roles/{roles}**: Get users by specific roles
- **GET /auth/user-by-email/{email}**: Get user by email
- **GET /auth/user-by-username/{username}**: Get user by username
- **GET /auth/expense-codes-by-username/{username}**: Get user with expense codes

## Important Notes

- **Port 8010**: Default application port
- **Keycloak integration**: All authentication operations delegate to Keycloak
- **OAuth2/OpenID Connect**: Standard authentication protocols
- **Role-based access control**: Supports multi-role authorization
- **OTP support**: Two-factor authentication capability
- **Expense code filtering**: Special user attribute for financial operations
- **Permission-based authorization**: Fine-grained access control
- **Token verification**: Uses Keycloak token verification for security
- **Stateless authentication**: JWT-based token management
- **Multi-client support**: Different client IDs for different applications