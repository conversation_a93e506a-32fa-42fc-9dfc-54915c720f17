package com.ipms.activity.model;

import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "renewal")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class Renewal extends BulletinRelatedActivity {
    @Column
    private String applicationNumber;

    @Column
    private LocalDate renewalDate;

    @Column
    private String decisionNumber;
}
