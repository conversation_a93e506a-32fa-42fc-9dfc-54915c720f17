package com.ipms.user.dto.keycloak;

import feign.form.FormProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KeycloakAuthRequestDto {
  @FormProperty("grant_type")
  private String grantType;

  @FormProperty("client_id")
  private String clientId;

  @FormProperty("client_secret")
  private String clientSecret;

  private String username;

  private String password;

  @FormProperty("refresh_token")
  private String refreshToken;

  private String audience;

  private String totp;
}
