package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.CourtDto;
import com.ipms.paramcommand.dto.CourtFilterRequest;
import com.ipms.paramcommand.dto.CourtPageDto;
import com.ipms.paramcommand.service.CourtService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RequiredArgsConstructor
@RestController
@RequestMapping("/court")
public class CourtController {
    private final CourtService service;

    @PostMapping
    public BaseResponse<CourtDto> save(@Valid @RequestBody CourtDto courtDto) {
        var court = service.save(courtDto);
        return BaseResponse.<CourtDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(court)
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<CourtDto> update(@Valid @RequestBody CourtDto courtDto, @PathVariable Long id) {
        var court = service.update(courtDto, id);
        return BaseResponse.<CourtDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(court)
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<CourtPageDto> getAll(CourtFilterRequest filterRequest, @PathVariable int page, @PathVariable int size) {
        var courtPageDto = service.getAll(filterRequest, page, size);
        return BaseResponse.<CourtPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(courtPageDto)
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<CourtDto> getById(@PathVariable Long id) {
        var courtDto = service.getById(id);
        return BaseResponse.<CourtDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(courtDto)
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
