package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.activity.dto.CustomsRecordalRenewalDto;
import com.ipms.activity.dto.CustomsRecordalRenewalPageDto;
import com.ipms.activity.service.CustomsRecordalRenewalService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class CustomsRecordalRenewalControllerTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private CustomsRecordalRenewalService service;

    ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());
    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {

        Mockito.when(service.save(Mockito.any(CustomsRecordalRenewalDto.class)))
                .thenReturn(getCustomsRecordalRenewalDto());
        mvc.perform(post("/customs-recordal/renewal")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(getCustomsRecordalRenewalDto()))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {

        Mockito.when(service.update(Mockito.any(CustomsRecordalRenewalDto.class), Mockito.anyLong()))
                .thenReturn(getCustomsRecordalRenewalDto());
        mvc.perform(put("/customs-recordal/renewal/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(getCustomsRecordalRenewalDto()))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetById_thenReturnBaseResponse() throws Exception {

        Mockito.when(service.getById(Mockito.anyLong()))
                .thenReturn(getCustomsRecordalRenewalDto());

        mvc.perform(get("/customs-recordal/renewal/1/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(getCustomsRecordalRenewalDto()))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByActivityId_thenReturnBaseResponse() throws Exception {
        var customsRecordalRenewalPageDto = CustomsRecordalRenewalPageDto.builder()
                .customsRecordalRenewalDtos(List.of(getCustomsRecordalRenewalDto()))
                .totalPages(1)
                .totalElements(1)
                .build();
        Mockito.when(service.getAllByActivityId(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(customsRecordalRenewalPageDto);

        mvc.perform(get("/customs-recordal/renewal/1/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(getCustomsRecordalRenewalDto()))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());
        mvc.perform(delete("/customs-recordal/renewal/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    private CustomsRecordalRenewalDto getCustomsRecordalRenewalDto() {
        return CustomsRecordalRenewalDto.builder()
                .activityId(1L)
                .renewalInstructionDate(LocalDate.of(2023, 1, 1))
                .renewalDate(LocalDate.of(2023, 1, 1))
                .nextRenewalDate(LocalDate.of(2023, 1, 1))
                .build();
    }
}