eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:prod-ipms-mail}.prod-ipms-mail
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  email-topic: emailTopic
  retry-policy:
    max-attempts: 5
  email-trx-topic: emailTrxTopic
  reply-status-topic: replyStatusTopic
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  invoice-is-ready-topic: invoiceIsReadyTopic

spring:
  datasource:
    url: *********************************************************************************
    username: ${db_username}
    password: ${db_password}
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ${keycloak_ipms_backend_client_secret}
        provider:
          keycloak:
            token-uri: http://keycloak.keycloak/auth/realms/ipms-production/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-production
  auth-server-url: http://keycloak.keycloak/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ${keycloak_ipms_backend_client_secret}

azure:
  mail:
    clientId: ${azure_mail_client}
    clientSecret: ${azure_mail_client_secret}
    tenantId: ${azure_mail_client_tenant_id}
    authTenant: common
    graphUserScopes: user.read,mail.read,mail.send
    scopes:
      - https://graph.microsoft.com/.default
    default-folder-addresses:
      - <EMAIL>
    ipms-folder-addresses:
      - <EMAIL>
    period: 65
    default-folder: inbox
    ipms-folder: load-ipms
    ipms-document-folder: load-ipms-for-document
    categories:
      - IPMS
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=ipmsprodstorage;AccountKey=${azure_storage_account_key};
    container-name: ipmsprodsa
    sas-host-url: https://ipmsprodstorage.blob.core.windows.net/ipmsprodsa/