package com.ipms.activity.converter;

import com.ipms.activity.enums.PIType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class PITypeConverter implements AttributeConverter<PIType, String> {
    @Override
    public String convertToDatabaseColumn(PIType piType) {
        return piType == null ? null : piType.getValue();
    }

    @Override
    public PIType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : PIType.getEnum(s);
    }
}
