package com.ipms.integration.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class JdeOrderGetResponse {
    private JdeResponseInformation responseInformatin;
    private List<Order> orders;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Order {
        private String erpOrderNumber;
        private String erpOrderType;
        private String orderNumber;
        private String orderDate;
        private String customerorSupplierNumber;
        private String businessUnit;
        private String status;
    }
}
