package com.ipms.activity.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum PartyProvidingGuarantee {
    TO_PAID_BY_THE_CLIENT_ATTORNEY("01"),
    PAID_UPFRONT_BY_THE_CLIENT_ATTORNEY("02"),
    ATTORNEY("03"),
    CLIENT("04");

    private final String value;

    PartyProvidingGuarantee(String value) {
        this.value = value;
    }

    public static PartyProvidingGuarantee getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
