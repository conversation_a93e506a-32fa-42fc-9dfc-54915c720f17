package com.ipms.activity.converter;

import com.ipms.activity.enums.CustomsSuspensionsOperationType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Objects;

@Converter(autoApply = true)
public class CustomsSuspensionsOperationTypeConverter implements AttributeConverter<CustomsSuspensionsOperationType, String> {
    @Override
    public String convertToDatabaseColumn(CustomsSuspensionsOperationType operationType) {
        return Objects.isNull(operationType) ? null : operationType.getValue();
    }

    @Override
    public CustomsSuspensionsOperationType convertToEntityAttribute(String s) {
        return Objects.isNull(s) || s.isEmpty() ? null : CustomsSuspensionsOperationType.getEnum(s);
    }
}
