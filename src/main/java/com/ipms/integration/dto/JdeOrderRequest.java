package com.ipms.integration.dto;

import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
public class JdeOrderRequest {
    private String mainCompany;
    private String currencyCode;
    private String orderNumber;
    private String orderDate;
    private String customerorSupplierNumber;
    private String businessUnit;
    private List<Attachment> attachments;
    private List<OrderLine> orderLines;

    @AllArgsConstructor
    @Data
    public static class Attachment {
        private String attachment;
    }

    @Builder
    @Data
    public static class OrderLine {
        private String billingId;
        private String itemNumber;
        private String orderLineNo;
        private String quantity;
        private BigDecimal unitPrice;
        private BigDecimal discountRate;
    }
}
