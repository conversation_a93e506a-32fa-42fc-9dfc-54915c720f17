package com.ipms.document.service.impl;

import com.ipms.config.storage.exception.StorageFileNotFoundException;
import com.ipms.config.storage.service.StorageService;
import com.ipms.document.dto.SampleAttachmentDto;
import com.ipms.document.mapper.SampleAttachmentMapper;
import com.ipms.document.model.SampleAttachment;
import com.ipms.document.repository.SampleAttachmentRepository;
import com.ipms.document.service.SampleAttachmentService;
import com.ipms.document.service.SampleService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;

import java.util.UUID;

@RequiredArgsConstructor
@Service
public class SampleAttachmentServiceImpl implements SampleAttachmentService {

    private final SampleAttachmentRepository repository;
    private final SampleAttachmentMapper mapper;
    private final StorageService storageService;
    private final SampleService sampleService;

    @Override
    public SampleAttachmentDto save(SampleAttachmentDto attachmentDto) {
        var evidence = sampleService.getSampleById(attachmentDto.getSampleId());
        var attachment = mapper.toSampleAttachment(attachmentDto);
        attachment.setSample(evidence);
        attachment.setFileUniqueName(prepareDocumentName(attachment.getFileName()));
        var saved = repository.save(attachment);
        return toAttachmentDto(saved);
    }

    @Override
    public void delete(Long id, Long version) {
        var attachment = getAttachmentById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .version(version)
                .build();
        repository.save(attachment);
    }

    public static String prepareDocumentName(String fileName) {
        return "s" + UUID.randomUUID() + "." + FilenameUtils.getExtension(fileName);
    }

    private SampleAttachment getAttachmentById(Long id) {
        return repository.findById(id).orElseThrow(StorageFileNotFoundException::new);
    }

    private SampleAttachmentDto toAttachmentDto(SampleAttachment attachment) {
        var attachmentDto = mapper.toSampleAttachmentDto(attachment);
        var url = storageService.generateSas(mapper.toStorageFile(attachmentDto));
        attachmentDto.setFileSignedUrl(url);
        return attachmentDto;
    }
}
