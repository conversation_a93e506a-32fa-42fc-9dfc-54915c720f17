include:
  - project: 'ipms-developers/devops/gitlab-ci'
    file: '/java/maven.yml'

build:
  extends: .build

test:
  extends: .test

artifact:
  extends: .artifact

sonarqube:
  extends: .sonarqube

docker:
  extends: .docker

clair:
  extends: .clair

# For the Test Environment
test-deploy:
  extends: .deploy-k8s-test-test

release-deploy:
  extends: .deploy-k8s-test-release

# For the Prod Environment
prod-deploy:
  extends: .deploy-k8s-prod-main
