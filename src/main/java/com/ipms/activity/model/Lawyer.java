package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.Set;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "lawyer")
@Entity
@Where(clause = "is_deleted='0'")
public class Lawyer extends VersionedEntity {

    @Column
    private String type;

    @Column
    private String name;

    @Column
    private String email;

    @Column
    private String phoneNumber;

    @ManyToMany(cascade = CascadeType.ALL)
    @JoinTable(
            name = "lawyer_agency",
            joinColumns = @JoinColumn(name = "lawyer_id"),
            inverseJoinColumns = @JoinColumn(name = "agency_id"))
    private Set<Agency> agencies;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name="activity_counter_right_owner_id")
    private CounterRightOwnerCard counterRightOwnerCard;

}
