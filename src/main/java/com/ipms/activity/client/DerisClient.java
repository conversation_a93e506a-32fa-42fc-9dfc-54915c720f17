package com.ipms.activity.client;

import com.ipms.activity.config.DerisClientConfig;
import com.ipms.activity.dto.MarkInformationRequestParam;
import com.ipms.activity.dto.turkpatent.MarkInformationResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = "deris-client", url = "${deris.url}", configuration = DerisClientConfig.class)
public interface DerisClient {

    @GetMapping(
            value = "/marka",
            consumes = MediaType.APPLICATION_XML_VALUE)
    MarkInformationResponse getMarkInformation(
            @SpringQueryMap MarkInformationRequestParam requestParam);
}
