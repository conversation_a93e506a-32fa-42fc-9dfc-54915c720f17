eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:release-ipms-transfer}.release-ipms-transfer
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  transfer-topic: dataTransferReleaseTopic
  notification-topic: notificationReleaseTopic
  data-transfer-topic: dataTransferReleaseTopic

spring:
  datasource:
    url: ************************************************************************************
    username: ipms_release_user
    password: zwk9w4TpjzG7TVX9
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-release/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web
  url: https://ipmsdemo.com/internal-module/

keycloak:
  realm: ipms-release
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk


legacy:
  uri: http://**************:8045/
  user: acc_deris_root
  password: pQL7.4\@*mgr?ZCa%

transfer:
  minute:
    activity: 10
    firm: 10
    issue: 10
    matter: 10
    timesheet: 10
    document: 10
    disbursement: 10
    expense: 10
    deadline: 10