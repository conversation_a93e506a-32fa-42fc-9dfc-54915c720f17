package com.ipms.integration.consumer;

import com.ipms.integration.service.BillingOrderIntegrationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Slf4j
public class BillingOrderIntegrationConsumer {

    private final BillingOrderIntegrationService billingOrderIntegrationService;

    @KafkaListener(topics = "${kafka.billing-order-integration-saved-topic}")
    public void consumeBillingOrder(String eventStr) {
        billingOrderIntegrationService.saveBillingOrder(Long.valueOf(eventStr));
    }

    @KafkaListener(topics = "${kafka.billing-order-cancelled-topic}")
    public void consumeBillingOrderCancel(String eventStr) {
        billingOrderIntegrationService.processBillingOrderCancelledEvent(Long.valueOf(eventStr));
    }
}
