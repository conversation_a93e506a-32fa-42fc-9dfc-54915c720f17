package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.activity.dto.ManuelBillingProcessRequest;
import com.ipms.activity.service.ActivityBillingProcessService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class ActivityBillingProcessControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private ActivityBillingProcessService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());


    @Test
    public void whenStartBillingProcess_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).startBillingProcessByBillingDay(Mockito.any(LocalDate.class));
        mvc.perform(post("/start-billing-process/2024-10-01")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenStartManuelBillingProcess_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).startManuelBillingProcess(Mockito.anyLong());
        var billingProcessRequest = ManuelBillingProcessRequest.builder()
                .activityId(1L)
                .build();
        mvc.perform(post("/manuel-billing-process")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(billingProcessRequest)))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetActivitiesForBillingCart_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getActivitiesForBillingCart("assignee")).thenReturn(List.of());
        mvc.perform(get("/billing-cart-activities/assignee")
                       .contentType(MediaType.APPLICATION_JSON)
                       .accept(MediaType.APPLICATION_JSON))
               .andExpect(status().isOk());
    }

    @Test
    public void whenUndoBilling_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).undoBillingApproval(1L);
        mvc.perform(put("/undo-billing/1")
                       .contentType(MediaType.APPLICATION_JSON)
                       .accept(MediaType.APPLICATION_JSON))
               .andExpect(status().isOk());
    }

    @Test
    public void whenCancelBillingOrder_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).cancelBillingOrder(1L);
        mvc.perform(put("/cancel-billing-order/1")
                       .contentType(MediaType.APPLICATION_JSON)
                       .accept(MediaType.APPLICATION_JSON))
               .andExpect(status().isOk());
    }

    @Test
    public void whenStartBillingProcessByBillingAmount_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).startBillingProcessByBillingAmount();
        mvc.perform(post("/start-billing-process-by-billing-amount")
                       .contentType(MediaType.APPLICATION_JSON)
                       .accept(MediaType.APPLICATION_JSON))
               .andExpect(status().isOk());
    }

    @Test
    public void whenStartBillingProcessByStatus_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).startBillingProcessByStatus(LocalDate.parse("2021-01-01"));
        mvc.perform(post("/start-billing-process-by-status/2021-01-01")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }


    @Test
    public void whenCheckBillingCancellable_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.checkBillingCancellable(Mockito.anyLong())).thenReturn(Mockito.anyBoolean());

        mvc.perform(get("/check-billing-cancellable/1")).andExpect(status().isOk());
    }

    @Test
    public void whenCheckBillingOrderCancellable_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.isBillingOrderCancellationSafe(Mockito.anyString())).thenReturn(Mockito.anyBoolean());

        mvc.perform(get("/is-billing-order-cancellation-safe/test123")).andExpect(status().isOk());
    }

    @Test
    public void whenCancelBilling_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).cancelBilling(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyBoolean());

        mvc.perform(put("/cancel-billing/1/2/true")).andExpect(status().isOk());
    }

}
