package com.ipms.document.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = StringListSizeValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface StringListSize {
    String message() default "Each string in the list must be at most {max} characters long";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    int max();
}

