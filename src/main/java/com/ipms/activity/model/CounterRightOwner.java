package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Set;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "counter_right_owner")
@Entity
@Where(clause = "is_deleted='0'")
public class CounterRightOwner extends VersionedEntity {

    @Column
    private String name;

    @JsonIgnore
    @OneToMany(mappedBy = "counterRightOwner")
    private Set<CounterRightOwnerCard> counterRightOwnerCards;

    @Column
    private String address;
}
