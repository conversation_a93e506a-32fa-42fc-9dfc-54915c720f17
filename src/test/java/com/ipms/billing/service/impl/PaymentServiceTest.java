package com.ipms.billing.service.impl;

import com.ipms.billing.dto.PaymentDto;
import com.ipms.billing.dto.PaymentFilterRequest;
import com.ipms.billing.enums.PaymentStatus;
import com.ipms.billing.exception.PaymentNotFoundException;
import com.ipms.billing.mapper.PaymentMapper;
import com.ipms.billing.model.Payment;
import com.ipms.billing.repository.PaymentRepository;
import com.ipms.billing.service.PaymentService;
import com.ipms.billing.specification.PaymentSpecification;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class PaymentServiceTest {

    @MockBean
    PaymentService service;

    @Mock
    private PaymentRepository repository;

    @Mock
    private PaymentMapper mapper;

    private Payment testPayment;
    private PaymentDto testPaymentDto;
    private PaymentFilterRequest filterRequest;
    private final Long TEST_ID = 1L;
    private final Long TEST_VERSION = 1L;
    private final Long INVALID_ID = 99L;
    private final String TEST_ACCRUAL_NUMBER = "ACC123";

    @Before
    public void setUp() {
        service = new PaymentServiceImpl(repository, mapper);

        testPayment = createTestPayment();
        testPaymentDto = createTestPaymentDto();

        filterRequest = PaymentFilterRequest.builder()
                .issuerIds(List.of(1L))
                .matterIds(List.of(2L))
                .accrualNumbers(List.of(TEST_ACCRUAL_NUMBER))
                .statuses(List.of(PaymentStatus.WAITING_FOR_APPROVAL))
                .build();
    }

    private Payment createTestPayment() {
        return Payment.builder()
                .id(TEST_ID)
                .accrualNumber(TEST_ACCRUAL_NUMBER)
                .status(PaymentStatus.WAITING_FOR_APPROVAL)
                .version(TEST_VERSION)
                .isDeleted(false)
                .build();
    }

    private PaymentDto createTestPaymentDto() {
        return PaymentDto.builder()
                .id(TEST_ID)
                .accrualNumber(TEST_ACCRUAL_NUMBER)
                .status(PaymentStatus.WAITING_FOR_APPROVAL)
                .version(TEST_VERSION)
                .build();
    }

    @Test
    public void givenValidDto_whenSave_thenReturnSavedDto() {
        when(mapper.toPayment(any(PaymentDto.class))).thenReturn(testPayment);
        when(repository.save(any(Payment.class))).thenReturn(testPayment);
        when(mapper.toPaymentDto(any(Payment.class))).thenReturn(testPaymentDto);

        var result = service.save(testPaymentDto);

        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(TEST_ID);
        assertThat(result.getStatus()).isEqualTo(PaymentStatus.WAITING_FOR_APPROVAL);
        verify(repository, times(1)).save(any(Payment.class));
        verify(mapper, times(1)).toPaymentDto(any(Payment.class));
    }

    @Test
    public void givenDtoWithNullStatus_whenSave_thenSetDefaultStatusAndSave() {
        var paymentWithNullStatus = testPayment.toBuilder().status(null).build();
        var dtoWithNullStatus = PaymentDto.builder()
                .id(TEST_ID)
                .accrualNumber(TEST_ACCRUAL_NUMBER)
                .status(null)
                .version(TEST_VERSION)
                .build();
        var savedPayment = testPayment.toBuilder().status(PaymentStatus.WAITING_FOR_APPROVAL).build();

        when(mapper.toPayment(any(PaymentDto.class))).thenReturn(paymentWithNullStatus);
        when(repository.save(any(Payment.class))).thenReturn(savedPayment);
        when(mapper.toPaymentDto(any(Payment.class))).thenReturn(testPaymentDto);

        var result = service.save(dtoWithNullStatus);

        assertThat(result.getStatus()).isEqualTo(PaymentStatus.WAITING_FOR_APPROVAL);
    }

    @Test
    public void givenValidId_whenGetPaymentById_thenReturnPayment() {
        when(repository.findById(TEST_ID)).thenReturn(Optional.of(testPayment));

        var result = service.getPaymentById(TEST_ID);

        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(TEST_ID);
        verify(repository, times(1)).findById(TEST_ID);
    }

    @Test
    public void givenInvalidId_whenGetPaymentById_thenThrowException() {
        when(repository.findById(INVALID_ID)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> service.getPaymentById(INVALID_ID))
                .isInstanceOf(PaymentNotFoundException.class);
    }

    @Test
    public void givenAccrualNumber_whenGetByAccrualNumber_thenReturnPaymentDtoList() {
        when(repository.findByAccrualNumberContainsIgnoreCase(TEST_ACCRUAL_NUMBER))
                .thenReturn(Optional.ofNullable(testPayment));
        when(mapper.toPaymentDto(any(Payment.class))).thenReturn(testPaymentDto);

        service.getByAccrualNumber(TEST_ACCRUAL_NUMBER);

        verify(repository, times(1)).findByAccrualNumberContainsIgnoreCase(TEST_ACCRUAL_NUMBER);
    }

    @Test
    public void givenFilterRequest_whenGetAll_thenReturnPagedDtoList() {
        int page = 0;
        int size = 10;
        var pageResult = new PageImpl<>(List.of(testPayment), PageRequest.of(page, size), 1);

        when(repository.findAll(any(PaymentSpecification.class), any(PageRequest.class)))
                .thenReturn(pageResult);
        when(mapper.toPaymentDto(any(Payment.class))).thenReturn(testPaymentDto);

        var result = service.getAll(filterRequest, page, size);

        assertThat(result.getTotalElements()).isEqualTo(1);
        assertThat(result.getTotalPages()).isEqualTo(1);
        assertThat(result.getPayments()).hasSize(1);
        assertThat(result.getPayments().get(0).getId()).isEqualTo(TEST_ID);
        verify(repository, times(1)).findAll(any(PaymentSpecification.class),
                any(PageRequest.class));
    }

    @Test
    public void givenValidDtoAndId_whenUpdate_thenReturnUpdatedDto() {
        var updatedPayment = testPayment.toBuilder()
                .status(PaymentStatus.PAID)
                .build();

        var updatedDto = PaymentDto.builder()
                .id(TEST_ID)
                .accrualNumber(TEST_ACCRUAL_NUMBER)
                .status(PaymentStatus.PAID)
                .build();

        when(repository.findById(TEST_ID)).thenReturn(Optional.of(testPayment));
        when(mapper.toPaymentFromDto(any(PaymentDto.class), any(Payment.class))).thenReturn(updatedPayment);
        when(repository.save(any(Payment.class))).thenReturn(updatedPayment);
        when(mapper.toPaymentDto(any(Payment.class))).thenReturn(updatedDto);

        var result = service.update(testPaymentDto, TEST_ID, TEST_VERSION);

        assertThat(result.getId()).isEqualTo(TEST_ID);
        assertThat(result.getStatus()).isEqualTo(PaymentStatus.PAID);
        verify(repository, times(1)).findById(TEST_ID);
        verify(repository, times(1)).save(any(Payment.class));
    }

    @Test
    public void givenInvalidId_whenUpdate_thenThrowException() {
        when(repository.findById(INVALID_ID)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> service.update(testPaymentDto, INVALID_ID, TEST_VERSION))
                .isInstanceOf(PaymentNotFoundException.class);
    }

    @Test
    public void whenDeleteUnapprovedPayments_thenMarkPaymentsAsDeleted() {
        List<Payment> unapprovedPayments = List.of(
                testPayment,
                Payment.builder()
                        .id(2L)
                        .status(PaymentStatus.PENDING_PAYMENT)
                        .build()
        );

        when(repository.findByStatusIn(anyList())).thenReturn(unapprovedPayments);
        when(repository.saveAll(anyList()))
                .thenAnswer(invocation -> invocation.getArgument(0));

        service.deleteUnapprovedPayments();

        verify(repository).findByStatusIn(List.of(
                PaymentStatus.WAITING_FOR_APPROVAL,
                PaymentStatus.PENDING_PAYMENT
        ));
        verify(repository).saveAll(argThat(list ->
                ((List<Payment>)list).stream().allMatch(Payment::isDeleted)));
    }

    @Test
    public void whenNoUnapprovedPayments_thenNoSaveAllCalled() {
        when(repository.findByStatusIn(anyList())).thenReturn(List.of());

        service.deleteUnapprovedPayments();

        verify(repository).findByStatusIn(anyList());
        verify(repository, never()).saveAll(anyList());
    }

    @Test
    public void givenFilterRequest_whenGetAllGrouped_thenReturnGroupedPagedResult() {
        int page = 0;
        int size = 2;

        Payment payment1 = Payment.builder()
                .id(1L)
                .accrualNumber("ACC123")
                .status(PaymentStatus.WAITING_FOR_APPROVAL)
                .build();

        Payment payment2 = Payment.builder()
                .id(2L)
                .accrualNumber("ACC123")
                .status(PaymentStatus.PAID)
                .build();

        Payment payment3 = Payment.builder()
                .id(3L)
                .accrualNumber("ACC456")
                .status(PaymentStatus.WAITING_FOR_APPROVAL)
                .build();

        List<Payment> allPayments = List.of(payment1, payment2, payment3);

        PaymentDto dto1 = PaymentDto.builder()
                .id(1L)
                .accrualNumber("ACC123")
                .status(PaymentStatus.WAITING_FOR_APPROVAL)
                .build();

        PaymentDto dto2 = PaymentDto.builder()
                .id(2L)
                .accrualNumber("ACC123")
                .status(PaymentStatus.PAID)
                .build();

        PaymentDto dto3 = PaymentDto.builder()
                .id(3L)
                .accrualNumber("ACC456")
                .status(PaymentStatus.WAITING_FOR_APPROVAL)
                .build();

        when(repository.findAll(any(PaymentSpecification.class))).thenReturn(allPayments);
        when(mapper.toPaymentDto(payment1)).thenReturn(dto1);
        when(mapper.toPaymentDto(payment2)).thenReturn(dto2);
        when(mapper.toPaymentDto(payment3)).thenReturn(dto3);

        var result = service.getAllGrouped(filterRequest, page, size);

        assertThat(result.getTotalElements()).isEqualTo(2L);
        assertThat(result.getTotalPages()).isEqualTo(1);
        assertThat(result.getPaymentGroups()).hasSize(2);

        var firstGroup = result.getPaymentGroups().get(0);
        assertThat(firstGroup.getAccrualNumber()).isEqualTo("ACC123");
        assertThat(firstGroup.getPayments()).hasSize(2);
        assertThat(firstGroup.getPayments())
                .extracting(PaymentDto::getId)
                .containsExactlyInAnyOrder(1L, 2L);

        var secondGroup = result.getPaymentGroups().get(1);
        assertThat(secondGroup.getAccrualNumber()).isEqualTo("ACC456");
        assertThat(secondGroup.getPayments()).hasSize(1);
        assertThat(secondGroup.getPayments().get(0).getId()).isEqualTo(3L);

        verify(repository, times(1)).findAll(any(PaymentSpecification.class));
        verify(mapper, times(3)).toPaymentDto(any(Payment.class));
    }

    @Test
    public void givenEmptyResult_whenGetAllGrouped_thenReturnEmptyPagedResult() {
        int page = 0;
        int size = 10;

        when(repository.findAll(any(PaymentSpecification.class))).thenReturn(List.of());

        var result = service.getAllGrouped(filterRequest, page, size);

        assertThat(result.getTotalElements()).isZero();
        assertThat(result.getTotalPages()).isZero();
        assertThat(result.getPaymentGroups()).isEmpty();

        verify(repository, times(1)).findAll(any(PaymentSpecification.class));
        verify(mapper, never()).toPaymentDto(any(Payment.class));
    }

    @Test
    public void givenMultipleGroups_whenGetAllGroupedWithPaging_thenReturnCorrectPage() {
        int page = 1;
        int size = 1;

        Payment payment1 = Payment.builder().id(1L).accrualNumber("ACC111").build();
        Payment payment2 = Payment.builder().id(2L).accrualNumber("ACC222").build();
        Payment payment3 = Payment.builder().id(3L).accrualNumber("ACC333").build();

        List<Payment> allPayments = List.of(payment1, payment2, payment3);

        PaymentDto dto1 = PaymentDto.builder().id(1L).accrualNumber("ACC111").build();
        PaymentDto dto2 = PaymentDto.builder().id(2L).accrualNumber("ACC222").build();
        PaymentDto dto3 = PaymentDto.builder().id(3L).accrualNumber("ACC333").build();

        when(repository.findAll(any(PaymentSpecification.class))).thenReturn(allPayments);
        when(mapper.toPaymentDto(payment1)).thenReturn(dto1);
        when(mapper.toPaymentDto(payment2)).thenReturn(dto2);
        when(mapper.toPaymentDto(payment3)).thenReturn(dto3);

        var result = service.getAllGrouped(filterRequest, page, size);

        assertThat(result.getTotalElements()).isEqualTo(3L);
        assertThat(result.getTotalPages()).isEqualTo(3);
        assertThat(result.getPaymentGroups()).hasSize(1);
        assertThat(result.getPaymentGroups().get(0).getAccrualNumber()).isEqualTo("ACC222");
    }

    @Test
    public void givenValidIdsAndStatus_whenBulkUpdateStatus_thenPaymentsAreUpdated() {
        List<Long> ids = List.of(1L, 2L);
        var newStatus = PaymentStatus.PAID;

        var payment1 = Payment.builder()
                .id(1L)
                .status(PaymentStatus.WAITING_FOR_APPROVAL)
                .build();

        var payment2 = Payment.builder()
                .id(2L)
                .status(PaymentStatus.PENDING_PAYMENT)
                .build();

        var existingPayments = List.of(payment1, payment2);

        when(repository.findAllById(ids)).thenReturn(existingPayments);
        when(repository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));

        service.bulkUpdateStatus(ids, newStatus);

        verify(repository, times(1)).findAllById(ids);
        verify(repository, times(1)).saveAll(argThat(payments -> {
            List<Payment> paymentList = new ArrayList<>();
            payments.forEach(paymentList::add);
            return paymentList.size() == 2 &&
                    paymentList.stream().allMatch(p -> p.getStatus() == newStatus);
        }));
    }
}
