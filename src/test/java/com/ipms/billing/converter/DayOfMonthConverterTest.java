package com.ipms.billing.converter;

import com.ipms.billing.enums.BillingDay;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class DayOfMonthConverterTest {

    private DayOfMonthConverter dayOfMonthConverter;

    @Test
    public void convertToDatabaseColumn() {
        dayOfMonthConverter = new DayOfMonthConverter();
        Integer result = dayOfMonthConverter.convertToDatabaseColumn(BillingDay.DAY_1);
        assertEquals(1, result);
    }

    @Test
    public void convertToEntityAttribute() {
        dayOfMonthConverter = new DayOfMonthConverter();
        BillingDay result = dayOfMonthConverter.convertToEntityAttribute(1);
        assertEquals(BillingDay.DAY_1, result);
    }
}