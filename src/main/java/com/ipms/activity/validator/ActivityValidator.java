package com.ipms.activity.validator;

import com.ipms.activity.dto.ActivityDto;
import com.ipms.activity.enums.ActivityResponseCode;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.exception.CDLetterNullException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class ActivityValidator {
    public void validate(ActivityDto activityDto) {
        if (activityDto.getType().equals(ActivityType.CEASE_AND_DESIST_LETTER.name()) &&
                (activityDto.getCdLetter()==null || StringUtils.isBlank(activityDto.getCdLetter().getOurPosition()))) {
                throw new CDLetterNullException(ActivityResponseCode.CD_LETTER_NULL);
        }
    }
}
