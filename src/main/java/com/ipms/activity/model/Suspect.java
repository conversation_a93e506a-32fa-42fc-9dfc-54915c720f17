package com.ipms.activity.model;

import com.ipms.core.entity.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "counter_party_suspect")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
public class Suspect extends BaseEntity {
    private String name;
    private String surname;
    private String identityNumber;
    @OneToOne
    @JoinColumn(name = "activity_counter_right_owner")
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private CounterParty counterParty;
}
