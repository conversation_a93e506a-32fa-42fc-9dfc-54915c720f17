package com.ipms.integration.service;

import java.time.LocalDate;

public interface BillingOrderIntegrationService {

    void saveBillingOrder(Long id);

    void sendBillingOrder();

    void updateExternalOrderNumber();

    Boolean isOrderCancelled(Long issuerId, String externalOrderNumber, String billingAccountNo);

    void fetchInvoices(LocalDate updateDateFrom, LocalDate updateDateTo, String mainCompany);

    void processBillingOrderCancelledEvent(Long aLong);
}
