package com.ipms.activity.mapper;

import com.ipms.activity.dto.PreliminaryInjunctionDetailDto;
import com.ipms.activity.model.PreliminaryInjunctionDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface PreliminaryInjunctionDetailMapper {
    PreliminaryInjunctionDetail toPreliminaryInjunctionDetail(PreliminaryInjunctionDetailDto dto);
    PreliminaryInjunctionDetailDto toPreliminaryInjunctionDetailDto(PreliminaryInjunctionDetail preliminaryInjunctionDetail);
    PreliminaryInjunctionDetail toPreliminaryInjunctionDetailFromDto(PreliminaryInjunctionDetailDto dto, @MappingTarget PreliminaryInjunctionDetail preliminaryInjunctionDetail);
}
