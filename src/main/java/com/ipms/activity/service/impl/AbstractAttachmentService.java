package com.ipms.activity.service.impl;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.mapper.AttachmentMapper;
import com.ipms.config.storage.service.StorageService;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public abstract class AbstractAttachmentService {
    private final StorageService storageService;
    protected final AttachmentMapper mapper;

    public String getSignedUrl(AttachmentDto attachmentDto) {
        return storageService.generateSas(mapper.toStorageFile(attachmentDto));
    }

    public void setSignedUrl(AttachmentDto attachmentDto) {
        attachmentDto.setFileSignedUrl(getSignedUrl(attachmentDto));
    }
}
