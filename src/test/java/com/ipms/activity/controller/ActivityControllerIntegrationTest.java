package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.activity.dto.*;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.RiskImpact;
import com.ipms.activity.exception.ActivityNotFoundException;
import com.ipms.activity.service.ActivityService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class ActivityControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private ActivityService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    public void givenId_whenGetActivity_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder().id(1L).build();
        Mockito.when(service.getById(Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(get("/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenGetActivity_thenReturnInternalServerError() throws Exception {
        var ex = new ActivityNotFoundException();
        Mockito.doThrow(ex).when(service).getById(Mockito.anyLong());
        mvc.perform(get("/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }

    @Test
    public void givenId_whenGetActivityListByMatter_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder().id(1L).firmId(1L)
                .status("TEST").type("test").matterId(1L).build();
        Mockito.when(service.getByMatter(Mockito.anyList(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(activityDto));
        mvc.perform(get("/activities/1?sortField=instructionDate&sortDirection=desc")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenGetActivityListByMatterAndType_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder().id(1L).firmId(1L)
                .status("TEST").type("test").matterId(1L).build();
        Mockito.when(service.getByMatterAndType(Mockito.anyList(), Mockito.any(ActivityType.class)))
                .thenReturn(List.of(activityDto));
        mvc.perform(get("/activities?matterIds=1,2&type=COURT_ACTION")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetTimesheetManuelApprovalIds_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getIdsForTimesheetManuelApproval())
                .thenReturn(List.of(1L));
        mvc.perform(get("/activities/ids?timesheetApprovalType=MANUEL")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenDtoAndId_whenUpdate_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder().id(1L).firmId(1L)
                .status("TEST").type("test").version(1L).matterId(1L).build();
        Mockito.when(service.update(Mockito.any(ActivityDto.class), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(activityDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenSave_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .instructionDate(LocalDate.parse("2022-09-29", DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .status("TEST")
                .type("test").matterId(1L)
                .build();
        Mockito.when(service.save(Mockito.any(ActivityDto.class)))
                .thenReturn(activityDto);
        mvc.perform(post("/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(activityDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenDto_whenLinkToIssue_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder().id(1L)
                .issues(List.of(1L,2L)).version(1L).build();
        Mockito.when(service.update(Mockito.any(ActivityDto.class), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(activityDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenIssueId_whenGetByIssue_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder().id(1L)
                .issues(List.of(1L,2L)).version(1L).build();
        Mockito.when(service.getByIssueId(Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(get("/issue/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenFirmId_whenGetActivityListByFirm_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .issues(List.of(1L,2L)).version(1L)
                .build();
        Mockito.when(service.getByFirmId(Mockito.anyLong()))
                .thenReturn(List.of(activityDto));
        mvc.perform(get("/activities/firm/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetList_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .issues(List.of(1L,2L))
                .version(1L)
                .build();

        Mockito.when(service.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(activityDto));

        mvc.perform(get("/ids/1231")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenChangeIssue_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .issues(List.of(1L,2L))
                .version(1L)
                .build();

        Mockito.when(service.addIssue(Mockito.anyLong(),Mockito.anyLong()))
                .thenReturn(activityDto);

        mvc.perform(put("/issue/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .param("newIssueId", "5")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenAddIssue_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .issues(List.of(1L,2L))
                .version(1L)
                .build();

        Mockito.when(service.addIssue(Mockito.anyLong(),Mockito.anyLong()))
                .thenReturn(activityDto);

        mvc.perform(put("/issue/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetIssueByFirm_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getIssuesByFirmId(Mockito.anyLong()))
                .thenReturn(List.of(1L));
        mvc.perform(get("/issues/firm/5")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenAddDocument_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.addDocument(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyBoolean()))
                .thenReturn(activityDto);
        mvc.perform(put("/document/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenRemoveDocument_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.removeDocument(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/document/unlink/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByDocument_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.getByDocumentId(Mockito.anyLong()))
                .thenReturn(List.of(activityDto));
        mvc.perform(get("/document/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetAll_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        var page = ActivityPageDto.builder()
                .activities(List.of(activityDto))
                .totalPages(1L)
                .totalPages(1L)
                .build();
        Mockito.when(service.getAll(Mockito.any(ActivityFilterRequest.class), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(page);
        mvc.perform(get("/activities/1/10")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenAddEvidence_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.addEvidence(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/evidence/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenRemoveEvidence_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.removeEvidence(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/evidence/unlink/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByEvidence_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.getByEvidenceId(Mockito.anyLong()))
                .thenReturn(List.of(activityDto));
        mvc.perform(get("/evidence/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenAddSample_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.addSample(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/sample/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenRemoveSample_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.removeSample(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/sample/unlink/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetBySample_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.getBySampleId(Mockito.anyLong()))
                .thenReturn(List.of(activityDto));
        mvc.perform(get("/sample/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenRemoveIssue_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .status("TEST")
                .type("test")
                .version(1L)
                .matterId(1L)
                .build();
        Mockito.when(service.removeDocument(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/issue/unlink/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenLinkBillingAccount_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .version(1L)
                .billingAccountId(1L)
                .riskImpact(RiskImpact.LOCKED.name())
                .build();
        Mockito.when(service.linkBillingAccount(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(put("/1/billing-account/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUnlinkBillingAccount_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .version(1L)
                .billingAccountId(1L)
                .riskImpact(null)
                .build();
        Mockito.when(service.unlinkBillingAccount(Mockito.anyLong()))
                .thenReturn(activityDto);
        mvc.perform(delete("/1/billing-account")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetOtherActivityIds_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getOtherActivityIds(Mockito.anyLong()))
                .thenReturn(List.of(2L, 3L));
        mvc.perform(get("/other-activity-ids/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetIssueIdsByBillingStatuses_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getIssuesByBillingStatuses(Mockito.anyList()))
                .thenReturn(List.of(1L, 2L));
        mvc.perform(get("/issues/billing-statuses/BILLING_STARTED,REVISION_REQUESTED")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenMatterIds_whenGetIssueIdsByMatterIds_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getIssuesByMatters(Mockito.anyList()))
                .thenReturn(List.of(1L, 2L));
        mvc.perform(get("/issues/matter-ids/1,2")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenImportFromTurkPatent_thenReturnBaseResponse() throws Exception {
        var activityDto = ActivityDto.builder()
                .id(123L)
                .status("TEST")
                .build();

        var request = ImportFromTpRequest.builder()
                .applicationNumber("2023/12345")
                .activityDto(activityDto)
                .build();

        Mockito.when(service.importFromTurkPatent(Mockito.any(ImportFromTpRequest.class)))
                .thenReturn(activityDto);

        mvc.perform(post("/import-from-tp")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetMatterIdsByActivities_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getMatterIdsByActivityIds(Mockito.anyList()))
                .thenReturn(List.of(
                        MatterId.builder().matterId(1L).build(),
                        MatterId.builder().matterId(2L).build()
                ));
        mvc.perform(get("/expenses-matterid/1,2")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
