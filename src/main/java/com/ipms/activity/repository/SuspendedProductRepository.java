package com.ipms.activity.repository;

import com.ipms.activity.model.SuspendedProduct;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface SuspendedProductRepository extends PagingAndSortingRepository<SuspendedProduct, Long> {
    Page<SuspendedProduct> findAllByActivityId(Long activityId, Pageable pageable);

    List<SuspendedProduct> findAllByActivityId(Long activityId);
}
