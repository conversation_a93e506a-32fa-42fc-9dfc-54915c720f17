package com.ipms.document.mapper;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.document.dto.*;
import com.ipms.document.model.Evidence;
import com.ipms.document.model.EvidenceAttachment;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface EvidenceMapper {
    EvidenceDto toDto(Evidence evidence);
    Evidence toEvidenceFromDto(EvidenceDto dto, @MappingTarget Evidence evidence);
    EvidenceAttachment toEvidenceAttachment(EvidenceCreateDto evidenceCreateDto);
    EvidenceAttachment toEvidenceAttachment(EvidenceAttachmentDto attachmentDto);
    Evidence toEvidence(EvidenceCreateDto evidenceCreateDto);
    Evidence toEvidence(EvidenceDto evidenceDto);
    StorageFile toStorageFile(EvidenceAttachmentDto attachmentDto);
}
