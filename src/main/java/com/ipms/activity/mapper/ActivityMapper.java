package com.ipms.activity.mapper;

import com.ipms.activity.dto.ActivityDocumentSummaryDto;
import com.ipms.activity.dto.ActivityDto;
import com.ipms.activity.model.Activity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", uses = {LawyerMapper.class, AgencyMapper.class,
        CriminalActionMapper.class, CustomsRecordalMapper.class, NotaryDeterminationMapper.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ActivityMapper {
    Activity toActivity(ActivityDto activityDto);

    ActivityDto toActivityDto(Activity activity);

    ActivityDocumentSummaryDto toActivityDocumentSummaryDto(Activity activity);

    @Mapping(target = "preliminaryInjunction.details", ignore = true)
    @Mapping(target = "preliminaryInjunction.refundedAttachments", ignore = true)
    @Mapping(target = "preliminaryInjunction.releasedAttachments", ignore = true)
    @Mapping(target = "useInvestigation.investigationFirms", ignore = true)
    @Mapping(target = "tsicPackages", ignore = true)
    @Mapping(target = "billingAccountId", ignore = true)
    @Mapping(target = "cdLetter.date", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "cdLetter.notificationDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "cdLetter.notificationType", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "cdLetter.officialDocumentNumber", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "cdLetter.notaryNameNumber", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "cdLetter.postalTrackingNumber", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "useInvestigation.completionDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "courtAction.ourPosition", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "courtAction.types", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "courtAction.counterActions", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "courtAction.stage", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "preliminaryInjunction.types", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "billingStatus", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "processStartDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "billingPeriodEnds", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "localAgent", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "oppositionOut.adverseApplicationStatus", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "statusDueDate", ignore = true)
    Activity toActivityFromDto(ActivityDto dto, @MappingTarget Activity activity);
}
