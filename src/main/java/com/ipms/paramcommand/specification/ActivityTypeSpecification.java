package com.ipms.paramcommand.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.paramcommand.model.ActivityType;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class ActivityTypeSpecification extends AbstractSpecification<ActivityType> {
    private final List<String> countries;
    private final List<String> matterTypes;
    private final List<String> activities;

    @Override
    public Predicate toPredicate(Root<ActivityType> root, CriteriaQuery<?> query, CriteriaBuilder cb) {

        var predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(matterTypes)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("matterType").in(values))));
        Optional.ofNullable(countries)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("country").in(values))));
        Optional.ofNullable(activities)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("activity").in(values))));
        return predicate;
    }
}
