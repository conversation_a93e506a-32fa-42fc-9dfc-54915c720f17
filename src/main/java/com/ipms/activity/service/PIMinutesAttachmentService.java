package com.ipms.activity.service;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.dto.GuaranteeAttachmentDto;
import com.ipms.activity.model.Guarantee;

import java.util.List;

public interface PIMinutesAttachmentService extends AttachmentService {
    String getSignedUrl(Long id);

    List<String> getSignedUrlsByGuaranteeIds(List<Long> ids);

    void delete(Long id);

    AttachmentDto save(GuaranteeAttachmentDto dto);

    void saveAttachments(List<AttachmentDto> attachmentDtos, Guarantee guarantee);

    void setFileSignedUrls(List<AttachmentDto> attachmentDtos);
}
