package com.ipms.activity.service.impl;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.dto.GuaranteeAttachmentDto;
import com.ipms.activity.exception.AttachmentNotFoundException;
import com.ipms.activity.mapper.AttachmentMapper;
import com.ipms.activity.model.Guarantee;
import com.ipms.activity.model.PIMinutesAttachment;
import com.ipms.activity.repository.PIMinutesAttachmentRepository;
import com.ipms.activity.service.GuaranteeService;
import com.ipms.activity.service.PIMinutesAttachmentService;
import com.ipms.activity.util.ActivityUtils;
import com.ipms.config.storage.service.StorageService;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Primary
public class PIMinutesAttachmentServiceImpl extends AbstractAttachmentService implements PIMinutesAttachmentService {
    private final PIMinutesAttachmentRepository repository;
    private final GuaranteeService guaranteeService;

    public PIMinutesAttachmentServiceImpl(StorageService storageService, AttachmentMapper mapper,
                                          PIMinutesAttachmentRepository repository,
                                          @Lazy GuaranteeService guaranteeService) {
        super(storageService, mapper);
        this.repository = repository;
        this.guaranteeService = guaranteeService;
    }


    @Override
    public String getSignedUrl(Long id) {
        var attachmentDto = getById(id);
        return getSignedUrl(attachmentDto);
    }

    @Override
    public List<String> getSignedUrlsByGuaranteeIds(List<Long> ids) {
        List<PIMinutesAttachment> attachments = repository.getAllByGuarantee_IdIn(ids);
        List<AttachmentDto> dtos = new ArrayList<>();
        List<String> signedUrls = new ArrayList<>();
        attachments.forEach(attachment -> dtos.add(mapper.toAttachmentDto(attachment)));
        dtos.forEach(dto -> signedUrls.add(getSignedUrl(dto.getId())));
        return signedUrls;
    }

    @Override
    public void delete(Long id) {
        var attachment = getAttachmentById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(attachment);
    }

    @Override
    public AttachmentDto save(GuaranteeAttachmentDto dto) {
        var guarantee = guaranteeService.getEntityById(dto.getGuaranteeId());
        dto.setFileUniqueName(ActivityUtils.prepareAttachmentName(dto.getFileName()));
        dto.setFileSignedUrl(getSignedUrl(dto));
        var attachment = mapper.toPIMinutesAttachment(dto);
        attachment.setGuarantee(guarantee);
        var save = repository.save(attachment);
        return mapper.toAttachmentDto(save, dto.getFileSignedUrl());
    }

    @Override
    public void saveAttachments(List<AttachmentDto> updatedAttachments, Guarantee guarantee) {
        if (Objects.nonNull(updatedAttachments)) {
            var existingAttachments = guarantee.getPiMinutesAttachments();
            deleteAttachments(updatedAttachments, existingAttachments);
            updatedAttachments.forEach(attachmentDto -> {
                if (Objects.nonNull(attachmentDto.getId())) {
                    updateExistingAttachment(guarantee, attachmentDto);
                } else {
                    var attachment = mapper.toPIMinutesAttachment(attachmentDto);
                    attachment.setGuarantee(guarantee);
                    existingAttachments.add(attachment);
                }
            });
        }
    }

    @Override
    public void setFileSignedUrls(List<AttachmentDto> attachmentDtos) {
        attachmentDtos.forEach(this::setSignedUrl);
    }

    public AttachmentDto getById(Long id) {
        return mapper.toAttachmentDto(getAttachmentById(id));
    }

    private PIMinutesAttachment getAttachmentById(Long id) {
        return repository.findById(id)
                .orElseThrow(AttachmentNotFoundException::new);
    }

    public void updateExistingAttachment(Guarantee guarantee,
                                         AttachmentDto attachmentDto) {
        var existingAttachment = guarantee.getPiMinutesAttachments().stream()
                .filter(attachment -> attachment.getId().equals(attachmentDto.getId()))
                .findFirst()
                .orElse(repository.findById(attachmentDto.getId()).orElseThrow(AttachmentNotFoundException::new));
        existingAttachment.setGuarantee(guarantee);
        var attachment = mapper.toPIMinutesAttachmentFromDto(attachmentDto, existingAttachment);
        guarantee.getPiMinutesAttachments().add(existingAttachment);
        repository.save(attachment);
    }

    private void deleteAttachments(List<AttachmentDto> attachmentDto,
                                   List<PIMinutesAttachment> existingAttachments) {
        var attachmentToBeDeleted = getAttachmentsToBeDeleted(attachmentDto, existingAttachments);
        attachmentToBeDeleted.forEach(attachment -> repository.save(attachment.toBuilder().isDeleted(Boolean.TRUE).build()));
        existingAttachments.removeAll(attachmentToBeDeleted);
    }

    private List<PIMinutesAttachment> getAttachmentsToBeDeleted(List<AttachmentDto> updatedAttachments, List<PIMinutesAttachment> existingAttachment) {
        return existingAttachment.stream()
                .filter(attachment -> updatedAttachments.stream()
                        .filter(attachmentDto -> attachmentDto.getId() != null)
                        .noneMatch(attachmentDto -> Objects.equals(attachmentDto.getId(), attachment.getId())))
                .toList();
    }
}
