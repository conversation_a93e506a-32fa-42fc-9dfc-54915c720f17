package com.ipms.document.util;

import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class DocumentUtilsTest {

    @Test
    public void whenExtractDocketNo_ForUyap_then() {
        // Test case 1: Valid input with a docket number
        String input1 = "This is a sample text #ES-1234 -";
        String expected1 = "1234";
        assertEquals(expected1, DocumentUtils.extractDocketNoForUyap(input1));

        // Test case 2: Valid input with a different docket number
        String input2 = "Another example #ES-5678 - Additional text";
        String expected2 = "5678";
        assertEquals(expected2, DocumentUtils.extractDocketNoForUyap(input2));

        // Test case 3: Input without a docket number
        String input3 = "No docket number here";
        assertNull(DocumentUtils.extractDocketNoForUyap(input3));

        // Test case 4: Empty input
        String input4 = "";
        assertNull(DocumentUtils.extractDocketNoForUyap(input4));
    }

    @Test
    public void whenExtractDocketNoForPTT_then() {
        // Test case 1: Valid input with a docket number
        String input1 = "16184-81942-03896 numaralı tebligat adresinize 26.02.2024 16:05 tarihinde ADALET BAKANLIĞI " +
                "tarafından Ankara 4. Fikri ve Sınaî Haklar Hukuk Mahkemesi [2023/140] [Ankara 4. " +
                "Fikri ve Sınaî Haklar Hukuk Mahkemesi-5000846806526-0-2023/140] konulu bir e-tebligat gönderildi.\n" +
                "\nElektronik yolla tebligat, muhatabın elektronik tebligat adresine ulaştığı tarihi " +
                "izleyen beşinci günün sonunda yapılmış sayılır.\n";
        String expected1 = "2023/140";
        assertEquals(expected1, DocumentUtils.extractDocketNoForPTT(input1));

        // Test case 2: Input without a docket number
        String input2 = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor " +
                "incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud " +
                "exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in " +
                "reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint " +
                "occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.";
        assertNull(DocumentUtils.extractDocketNoForPTT(input2));
    }
}