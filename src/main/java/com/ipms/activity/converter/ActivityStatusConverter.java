package com.ipms.activity.converter;

import com.ipms.activity.enums.ActivityStatus;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class ActivityStatusConverter implements AttributeConverter<ActivityStatus, String> {
    @Override
    public String convertToDatabaseColumn(ActivityStatus activityStatus) {
        return activityStatus == null ? null : activityStatus.getValue();
    }

    @Override
    public ActivityStatus convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : ActivityStatus.getEnum(s);
    }
}
