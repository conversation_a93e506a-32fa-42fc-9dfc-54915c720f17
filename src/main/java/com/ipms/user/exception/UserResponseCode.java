package com.ipms.user.exception;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum UserResponseCode implements Code {

    AUTH_FAILED(1000, "error.code.user.auth_failed"),
    INVALID_CREDENTIALS(1001, "error.code.user.invalid_credentials"),
    INVALID_REFRESH_TOKEN(1002, "error.code.user.invalid_refresh_token"),
    INVALID_ACCESS_TOKEN(1003, "error.code.user.invalid_access_token"),
    USER_NOT_ACTIVE(1004, "error.code.user.not_active"),
    USER_ANY_ROLE(1005, "error.code.user.has_not_role"),
    USER_NOT_FOUND(1006, "error.code.user.not_found"),
    OTP_REQUIRED_ACTION(1007, "error.code.user.otp_required_action");

    private final Integer code;
    private final String messageKey;

    UserResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
