package com.ipms.activity.controller;

import com.ipms.activity.dto.CustomsSuspensionsReceiverDto;
import com.ipms.activity.service.CustomsSuspensionsReceiverService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/customs-suspensions/receivers")
public class CustomsSuspensionsReceiversController {
    private final CustomsSuspensionsReceiverService service;

    @GetMapping("/search/{name}")
    public BaseResponse<List<CustomsSuspensionsReceiverDto>> search(@PathVariable @NotEmpty @Size(min = 3) String name) {
        var receivers = service.findByName(name);
        return BaseResponse.<List<CustomsSuspensionsReceiverDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(receivers)
                .build();
    }
}
