package com.ipms.activity.service.impl;

import com.ipms.activity.dto.SuspendedProductDto;
import com.ipms.activity.dto.SuspendedProductPageDto;
import com.ipms.activity.exception.SuspendedProductNotFoundException;
import com.ipms.activity.mapper.SuspendedProductMapper;
import com.ipms.activity.model.SuspendedProduct;
import com.ipms.activity.repository.SuspendedProductRepository;
import com.ipms.activity.service.SuspendedProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SuspendedProductServiceImpl implements SuspendedProductService {
    private final SuspendedProductRepository repository;
    private final SuspendedProductMapper mapper;

    @Override
    public SuspendedProductDto save(SuspendedProductDto suspendedProductDto) {
        var mapped = mapper.toSuspendedProduct(suspendedProductDto);
        mapped.setType(suspendedProductDto.getType());
        var saved = repository.save(mapped);
        return mapper.toSuspendedProductDto(saved);
    }

    @Override
    public SuspendedProductPageDto getAllByActivity(Long activityId, int page, int size) {
        var productsPage = repository.findAllByActivityId(activityId, PageRequest.of(page, size));
        return SuspendedProductPageDto.builder()
                .suspendedProductDtos(productsPage.getContent()
                        .stream()
                        .map(mapper::toSuspendedProductDto)
                        .toList())
                .totalPages(productsPage.getTotalPages())
                .totalElements(productsPage.getTotalElements())
                .build();
    }

    @Override
    public List<SuspendedProductDto> getAllByActivity(Long activityId) {
        return repository.findAllByActivityId(activityId)
                .stream()
                .map(mapper::toSuspendedProductDto).toList();
    }

    @Override
    public void delete(Long id) {
        SuspendedProduct suspendedProduct = getSuspendedProductById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(suspendedProduct);
    }

    @Override
    public SuspendedProductDto getById(Long id) {
        var suspendedProduct = getSuspendedProductById(id);
        return mapper.toSuspendedProductDto(suspendedProduct);
    }

    private SuspendedProduct getSuspendedProductById(Long id) {
        return repository.findById(id).orElseThrow(SuspendedProductNotFoundException::new);
    }
}
