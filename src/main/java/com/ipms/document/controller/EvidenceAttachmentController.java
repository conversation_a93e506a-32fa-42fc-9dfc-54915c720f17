package com.ipms.document.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.document.dto.EvidenceAttachmentDto;
import com.ipms.document.service.EvidenceAttachmentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RequiredArgsConstructor
@RestController
@RequestMapping("/evidence-attachment")
@Tag(name = "evidence-attachment", description = "This endpoint contains evidence-attachment APIs ")
public class EvidenceAttachmentController {

    private final EvidenceAttachmentService service;

    @PostMapping("/")
    public BaseResponse<EvidenceAttachmentDto> save(@Valid @RequestBody EvidenceAttachmentDto attachmentDto) {
        var dto = service.save(attachmentDto);
        return BaseResponse.<EvidenceAttachmentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
