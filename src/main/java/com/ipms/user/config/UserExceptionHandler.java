package com.ipms.user.config;

import com.ipms.core.exception.BaseException;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.config.GlobalExceptionHandler;
import com.ipms.core.i18n.service.II18nMessageService;
import com.ipms.user.exception.*;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Locale;


@RestControllerAdvice
public class UserExceptionHandler extends GlobalExceptionHandler {
    public UserExceptionHandler(II18nMessageService ii18nMessageService) {
        super(ii18nMessageService);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({
            AuthenticationFailedException.class,
            InvalidCredentialsException.class,
            InvalidRefreshTokenException.class,
            InvalidAccessTokenException.class,
            UserAnyRoleException.class,
            UserRequiredActionException.class
    })
    public BaseResponse<Object> handleUserExceptions(BaseException exception, Locale locale) {
        return buildResponseCode(exception, locale);
    }
}
