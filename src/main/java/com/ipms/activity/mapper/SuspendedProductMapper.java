package com.ipms.activity.mapper;

import com.ipms.activity.dto.SuspendedProductDto;
import com.ipms.activity.model.SuspendedProduct;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface SuspendedProductMapper {

    @Mapping(target = "type", ignore = true)
    SuspendedProduct toSuspendedProduct(SuspendedProductDto suspendedProductDto);
    SuspendedProductDto toSuspendedProductDto(SuspendedProduct suspendedProduct);
}
