package com.ipms.integration.client;

import com.ipms.integration.dto.PriceRequestParam;
import com.ipms.integration.dto.PriceResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(value = "jde-price-client", url = "${jde.price-url}")
public interface JdePriceClient {

    @GetMapping("/Price")
    PriceResponse fetchPrices(@SpringQueryMap PriceRequestParam invoiceRequestParam);
}
