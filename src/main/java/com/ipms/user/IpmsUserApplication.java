package com.ipms.user;

import com.ipms.core.common.constants.Constants;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication(scanBasePackages = Constants.BASE_PACKAGE)
@EnableFeignClients(basePackages = Constants.BASE_PACKAGE)
public class IpmsUserApplication {

    public static void main(String[] args) {
        SpringApplication.run(IpmsUserApplication.class, args);
    }
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
