package com.ipms.billing.validator;

import com.ipms.billing.dto.BillingAccountDto;
import com.ipms.billing.exception.BillingAccountValidationException;
import com.ipms.billing.model.BillingAccount;
import com.ipms.billing.repository.BillingAccountRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillingAccountValidatorTest {
    @Mock
    private BillingAccountRepository repository;

    @Test
    @DisplayName("Given billing account with existing account number and issuer, when validate, then throw exception")
    void givenBillingAccountWithExistingAccountNumberAndIssuer_whenValidate_thenThrowException() {
        // Arrange
        BillingAccountValidator validator = new BillingAccountValidator(repository);
        BillingAccountDto dto = new BillingAccountDto();
        dto.setAccountNo("123");
        dto.setIssuerId(1L);
        when(repository.findByAccountNoAndIssuerId(dto.getAccountNo(), dto.getIssuerId()))
                .thenReturn(Optional.of(new BillingAccount()));

        // Act and Assert
        assertThrows(BillingAccountValidationException.class, () -> validator.validate(dto));
    }

    @Test
    @DisplayName("Given billing account with not existing account number and issuer, when validate, then do nothing")
    void givenBillingAccountWithNotExistingAccountNumberAndIssuer_whenValidate_thenDoNothing() {
        // Arrange
        BillingAccountValidator validator = new BillingAccountValidator(repository);
        BillingAccountDto dto = new BillingAccountDto();
        dto.setAccountNo("123");
        dto.setIssuerId(1L);
        when(repository.findByAccountNoAndIssuerId(dto.getAccountNo(), dto.getIssuerId()))
                .thenReturn(Optional.empty());

        // Act and Assert
        assertDoesNotThrow(() -> validator.validate(dto));
    }

    @Test
    @DisplayName("Given id and billing account with existing account number and issuer and different id, " +
            "when validate, then throw exception")
    void givenIdAndBillingAccountWithExistingAccountNumberAndIssuerAndDifferentId_whenValidate_thenThrowException() {
        // Arrange
        BillingAccountValidator validator = new BillingAccountValidator(repository);
        BillingAccountDto dto = new BillingAccountDto();
        dto.setAccountNo("123");
        dto.setIssuerId(1L);
        when(repository.findByAccountNoAndIssuerId(dto.getAccountNo(), dto.getIssuerId()))
                .thenReturn(Optional.of(new BillingAccount().toBuilder().id(2L).build()));

        // Act and Assert
        assertThrows(BillingAccountValidationException.class, () -> validator.validate(1L, dto));
    }

    @Test
    @DisplayName("Given id and billing account with existing account number and issuer and same id, " +
            "when validate, then do nothing")
    void givenIdAndBillingAccountWithExistingAccountNumberAndIssuerAndSameId_whenValidate_thenDoNothing() {
        // Arrange
        BillingAccountValidator validator = new BillingAccountValidator(repository);
        BillingAccountDto dto = new BillingAccountDto();
        dto.setAccountNo("123");
        dto.setIssuerId(1L);
        when(repository.findByAccountNoAndIssuerId(dto.getAccountNo(), dto.getIssuerId()))
                .thenReturn(Optional.of(new BillingAccount().toBuilder().id(1L).build()));

        // Act and Assert
        assertDoesNotThrow(() -> validator.validate(1L, dto));
    }
}