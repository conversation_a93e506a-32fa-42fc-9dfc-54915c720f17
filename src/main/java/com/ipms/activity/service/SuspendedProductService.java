package com.ipms.activity.service;

import com.ipms.activity.dto.SuspendedProductDto;
import com.ipms.activity.dto.SuspendedProductPageDto;

import java.util.List;

public interface SuspendedProductService {
    
    SuspendedProductDto save(SuspendedProductDto suspendedProductDto);
    SuspendedProductPageDto getAllByActivity(Long activityId, int page, int size);
    List<SuspendedProductDto> getAllByActivity(Long activityId);
    void delete(Long id);
    SuspendedProductDto getById(Long id);
}
