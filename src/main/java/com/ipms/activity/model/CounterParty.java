package com.ipms.activity.model;


import com.ipms.activity.enums.CounterRightOwnerCardType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.Audited;

import javax.persistence.CascadeType;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.OneToOne;


@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@DiscriminatorValue(CounterRightOwnerCardType.Values.COUNTER_PARTY)
@Audited
public class CounterParty extends CounterRightOwnerCard {

    @OneToOne(mappedBy = "counterParty", cascade = CascadeType.ALL)
    private Suspect suspect;
}
