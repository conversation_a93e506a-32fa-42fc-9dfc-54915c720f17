package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.LocalAttorney;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface LocalAttorneyRepository extends JpaRepository<LocalAttorney, Long>, JpaSpecificationExecutor<LocalAttorney> {
    List<LocalAttorney> findByIdIn(List<Long> ids);
}
