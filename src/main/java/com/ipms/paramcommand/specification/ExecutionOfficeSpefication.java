package com.ipms.paramcommand.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.paramcommand.model.ExecutionOffice;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class ExecutionOfficeSpefication extends AbstractSpecification<ExecutionOffice> {
    private final List<String> cities;
    private final List<String> names;

    @Override
    public Predicate toPredicate(Root<ExecutionOffice> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Optional.ofNullable(cities)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("city").in(value))));
        Optional.ofNullable(names)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("name").in(value))));
        return predicate;
    }
}
