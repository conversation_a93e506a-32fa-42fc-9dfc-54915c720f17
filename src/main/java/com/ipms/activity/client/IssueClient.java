package com.ipms.activity.client;

import com.ipms.activity.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "ipms-issue")
public interface IssueClient {

    @PostMapping("/api/issue/manual")
    IssueResponse save(@RequestBody IssueCreateDto dto);

    @GetMapping("/api/issue/issues")
    IssueListResponse getAll(@SpringQueryMap IssueFilterRequest filterRequest);

    @PutMapping("/api/issue/assign-to/{id}")
    void assign(@PathVariable Long id, @RequestBody AssignmentRequest request);
}
