package com.ipms.document.specification;

import com.ipms.document.enums.SampleCollectionPlace;
import com.ipms.document.enums.SampleLocation;
import com.ipms.document.enums.SampleType;
import com.ipms.document.model.Sample;
import com.ipms.document.repository.SampleRepository;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-document/application.yml")
public class SampleSpecificationIntegrationTest {

    @Autowired
    private SampleRepository repository;

    @Before
    public void givenEvidence() {
        var sample1 = Sample.builder()
                .id(1L)
                .firms(new ArrayList<>(Arrays.asList(1L, 2L)))
                .name("test-name")
                .description("test-description")
                .sampleType(SampleType.TEXTILE)
                .collectionPlace(SampleCollectionPlace.CLIENT)
                .quantity(3L)
                .sampleLocation(SampleLocation.CLIENT)
                .associate("test-associate")
                .deliveryDate(LocalDateTime.now())
                .shelfInfo("test-shelfInfo")
                .note("test-note")
                .arrivalDate(LocalDate.now())
                .receiptInvoiceNo(5L)
                .original(Boolean.TRUE)
                .build();
        repository.save(sample1);
    }

    @After
    public void deleteAll() {
        repository.deleteAll();
    }

    @Test
    public void givenSortField_whenFindAll_thenReturnSortedSampleList() {
        Page<Sample> samplePage = repository.findAll(SampleSpecification.builder()
                .createdBy("system")
                .updatedBy("system")
                .ids(List.of(1L, 2L))
                .firmId(1L)
                .build(), PageRequest.of(0, 10));
        assertThat(samplePage.getContent())
                .isSortedAccordingTo(Comparator.comparing(Sample::getId).reversed());
    }
}
