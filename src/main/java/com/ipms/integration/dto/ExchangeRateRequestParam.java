package com.ipms.integration.dto;

import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@Data
@Builder
public class ExchangeRateRequestParam {
    private String mainCompany;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate currencyDate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate updatedDate;
}
