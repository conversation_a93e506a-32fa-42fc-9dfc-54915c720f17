package com.ipms.activity.service;

import com.ipms.activity.dto.ActivityDto;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.CustomsSuspension;

public interface CustomsSuspensionService {
    CustomsSuspension setCustomSuspensionToUpdateActivity(ActivityDto dto, Activity activity);


    CustomsSuspension setCustomSuspensionToSaveActivity(ActivityDto dto, Activity activity);
}
