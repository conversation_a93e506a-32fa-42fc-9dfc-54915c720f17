server:
  port: 8062
  servlet:
    context-path: /api/billing
  max-http-header-size: 100KB

app:
  allowed-origins: >
    *
  allowed-headers: >
    *
  allowed-methods: >
    GET,
    POST,
    PUT,
    PATCH,
    DELETE,
    OPTIONS,
    HEAD

spring:
  application:
    name: ipms-billing

springdoc:
  api-docs:
    path: /docs
  swagger-ui:
    path: /docs/swagger-ui
    tagsSorter: alpha
    operationsSorter: alpha

management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health,info,metrics"
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessState:
      enabled: true
    readinessState:
      enabled: true

param:
  default-currency: DEFAULT_CURRENCY
  auto-billing-amount: AUTO_BILLING_AMOUNT