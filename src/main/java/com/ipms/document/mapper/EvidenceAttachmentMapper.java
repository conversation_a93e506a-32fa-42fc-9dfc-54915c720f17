package com.ipms.document.mapper;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.document.dto.EvidenceAttachmentDto;
import com.ipms.document.model.EvidenceAttachment;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface EvidenceAttachmentMapper {
    EvidenceAttachment toEvidenceAttachment(EvidenceAttachmentDto dto);
    EvidenceAttachmentDto toEvidenceAttachmentDto(EvidenceAttachment attachment);
    StorageFile toStorageFile(EvidenceAttachmentDto dto);
}
