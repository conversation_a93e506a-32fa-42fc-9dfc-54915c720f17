package com.ipms.activity.converter;

import com.ipms.activity.enums.GuaranteeType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Objects;

@Converter(autoApply = true)
public class GuaranteeTypeConverter implements AttributeConverter<GuaranteeType, String> {
    @Override
    public String convertToDatabaseColumn(GuaranteeType guaranteeType) {
        return Objects.isNull(guaranteeType) ? null : guaranteeType.getValue();
    }

    @Override
    public GuaranteeType convertToEntityAttribute(String s) {
        return Objects.isNull(s) || s.isEmpty() ? null : GuaranteeType.getEnum(s);
    }
}
