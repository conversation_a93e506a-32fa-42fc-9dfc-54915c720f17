package com.ipms.integration.dto;

import lombok.*;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
public class BillingOrderDto {
    private Long id;
    private Long version;
    private String orderNumber;
    private LocalDate orderDate;
    private String businessUnit;
    private Long issuerId;
    private List<BillingOrderDetailDto> orderDetails;
    private String description1;
    private String description2;
    private String description3;
    private String billingAccountNo;
    private String externalOrderNumber;
}
