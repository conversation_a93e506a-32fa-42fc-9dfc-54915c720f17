package com.ipms.activity.service;

import com.ipms.activity.client.BillingClient;
import com.ipms.activity.dto.TimesheetDto;
import com.ipms.activity.dto.TimesheetResponse;
import com.ipms.activity.model.TSICPackage;
import com.ipms.activity.repository.TSICPackageRepository;
import com.ipms.activity.service.impl.TSICOvertimeCalculationServiceImpl;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class TSICOvertimeCalculationServiceTest {

    @Mock
    private BillingClient billingClient;

    @Mock
    private TSICPackageRepository repository;

    @InjectMocks
    private TSICOvertimeCalculationServiceImpl service;

    private TSICPackage tsicPackage1;
    private TSICPackage tsicPackage2;
    private TimesheetResponse timesheetResponse;
    private List<TSICPackage> tsicPackages;

    @Before
    public void setup() {
        tsicPackage1 = TSICPackage.builder()
                .id(1L)
                .startDate(LocalDate.of(2023, 1, 1))
                .endDate(LocalDate.of(2023, 3, 31))
                .includedCharge(BigDecimal.valueOf(1))
                .fixedFee(true)
                .build();

        tsicPackage2 = TSICPackage.builder()
                .id(2L)
                .startDate(LocalDate.of(2023, 4, 1))
                .endDate(null)
                .includedCharge(BigDecimal.valueOf(2))
                .fixedFee(true)
                .build();

        tsicPackages = List.of(tsicPackage1, tsicPackage2);

        var timeSheets = Arrays.asList(
                // Timesheets for package 1
                TimesheetDto.builder()
                        .id(1L)
                        .activityId(100L)
                        .date(LocalDate.of(2023, 1, 15))
                        .billableTime(BigDecimal.valueOf(10))
                        .includedInCharge(true)
                        .build(),
                TimesheetDto.builder()
                        .id(2L)
                        .activityId(100L)
                        .date(LocalDate.of(2023, 2, 15))
                        .billableTime(BigDecimal.valueOf(15))
                        .includedInCharge(true)
                        .build(),
                TimesheetDto.builder()
                        .id(3L)
                        .activityId(100L)
                        .date(LocalDate.of(2023, 3, 15))
                        .billableTime(BigDecimal.valueOf(10))
                        .includedInCharge(false)
                        .expenseDate(LocalDate.of(2023, 3, 20))
                        .build(),

                // Timesheets for package 2
                TimesheetDto.builder()
                        .id(4L)
                        .activityId(100L)
                        .date(LocalDate.of(2023, 4, 15))
                        .billableTime(BigDecimal.valueOf(60))
                        .includedInCharge(true)
                        .build(),
                TimesheetDto.builder()
                        .id(5L)
                        .activityId(100L)
                        .date(LocalDate.of(2023, 5, 15))
                        .billableTime(BigDecimal.valueOf(70))
                        .includedInCharge(true)
                        .build(),
                TimesheetDto.builder()
                        .id(6L)
                        .activityId(100L)
                        .date(LocalDate.of(2023, 5, 20))
                        .billableTime(BigDecimal.valueOf(5))
                        .includedInCharge(null)
                        .expenseDate(LocalDate.of(2023, 5, 25))
                        .build()
        );

        timesheetResponse = TimesheetResponse.builder()
                .payload(timeSheets)
                .build();
    }

    @Test
    public void whenFixedFeeIsFalse_thenOverTimeShouldBeNull() {
        Long activityId = 1L;

        tsicPackage1.setFixedFee(false);

        when(repository.findAllByActivityId(activityId)).thenReturn(List.of(tsicPackage1));
        when(billingClient.getAll(activityId)).thenReturn(timesheetResponse);

        service.updateOvertimeForAllActivePackages(activityId);

        ArgumentCaptor<TSICPackage> packageCaptor = ArgumentCaptor.forClass(TSICPackage.class);
        verify(repository).save(packageCaptor.capture());

        TSICPackage savedPackage = packageCaptor.getValue();
        Assertions.assertThat(savedPackage.getOverTime()).isNull();
    }

    @Test
    public void testUpdateOvertimeForAllActivePackages() {
        Long activityId = 100L;
        
        when(repository.findAllByActivityId(activityId)).thenReturn(tsicPackages);
        when(billingClient.getAll(activityId)).thenReturn(timesheetResponse);
        
        service.updateOvertimeForAllActivePackages(activityId);
        
        verify(billingClient).getAll(activityId);
        verify(repository, times(2)).save(any(TSICPackage.class));
        Assertions.assertThat(tsicPackage1.getOverTime()).isFalse();
        Assertions.assertThat(tsicPackage2.getOverTime()).isTrue();
    }

    @Test
    public void testUpdateOvertimeForAllActivePackages_WithNonFixedFeePackage() {
        Long activityId = 100L;
        
        tsicPackage1.setFixedFee(false);

        when(repository.findAllByActivityId(activityId)).thenReturn(tsicPackages);
        when(billingClient.getAll(activityId)).thenReturn(timesheetResponse);
        
        service.updateOvertimeForAllActivePackages(activityId);
        
        verify(repository, times(2)).save(any(TSICPackage.class));
        Assertions.assertThat(tsicPackage1.getOverTime()).isNull();
        Assertions.assertThat(tsicPackage2.getOverTime()).isTrue();
    }

    @Test
    public void testUpdateOvertimeForAllActivePackages_WithOvertime() {
        Long activityId = 100L;
        
        tsicPackage2.setIncludedCharge(BigDecimal.valueOf(3));

        when(repository.findAllByActivityId(activityId)).thenReturn(tsicPackages);
        when(billingClient.getAll(activityId)).thenReturn(timesheetResponse);
        
        service.updateOvertimeForAllActivePackages(activityId);
        
        verify(billingClient).getAll(activityId);
        verify(repository, times(2)).save(any(TSICPackage.class));
        Assertions.assertThat(tsicPackage1.getOverTime()).isFalse();
        Assertions.assertThat(tsicPackage2.getOverTime()).isFalse();
    }

    @Test
    public void testUpdateOvertimeForAllActivePackages_WithEmptyTimesheets() {
        Long activityId = 100L;
        
        TimesheetResponse emptyResponse = TimesheetResponse.builder()
                .payload(List.of())
                .build();

        when(repository.findAllByActivityId(activityId)).thenReturn(tsicPackages);
        when(billingClient.getAll(activityId)).thenReturn(emptyResponse);
        
        service.updateOvertimeForAllActivePackages(activityId);
        
        verify(billingClient).getAll(activityId);
        verify(repository, times(2)).save(any(TSICPackage.class));
        Assertions.assertThat(tsicPackage1.getOverTime()).isFalse();
        Assertions.assertThat(tsicPackage2.getOverTime()).isFalse();
    }

    @Test
    public void testUpdateOvertimeForAllActivePackages_WithAllTimesheetsOutsidePackageDates() {
        Long activityId = 100L;

        List<TimesheetDto> outsideTimeSheets = List.of(
            TimesheetDto.builder()
                .id(7L)
                .activityId(100L)
                .date(LocalDate.of(2021, 12, 15))  // Before package 1
                .billableTime(BigDecimal.valueOf(10))
                .includedInCharge(true)
                .build(),
            TimesheetDto.builder()
                .id(8L)
                .activityId(100L)
                .date(LocalDate.of(2022, 1, 15))   // After both packages (assuming package 2 has an end date)
                .billableTime(BigDecimal.valueOf(15))
                .includedInCharge(true)
                .build()
        );
        
        TimesheetResponse outsideResponse = TimesheetResponse.builder()
                .payload(outsideTimeSheets)
                .build();

        when(repository.findAllByActivityId(activityId)).thenReturn(tsicPackages);
        when(billingClient.getAll(activityId)).thenReturn(outsideResponse);
        
        service.updateOvertimeForAllActivePackages(activityId);
        
        verify(billingClient).getAll(activityId);
        verify(repository, times(2)).save(any(TSICPackage.class));
        Assertions.assertThat(tsicPackage1.getOverTime()).isFalse();
        Assertions.assertThat(tsicPackage2.getOverTime()).isFalse();
    }
}