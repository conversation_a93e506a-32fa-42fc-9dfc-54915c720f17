package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;
import lombok.Getter;

@Getter
public enum ActivityResponseCode implements Code {

    ACTIVITY_NOT_FOUND(1000, "error.code.activity.not_found"),
    CD_LETTER_NULL(1001, "error.code.activity.cd_letter_null"),
    ATTACHMENT_NOT_FOUND(1002, "error.code.attachment.not_found"),
    CUSTOMS_TRAINING_DATE_NOT_FOUND(1003, "error.code.activity.customsTrainingDate.not_found"),
    PI_NOT_FOUND(1004, "error.code.activity.preliminary-injunction.not_found"),
    PI_DETAIL_NOT_FOUND(1005, "error.code.activity.preliminary-injunction.detail_not_found"),
    BILLING_CANNOT_CREATE(1006, "error.code.activity.billing.cannot_create"),
    BILLING_CANCELLATION_TIMESHEET_CANNOT_UPDATE(1006, "error.code.activity.billing.timesheet_cannot_update"),
    BILLING_ALREADY_STARTED(1006, "error.code.activity.billing.already_started"),
    BILLING_ISSUE_CAN_NOT_BE_CANCELED(1007, "error.code.issue.billing_issue_can_not_be_canceled"),
    BILLING_CANNOT_APPROVE(1008, "error.code.activity.billing.cannot_approve"),
    NO_EXPENSE_RECORD_TO_BE_INVOICED(1010, "error.code.activity.billing.no_expense_record_to_be_invoiced"),
    ACTIVITY_CANNOT_BE_UPDATED(1009, "error.code.activity.cannot_be_updated");

    private final Integer code;
    private final String messageKey;

    ActivityResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }
}
