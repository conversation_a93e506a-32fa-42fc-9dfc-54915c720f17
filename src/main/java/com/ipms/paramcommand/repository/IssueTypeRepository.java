package com.ipms.paramcommand.repository;

import com.ipms.paramcommand.model.IssueType;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IssueTypeRepository extends PagingAndSortingRepository<IssueType, Long>, JpaSpecificationExecutor<IssueType> {
    Optional<IssueType> findIssueTypeByKey(String key);
}
