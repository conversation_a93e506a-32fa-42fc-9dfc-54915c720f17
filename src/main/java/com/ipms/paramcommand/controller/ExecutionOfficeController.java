package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.ExecutionOfficeDto;
import com.ipms.paramcommand.dto.ExecutionOfficeFilterRequest;
import com.ipms.paramcommand.dto.ExecutionOfficePageDto;
import com.ipms.paramcommand.service.ExecutionOfficeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("execution-office")
@Tag(name = "executionOffice", description = "This endpoints contains execution office APIs")
public class ExecutionOfficeController {
    private final ExecutionOfficeService service;

    @PostMapping
    public BaseResponse<ExecutionOfficeDto> save(@Valid @RequestBody ExecutionOfficeDto dto) {
        return BaseResponse.<ExecutionOfficeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<ExecutionOfficeDto> getById(@PathVariable Long id) {
        return BaseResponse.<ExecutionOfficeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

    @GetMapping
    public BaseResponse<List<ExecutionOfficeDto>> getAll() {
        return BaseResponse.<List<ExecutionOfficeDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAll())
                .build();
    }

    @GetMapping("/by-city/{city}")
    public BaseResponse<List<ExecutionOfficeDto>> getAllByCity(@PathVariable String city) {
        return BaseResponse.<List<ExecutionOfficeDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByCity(city))
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<ExecutionOfficePageDto> getAllByFilter(ExecutionOfficeFilterRequest filterRequest,
                                                                     @PathVariable int page,
                                                                     @PathVariable int size) {
        return BaseResponse.<ExecutionOfficePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByFilter(filterRequest, page, size))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
