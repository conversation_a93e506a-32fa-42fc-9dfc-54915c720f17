server:
  port: 8761
  servlet:
    context-path: /registry
eureka:
  client:
    register-with-eureka: false
    fetch-registry: false
  server:
    renewal-percent-threshold: 0.49
logging:
  level:
    com.netflix.eureka: OFF
    com.netflix.discovery: OFF

management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health,info,metrics"
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessState:
      enabled: true
    readinessState:
      enabled: true