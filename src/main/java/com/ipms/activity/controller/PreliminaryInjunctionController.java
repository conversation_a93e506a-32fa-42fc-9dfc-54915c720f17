package com.ipms.activity.controller;

import com.ipms.activity.dto.PreliminaryInjunctionDetailDto;
import com.ipms.activity.service.PreliminaryInjunctionDetailService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("preliminary-injunction")
@Tag(name = "preliminary-injunction", description = "This endpoint contains preliminary injunction APIs ")
public class PreliminaryInjunctionController {

    private final PreliminaryInjunctionDetailService service;

    @GetMapping("/{id}")
    public BaseResponse<PreliminaryInjunctionDetailDto> getById(@PathVariable Long id) {
        return BaseResponse.<PreliminaryInjunctionDetailDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

}
