package com.ipms.document.consumer;

import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.document.dto.DocumentEvent;
import com.ipms.document.dto.MailEvent;
import com.ipms.document.service.DocumentService;
import com.ipms.document.service.DraftDocumentService;
import lombok.RequiredArgsConstructor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class DocumentConsumer {
    private final DocumentService service;
    private final DraftDocumentService draftDocumentService;

    @KafkaListener(topics = "${kafka.document-updated-topic}")
    public void consumeUpdatedTopic(String eventStr) {
        service.processUpdatedEvent(ObjectUtils.fromJson(eventStr, DocumentEvent.class));
    }

    @KafkaListener(topics = "${kafka.email-topic}")
    public void consumeEmailTopic(String eventStr) {
        MailEvent mailEvent = ObjectUtils.fromJson(eventStr, MailEvent.class);
        if (isEventForDocument(mailEvent)) {
            draftDocumentService.processMailEvent(mailEvent);
        }
    }

    private boolean isEventForDocument(MailEvent mailEvent) {
        return "L_D".equals(mailEvent.getMailFolder());
    }
}
