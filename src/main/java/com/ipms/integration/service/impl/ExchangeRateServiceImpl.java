package com.ipms.integration.service.impl;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.integration.client.JdeClient;
import com.ipms.integration.dto.ExchangeRateDto;
import com.ipms.integration.dto.ExchangeRateEvent;
import com.ipms.integration.dto.ExchangeRateRequestParam;
import com.ipms.integration.dto.ExchangeRateResponse;
import com.ipms.integration.service.ExchangeRateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class ExchangeRateServiceImpl implements ExchangeRateService {
    private final JdeClient jdeClient;
    @Value("${kafka.exchange-rate-topic}")
    private String exchangeRateTopic;
    private final ProducerService producerService;
    @Value("${jde.main-company}")
    private String mainCompany;

    @Override
    public List<ExchangeRateDto> fetchExchangeRates(LocalDate updateDate) {
        var exchangeRateRequest = ExchangeRateRequestParam.builder()
                .updatedDate(updateDate)
                .mainCompany(mainCompany)
                .build();
        var exchangeRateResponse = jdeClient.fetchExchangeRates(exchangeRateRequest);
        log.info("Exchange rates are fetched successfully for update date: {}. response: {}",
                updateDate, exchangeRateResponse);
        if (!hasExchangeRate(exchangeRateResponse)) {
            return List.of();
        }
        sendExchangeRateEvent(exchangeRateResponse.getExchangeRates());
        return exchangeRateResponse.getExchangeRates();
    }

    @Override
    public List<ExchangeRateDto> fetchAllExchangeRates() {
        var exchangeRateRequest = ExchangeRateRequestParam.builder()
                .mainCompany(mainCompany)
                .build();
        var exchangeRateResponse = jdeClient.fetchExchangeRates(exchangeRateRequest);
        log.info("All exchange rates are fetched successfully. response: {}", exchangeRateResponse);
        if (!hasExchangeRate(exchangeRateResponse)) {
            return List.of();
        }
        sendExchangeRateEvent(exchangeRateResponse.getExchangeRates());
        return exchangeRateResponse.getExchangeRates();
    }

    private boolean hasExchangeRate(ExchangeRateResponse exchangeRateResponse) {
        return "0".equals(exchangeRateResponse.getResponseInformatin().getErrorCode());
    }

    private void sendExchangeRateEvent(List<ExchangeRateDto> exchangeRateDtos) {
        var exchangeRateEvent = ExchangeRateEvent.builder()
                .exchangeRateDtos(exchangeRateDtos)
                .build();
        producerService.send(exchangeRateTopic, ObjectUtils.toJson(exchangeRateEvent));
    }
}
