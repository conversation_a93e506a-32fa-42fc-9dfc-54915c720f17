package com.ipms.integration.service.impl;

import com.ipms.integration.client.JdeClient;
import com.ipms.integration.dto.CustomerInformation;
import com.ipms.integration.dto.CustomerInformationResponse;
import com.ipms.integration.service.CustomerInformationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerInformationServiceTest {

    @Mock
    private JdeClient jdeClient;

    private CustomerInformationService cut;

    @BeforeEach
    void setUp() {
        cut = new CustomerInformationServiceImpl(jdeClient);
    }

    @Test
    void shouldNotThrowExceptionWhenDiscountRateIsNull() {
        var customerInformation1 = CustomerInformation.builder()
                .build();
        var customerInformation2 = CustomerInformation.builder()
                .build();
        var customerInfoResponse = CustomerInformationResponse.builder()
                .customerInformations(List.of(customerInformation1, customerInformation2))
                .build();
        when(jdeClient.getCustomerInformation(any())).thenReturn(customerInfoResponse);

        assertThatCode(() -> cut.getCustomerInformation("123")).doesNotThrowAnyException();
    }

    @Test
    void shouldDivideWhenDiscountRateIsNotNull() {
        var customerInformation1 = CustomerInformation.builder()
                .discountRate(new BigDecimal("100000"))
                .build();
        var customerInformation2 = CustomerInformation.builder()
                .discountRate(new BigDecimal("100000"))
                .build();
        var customerInfoResponse = CustomerInformationResponse.builder()
                .customerInformations(List.of(customerInformation1, customerInformation2))
                .build();
        when(jdeClient.getCustomerInformation(any())).thenReturn(customerInfoResponse);

        var customerInformation = cut.getCustomerInformation("123");

        assertThat(customerInformation.getDiscountRate()).isEqualTo(new BigDecimal("10"));
    }
}