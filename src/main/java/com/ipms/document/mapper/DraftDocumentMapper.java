package com.ipms.document.mapper;


import com.ipms.document.dto.DraftDocumentDto;
import com.ipms.document.model.DraftDocument;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface DraftDocumentMapper {
    DraftDocumentDto toDto(DraftDocument draftDocument);
    DraftDocument toDraftDocumentFromDto(DraftDocumentDto dto, @MappingTarget DraftDocument draftDocument);
}
