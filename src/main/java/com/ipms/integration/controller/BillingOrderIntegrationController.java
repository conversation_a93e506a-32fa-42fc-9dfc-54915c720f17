package com.ipms.integration.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.integration.service.BillingOrderIntegrationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RequiredArgsConstructor
@RestController
@RequestMapping("/billing-order-integration")
@Tag(name = "exchange rate", description = "This endpoint contains exchange rate APIs ")
public class BillingOrderIntegrationController {
    private final BillingOrderIntegrationService service;

    @PostMapping("/send-billing-order")
    public BaseResponse<String> sendBillingOrder() {
        service.sendBillingOrder();
        return BaseResponse.<String>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @PostMapping("/update-external-order-number")
    public BaseResponse<String> updateExternalOrderNumber() {
        service.updateExternalOrderNumber();
        return BaseResponse.<String>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @PutMapping("/fetch-invoices/{updateDateFrom}/{updateDateTo}/{mainCompany}")
    public BaseResponse<String> fetchInvoices(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate updateDateFrom,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate updateDateTo,
            @PathVariable String mainCompany) {
        service.fetchInvoices(updateDateFrom, updateDateTo, mainCompany);
        return BaseResponse.<String>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/billing-order/{issuerId}/{externalOrderNumber}/{billingAccountNo}/is-cancelled")
    public BaseResponse<Boolean> isOrderCancelled(@PathVariable Long issuerId,
                                                  @PathVariable String externalOrderNumber,
                                                  @PathVariable String billingAccountNo) {
        var isCancelled = service.isOrderCancelled(issuerId, externalOrderNumber, billingAccountNo);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(isCancelled)
                .build();
    }
}
