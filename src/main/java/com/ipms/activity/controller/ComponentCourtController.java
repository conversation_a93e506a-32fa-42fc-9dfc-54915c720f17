package com.ipms.activity.controller;

import com.ipms.activity.dto.ComponentCourtDto;
import com.ipms.activity.dto.ComponentCourtFilterRequest;
import com.ipms.activity.service.ComponentCourtService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@RequiredArgsConstructor
@RestController
@RequestMapping("/component-court")
public class ComponentCourtController {
    private final ComponentCourtService service;

    @PostMapping
    public BaseResponse<ComponentCourtDto> save(@Valid @RequestBody ComponentCourtDto componentCourtDto) {
        var court = service.save(componentCourtDto);
        return BaseResponse.<ComponentCourtDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(court)
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<ComponentCourtDto> update(@Valid @RequestBody ComponentCourtDto componentCourtDto, @PathVariable Long id) {
        var court = service.update(componentCourtDto, id);
        return BaseResponse.<ComponentCourtDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(court)
                .build();
    }

    @GetMapping("/activity/{activityId}")
    public BaseResponse<List<ComponentCourtDto>> getByActivityId(@PathVariable Long activityId) {
        return BaseResponse.<List<ComponentCourtDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByActivityId(activityId))
                .build();
    }

    @GetMapping("/activities/{activityIds}")
    public BaseResponse<List<ComponentCourtDto>> getByActivityIds(@PathVariable List<Long> activityIds) {
        return BaseResponse.<List<ComponentCourtDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByActivityIds(activityIds))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/activities/get-all")
    public BaseResponse<List<ComponentCourtDto>> getAll(ComponentCourtFilterRequest filterRequest) {
        return BaseResponse.<List<ComponentCourtDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllFiltered(filterRequest))
                .build();
    }
}