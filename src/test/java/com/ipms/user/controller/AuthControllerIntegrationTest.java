package com.ipms.user.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.config.security.utils.SecurityUtils;
import com.ipms.user.dto.auth.AccessTokenRequest;
import com.ipms.user.dto.auth.AuthDto;
import com.ipms.user.dto.auth.LogoutRequest;
import com.ipms.user.dto.auth.RefreshTokenRequest;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;
import com.ipms.user.exception.InvalidAccessTokenException;
import com.ipms.user.service.AuthService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;
import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-user/application.yml")
public class AuthControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private AuthService service;

    ObjectMapper objectMapper = new ObjectMapper();

    private static final String ROLE_LIST = "/auth/role-list";

    @Test
    public void whenGetAccessToken_thenReturnResponseEntity() throws Exception {
        Mockito.when(service.getAccessToken(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(AuthDto.builder().accessToken("access-token").build());
        var accessTokenRequest = new AccessTokenRequest();
        accessTokenRequest.setPassword("password");
        accessTokenRequest.setUsername("username");
        accessTokenRequest.setOtp("otp");
        mvc.perform(post("/auth/access-token/123")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(accessTokenRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetRefreshToken_thenReturnResponseEntity() throws Exception {
        Mockito.when(service.getRefreshToken(Mockito.anyString(),Mockito.anyString()))
                .thenReturn(AuthDto.builder().accessToken("refresh-token").build());

        var refreshTokenRequest = new RefreshTokenRequest();
        refreshTokenRequest.setRefreshToken("refresh");
        mvc.perform(post("/auth/refresh-token/123")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(refreshTokenRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetLogout_thenReturnHttpStatusOk() throws Exception {
        Mockito.doNothing().when(service).logout(Mockito.anyString(),Mockito.anyString(),Mockito.anyString());
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken).thenReturn(Optional.of("ok"));
            var logoutRequest = new LogoutRequest();
            logoutRequest.setRefreshToken("refresh");
            mvc.perform(post("/auth/logout/123")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(logoutRequest))
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        }
    }

    @Test
    public void whenGetAccessToken_thenReturnHttpStatusBadRequest() throws Exception {
        var ex = new InvalidAccessTokenException("Access token is not found.");
        Mockito.doThrow(ex).when(service)
                .getAccessToken(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString());

        var accessTokenRequest = new AccessTokenRequest();
        accessTokenRequest.setPassword("password");
        accessTokenRequest.setUsername("username");
        accessTokenRequest.setOtp("otp");
        mvc.perform(post("/auth/access-token/123")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(accessTokenRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void whenGetLogout_thenReturnHttpStatusBadRequest() throws Exception {
        Mockito.doNothing().when(service).logout(Mockito.anyString(),Mockito.anyString(),Mockito.anyString());
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken).thenReturn(Optional.empty());
            var logoutRequest = new LogoutRequest();
            logoutRequest.setRefreshToken("refresh");
            mvc.perform(post("/auth/logout/123")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(logoutRequest))
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isBadRequest());
        }
    }

    @Test
    public void whenGetRoleList_thenReturnBaseResponse() throws Exception {
        var role = RoleDto
                .builder()
                .id("1")
                .build();
        Mockito.when(service.getRoleList(Mockito.anyString()))
                .thenReturn(List.of(role));
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken)
                    .thenReturn(Optional.of("ok"));
            var logoutRequest = new LogoutRequest();
            logoutRequest.setRefreshToken("refresh");
            mvc.perform(get(ROLE_LIST)
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        }
    }

    @Test
    public void whenGetRoleList_thenReturnHttpStatusBadRequest() throws Exception {
        var ex = new InvalidAccessTokenException("Access token is not found.");
        Mockito.doThrow(ex).when(service)
                .getAccessToken(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString());

        var accessTokenRequest = new AccessTokenRequest();
        accessTokenRequest.setPassword("password");
        accessTokenRequest.setUsername("username");
        accessTokenRequest.setOtp("otp");
        mvc.perform(get(ROLE_LIST)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(accessTokenRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void givenRole_whenGetUserListByRole_thenReturnBaseResponse() throws Exception {
        var userInfo = UserInfoDto
                .builder()
                .email("testMail")
                .lastName("testLastName")
                .firstName("testFirstName")
                .build();
        Mockito.when(service.getUserListByRole(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(List.of(userInfo));
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken)
                    .thenReturn(Optional.of("ok"));
            var logoutRequest = new LogoutRequest();
            logoutRequest.setRefreshToken("refresh");
            mvc.perform(get("/auth/user-list/roles/default-user")
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        }
    }

    @Test
    public void givenRole_whenGetUserListByRole_thenReturnHttpStatusBadRequest() throws Exception {
        var ex = new InvalidAccessTokenException("Access token is not found.");
        Mockito.doThrow(ex).when(service)
                .getAccessToken(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString());

        var accessTokenRequest = new AccessTokenRequest();
        accessTokenRequest.setPassword("password");
        accessTokenRequest.setUsername("username");
        accessTokenRequest.setOtp("otp");
        mvc.perform(get("/auth/user-list/roles/default-user")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(accessTokenRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void whenGetUserListWithRoles_thenReturnBaseResponse() throws Exception {
        var userInfo = UserInfoDto
                .builder()
                .email("testMail")
                .lastName("testLastName")
                .firstName("testFirstName")
                .build();
        Mockito.when(service.getUserListWithRole(Mockito.anyString()))
                .thenReturn(List.of(userInfo));
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken)
                    .thenReturn(Optional.of("ok"));
            var logoutRequest = new LogoutRequest();
            logoutRequest.setRefreshToken("refresh");
            mvc.perform(get("/auth/user-list")
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        }
    }

    @Test
    public void whenGetUserListWithRoles_thenReturnHttpStatusBadRequest() throws Exception {
        var ex = new InvalidAccessTokenException("Access token is not found.");
        Mockito.doThrow(ex).when(service)
                .getAccessToken(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString());

        var accessTokenRequest = new AccessTokenRequest();
        accessTokenRequest.setPassword("password");
        accessTokenRequest.setUsername("username");
        accessTokenRequest.setOtp("otp");
        mvc.perform(get("/auth/user-list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(accessTokenRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void whenGetUserByEmail_thenReturnBaseResponse() throws Exception {
        var userInfo = UserInfoDto
                .builder()
                .email("testMail")
                .lastName("testLastName")
                .firstName("testFirstName")
                .build();
        Mockito.when(service.getByEmail(Mockito.anyString(),Mockito.anyString()))
                .thenReturn(userInfo);
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken)
                    .thenReturn(Optional.of("ok"));

            mvc.perform(get("/auth/user-by-email/<EMAIL>")
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        }
    }

    @Test
    public void whenGetUserByUsername_thenReturnBaseResponse() throws Exception {
        var userInfo = UserInfoDto
                .builder()
                .email("testMail")
                .lastName("testLastName")
                .firstName("testFirstName")
                .build();
        Mockito.when(service.getByUsername(Mockito.anyString(),Mockito.anyString()))
                .thenReturn(userInfo);
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken)
                    .thenReturn(Optional.of("ok"));

            mvc.perform(get("/auth/user-by-username/test")
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        }
    }

    @Test
    public void whenGetUserRolesByEmail_thenReturnHttpStatusBadRequest() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken)
                    .thenReturn(Optional.empty());

            mvc.perform(get("/auth/user-by-email/<EMAIL>")
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isBadRequest());
        }
    }

    @Test
    public void whenGetUserRolesByUsername_thenReturnHttpStatusBadRequest() throws Exception {
        try (MockedStatic<SecurityUtils> securityUtilsMock = Mockito.mockStatic(SecurityUtils.class)) {
            securityUtilsMock.when(SecurityUtils::getCurrentAccessToken)
                    .thenReturn(Optional.empty());

            mvc.perform(get("/auth/user-by-username/test")
                            .contentType(MediaType.APPLICATION_JSON)
                            .accept(MediaType.APPLICATION_JSON))
                    .andExpect(status().isBadRequest());
        }
    }
}
