package com.ipms.document.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.document.dto.AttachmentDto;
import com.ipms.document.service.AttachmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RequiredArgsConstructor
@RestController
@RequestMapping("/attachment")
public class AttachmentController {
    private final AttachmentService service;

    @PostMapping("/")
    public BaseResponse<AttachmentDto> save(@Valid @RequestBody AttachmentDto attachmentDto) {
        var dto = service.save(attachmentDto);
        return BaseResponse.<AttachmentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
