package com.ipms.user.service.keycloak.impl;

import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.core.exception.InternalServerException;
import com.ipms.user.dto.keycloak.KeycloakAuthRequestDto;
import com.ipms.user.dto.keycloak.KeycloakLogoutRequestDto;
import com.ipms.user.dto.keycloak.KeycloakTokenDto;
import com.ipms.user.dto.keycloak.RoleDto;
import com.ipms.user.dto.role.UserInfoDto;
import com.ipms.user.exception.InvalidCredentialsException;
import com.ipms.user.exception.InvalidRefreshTokenException;
import com.ipms.user.exception.UserRequiredActionException;
import com.ipms.user.exception.UserResponseCode;
import com.ipms.user.service.keycloak.KeycloakAuthService;
import com.ipms.user.service.keycloak.KeycloakClient;
import com.ipms.user.service.keycloak.KeycloakClientRestTemplate;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.OAuth2Constants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class KeycloakAuthServiceImpl implements KeycloakAuthService {

    private final KeycloakClient keycloakClient;
    private final KeycloakClientRestTemplate keycloakClientRestTemplate;

    @Value("${ipms.keycloak-web-client-secret}")
    private String clientSecret;

    @Value("${ipms.keycloak-master-client-secret}")
    private String clientMasterSecret;

    @Value("${ipms.keycloak-master-realm}")
    private String realm;


    private static final String CLIENT_ID_REQUIRED = "clientId cannot be null.";
    private static final String REALM_REQUIRED = "clientId cannot be null.";
    private static final String KEYCLOAK_CLIENT_EXCEPTION_MSG = "Keycloak client exception.";
    private static final String KEYCLOAK_AUTHENTICATION_ERROR_MSG = "An error has occurred while authenticating Keycloak.";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String KEYCLOAK_REQUIRED_ACTION_ERROR = "Account is not fully set up";

    @Override
    public KeycloakTokenDto getRefreshToken(String refreshToken, String realm, String clientId) {
        ObjectUtils.requireNonNull(refreshToken, "refreshToken cannot be null.");
        ObjectUtils.requireNonNull(realm, REALM_REQUIRED);
        ObjectUtils.requireNonNull(clientId, CLIENT_ID_REQUIRED);

        try {
            return grantRefreshToken(refreshToken, realm, clientId);
        } catch (FeignException e) {
            log.warn(KEYCLOAK_CLIENT_EXCEPTION_MSG, e);
            throw new InvalidRefreshTokenException("Invalid refresh token.");
        }
    }

    @Override
    public KeycloakTokenDto getAccessToken(String username, String password, String realm, String clientId, String otp) {
        ObjectUtils.requireNonNull(username, "username cannot be null.");
        ObjectUtils.requireNonNull(password, "password cannot be null.");
        ObjectUtils.requireNonNull(realm, REALM_REQUIRED);
        ObjectUtils.requireNonNull(clientId, CLIENT_ID_REQUIRED);

        try {
            return grantAccessToken(username, password, realm, clientId, otp);
        } catch (FeignException.FeignClientException e) {
            log.warn(KEYCLOAK_CLIENT_EXCEPTION_MSG, e);

            int statusCode = e.status();
            if (statusCode == HttpStatus.UNAUTHORIZED.value()) {
                throw new InvalidCredentialsException();
            } else if (statusCode == HttpStatus.BAD_REQUEST.value() && e.getMessage().contains(KEYCLOAK_REQUIRED_ACTION_ERROR)) {
                throw new UserRequiredActionException(UserResponseCode.OTP_REQUIRED_ACTION);
            }

            throw new InternalServerException(KEYCLOAK_AUTHENTICATION_ERROR_MSG);
        }
    }

    @Override
    public KeycloakTokenDto getAccessToken(String clientId) {

        try {
            return grantAccessToken(clientId);
        } catch (FeignException.FeignClientException e) {
            log.warn(KEYCLOAK_CLIENT_EXCEPTION_MSG, e);

            int statusCode = e.status();
            if (statusCode == HttpStatus.UNAUTHORIZED.value()) {
                throw new InvalidCredentialsException();
            }
            throw new InternalServerException(KEYCLOAK_AUTHENTICATION_ERROR_MSG);
        }
    }

    @Override
    public List<UserInfoDto> getUserList(String accessToken, String realm) {
        try {
            return keycloakClientRestTemplate.getUserList(realm, BEARER_PREFIX + accessToken);
        } catch (FeignException.FeignClientException e) {
            log.warn(KEYCLOAK_CLIENT_EXCEPTION_MSG, e);

            int statusCode = e.status();
            if (statusCode == HttpStatus.UNAUTHORIZED.value()) {
                throw new InvalidCredentialsException();
            }
            throw new InternalServerException(KEYCLOAK_AUTHENTICATION_ERROR_MSG);
        }
    }

    @Override
    public UserInfoDto filterExpenseCode(String accessToken, String realm, String username, List<String> expenseCodes) {
        try {
            return keycloakClientRestTemplate.filterExpenseCode(realm, BEARER_PREFIX + accessToken, username, expenseCodes);
        } catch (FeignException.FeignClientException e) {
            log.warn(KEYCLOAK_CLIENT_EXCEPTION_MSG, e);

            int statusCode = e.status();
            if (statusCode == HttpStatus.UNAUTHORIZED.value()) {
                throw new InvalidCredentialsException();
            }
            throw new InternalServerException(KEYCLOAK_AUTHENTICATION_ERROR_MSG);
        }
    }

    @Override
    public List<RoleDto> getRoleList(String realm, String clientId, String accessToken) {
        try {
            return keycloakClientRestTemplate.getRoleList(realm, clientId, BEARER_PREFIX + accessToken);
        } catch (FeignException.FeignClientException e) {
            log.warn(KEYCLOAK_CLIENT_EXCEPTION_MSG, e);

            int statusCode = e.status();
            if (statusCode == HttpStatus.UNAUTHORIZED.value()) {
                throw new InvalidCredentialsException();
            }
            throw new InternalServerException(KEYCLOAK_AUTHENTICATION_ERROR_MSG);
        }
    }

    @Override
    public List<UserInfoDto> getUserListByRole(String realm, String clientId, String role, String accessToken) {
        try {
            return keycloakClientRestTemplate.getUserListByRole(realm, clientId, role,BEARER_PREFIX + accessToken);
        } catch (FeignException.FeignClientException e) {
            log.warn(KEYCLOAK_CLIENT_EXCEPTION_MSG, e);

            int statusCode = e.status();
            if (statusCode == HttpStatus.UNAUTHORIZED.value()) {
                throw new InvalidCredentialsException();
            }
            throw new InternalServerException(KEYCLOAK_AUTHENTICATION_ERROR_MSG);
        }
    }

    @Override
    public void logout(String accessToken, String refreshToken, String realm, String clientId) {
        ObjectUtils.requireNonNull(accessToken, "accessToken cannot be null.");
        ObjectUtils.requireNonNull(refreshToken, "refreshToken cannot be null.");
        ObjectUtils.requireNonNull(realm, REALM_REQUIRED);
        ObjectUtils.requireNonNull(clientId, CLIENT_ID_REQUIRED);

        var keycloakLogoutDto = KeycloakLogoutRequestDto.builder()
                .clientId(clientId)
                .refreshToken(refreshToken)
                .clientSecret(clientSecret)
                .build();
        keycloakClientRestTemplate.logout(keycloakLogoutDto, realm, BEARER_PREFIX + accessToken);
    }

    @Override
    public KeycloakTokenDto getPermissionToken(String accessToken, String realm, String clientId) {
        ObjectUtils.requireNonNull(realm, REALM_REQUIRED);
        ObjectUtils.requireNonNull(clientId, CLIENT_ID_REQUIRED);

        try {
            return grantPermissionToken(accessToken, realm, clientId);
        } catch (FeignException.FeignClientException e) {
            log.warn(KEYCLOAK_CLIENT_EXCEPTION_MSG, e);

            int statusCode = e.status();
            if (statusCode == HttpStatus.UNAUTHORIZED.value()) {
                throw new InvalidCredentialsException();
            }

            throw new InternalServerException(KEYCLOAK_AUTHENTICATION_ERROR_MSG);
        }
    }

    private KeycloakTokenDto grantAccessToken(String username, String password, String realm, String clientId, String otp) {
        var keycloakRequestDto = KeycloakAuthRequestDto
                .builder()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .grantType(OAuth2Constants.PASSWORD)
                .username(username)
                .password(password)
                .totp(otp)
                .build();
        return keycloakClient.getToken(keycloakRequestDto, realm);
    }

    private KeycloakTokenDto grantAccessToken(String clientId) {
        var keycloakRequestDto = KeycloakAuthRequestDto
                .builder()
                .clientId(clientId)
                .clientSecret(clientMasterSecret)
                .grantType(OAuth2Constants.CLIENT_CREDENTIALS)
                .build();
        return keycloakClient.getToken(keycloakRequestDto, realm);
    }

    private KeycloakTokenDto grantRefreshToken(String refreshToken, String realm, String clientId) {
        var keycloakRequestDto = KeycloakAuthRequestDto
                .builder()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .grantType(OAuth2Constants.REFRESH_TOKEN)
                .refreshToken(refreshToken)
                .build();
        return keycloakClient.getToken(keycloakRequestDto, realm);
    }

    private KeycloakTokenDto grantPermissionToken(String accessToken, String realm, String clientId) {
        var keycloakRequestDto = KeycloakAuthRequestDto
                .builder()
                .clientId(clientId)
                .clientSecret(clientSecret)
                .grantType(OAuth2Constants.UMA_GRANT_TYPE)
                .audience(clientId)
                .build();
        return keycloakClientRestTemplate.getPermissionToken(keycloakRequestDto, realm, BEARER_PREFIX + accessToken);
    }
}
