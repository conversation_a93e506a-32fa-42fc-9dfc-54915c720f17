package com.ipms.activity.model;

import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.BillingStatus;
import com.ipms.activity.enums.RiskImpact;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "activity")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class ActivityRevisionProjection extends VersionedEntity {

    @Column
    private ActivityType type;

    @Column
    private ActivityStatus status;

    @Column
    private LocalDate instructionDate;

    @Column
    private Long firmId;

    @Column
    private Long matterId;

    @Column
    private BigDecimal includedCharge;

    @Column
    private String agentReference;

    @Column
    private Long billingAccountId;

    @Column
    @Enumerated(value = EnumType.STRING)
    private RiskImpact riskImpact;

    @Column
    @Enumerated(value = EnumType.STRING)
    private BillingStatus billingStatus;

    @Column
    private LocalDateTime processStartDate;

    @Column
    private LocalDate billingPeriodEnds;

}
