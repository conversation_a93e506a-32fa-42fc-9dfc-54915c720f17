package com.ipms.user.mapper;

import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.user.dto.auth.AuthDto;
import com.ipms.user.dto.keycloak.KeycloakTokenDto;
import com.ipms.user.dto.role.UserInfoDto;
import org.keycloak.representations.AccessToken;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring")

public interface AuthDtoMapper {

    AuthDto keycloakTokenDtoToAuthDto(KeycloakTokenDto keycloakTokenDto);

    @Mapping(source = "givenName", target = "firstName")
    @Mapping(source = "familyName", target = "lastName")
    @Mapping(source = "preferredUsername", target = "username")
    @Mapping(source = "email", target = "email")
    @Mapping(source = "website", target = "website")
    @Mapping(source = "nickName", target = "nickName")
    @Mapping(source = "otherClaims", target = "expenseCode", qualifiedByName = "mapExpenseCode")

    @Mapping(source = "otherClaims", target = "expenseCode2", qualifiedByName = "mapExpenseCode2")
    UserInfoDto toUserInfoDto(AccessToken accessToken);


    @Named("mapExpenseCode")
    default String mapExpenseCode(Map<String, Object> otherClaims) {
        if (otherClaims.get("expenseCode") != null) {
            return otherClaims.get("expenseCode").toString();
        }
        return "";
    }

    @Named("mapExpenseCode2")
    default String mapExpenseCode2(Map<String, Object> otherClaims) {
        if (otherClaims.get("expenseCode2") != null) {
            return otherClaims.get("expenseCode2").toString();
        }
        return "";
    }
}
