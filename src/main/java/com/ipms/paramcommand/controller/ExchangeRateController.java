package com.ipms.paramcommand.controller;


import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.ExchangeRateResponseDto;
import com.ipms.paramcommand.service.ExchangeRateService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@RequiredArgsConstructor
@RestController
@RequestMapping("/exchange-rate")
@Tag(name = "exchange rate", description = "This endpoint contains exchange rate APIs ")
public class ExchangeRateController {
    private final ExchangeRateService service;

    @GetMapping("/latest")
    public BaseResponse<ExchangeRateResponseDto> getLatest(
            @RequestParam String fromCurrencyCode,
            @RequestParam String toCurrencyCode,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate currencyDate) {
        return BaseResponse.<ExchangeRateResponseDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getLatest(fromCurrencyCode, toCurrencyCode, currencyDate))
                .build();
    }
}
