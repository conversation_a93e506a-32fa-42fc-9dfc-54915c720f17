package com.ipms.activity.repository;

import com.ipms.activity.model.InvestigationFirm;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface InvestigationFirmRepository extends CrudRepository<InvestigationFirm, Long> {
    List<InvestigationFirm> findTop10ByNameContainsIgnoreCaseOrderByName(String name);
    Optional<InvestigationFirm> findByNameAndIdNot(String name, Long aLong);
}
