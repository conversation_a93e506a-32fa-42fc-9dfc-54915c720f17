package com.ipms.activity.service.impl;

import com.ipms.activity.dto.GuaranteePaymentDto;
import com.ipms.activity.mapper.GuaranteePaymentMapper;
import com.ipms.activity.model.GuaranteePayment;
import com.ipms.activity.repository.GuaranteePaymentRepository;
import com.ipms.activity.service.GuaranteePaymentService;
import com.ipms.activity.service.GuaranteeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
@RequiredArgsConstructor
public class GuaranteePaymentServiceImpl implements GuaranteePaymentService {

    private final GuaranteePaymentRepository repository;
    private final GuaranteePaymentMapper mapper;
    private final GuaranteeService guaranteeService;

    @Override
    public GuaranteePaymentDto save(GuaranteePaymentDto dto) {
        var payment = mapper.toGuaranteePayment(dto);
        var guarantee = guaranteeService.getEntityById(dto.getGuaranteeId());
        payment.setGuarantee(guarantee);
        var saved = repository.save(payment);
        return mapper.toGuaranteePaymentDto(saved);
    }

    @Override
    public GuaranteePaymentDto getById(Long id) {
        var payment = repository.findById(id).orElseThrow(EntityNotFoundException::new);
        return mapper.toGuaranteePaymentDto(payment);
    }

    @Override
    public List<GuaranteePaymentDto> getAllByGuaranteeId(Long id) {
        var guaranteePayments = repository.findGuaranteePaymentByGuarantee_Id(id);
        var dtos = new ArrayList<GuaranteePaymentDto>();
        guaranteePayments.sort(Comparator.comparing(GuaranteePayment::getCreatedAt).reversed());
        guaranteePayments.forEach(guaranteePayment -> dtos.add(mapper.toGuaranteePaymentDto(guaranteePayment)));
        return dtos;
    }

    @Override
    public void delete(Long id) {
        var guarantee = repository.findById(id).orElseThrow(EntityNotFoundException::new)
                .toBuilder()
                .isDeleted(true)
                .build();
        repository.save(guarantee);
    }
}
