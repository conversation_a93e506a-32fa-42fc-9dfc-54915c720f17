package com.ipms.paramcommand.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.paramcommand.enums.ExpenseCodeType;
import com.ipms.paramcommand.model.ExpenseCode;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.*;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class ExpenseCodeSpecification extends AbstractSpecification<ExpenseCode> {

    private final List<String> codes;
    private final String code;
    private final String definition;
    private final List<String> types;
    private final String activityType;
    private final String key;

    @Override
    public Predicate toPredicate(Root<ExpenseCode> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Join<ExpenseCode,String> activityJoin = root.join("activityTypes");

        Optional.ofNullable(types)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("type").in(types.stream().map(ExpenseCodeType::valueOf).toList()))));
        Optional.ofNullable(code)
                .ifPresent(value -> predicate.getExpressions().add(cb.like(root.get("code"), "%" + value + "%")));
        Optional.ofNullable(codes)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("code").in(values))));
        Optional.ofNullable(definition)
                .ifPresent(value -> predicate.getExpressions().add(cb.like(root.get("definition"), "%" + value + "%")));

        Optional.ofNullable(activityType)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(activityJoin.in(List.of(activityType)))));


        Optional.ofNullable(key).ifPresent(value -> predicate.getExpressions().add(cb.or(
                        cb.like(root.get("code"), "%" + value + "%"),
                        cb.like(root.get("definition"), "%" + value + "%"))
                ));
        query.distinct(Boolean.TRUE);
        return predicate;
    }
}
