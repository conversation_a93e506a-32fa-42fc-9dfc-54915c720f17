package com.ipms.activity.service;

import com.ipms.activity.dto.GuaranteeDto;
import com.ipms.activity.model.Guarantee;

import java.util.List;

public interface GuaranteeService {
    GuaranteeDto save(GuaranteeDto dto);

    Guarantee getEntityById(Long id);

    GuaranteeDto getById(Long id);

    List<GuaranteeDto> getAllByPreliminaryInjunctionId(Long id);

    GuaranteeDto update(GuaranteeDto dto);

    void delete(Long id);
}
