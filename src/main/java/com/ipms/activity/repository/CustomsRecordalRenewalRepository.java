package com.ipms.activity.repository;

import com.ipms.activity.model.CustomsRecordalRenewal;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CustomsRecordalRenewalRepository extends JpaRepository<CustomsRecordalRenewal, Long> {
    Page<CustomsRecordalRenewal> findAllByActivityId(Long activityId, Pageable pageable);
}
