package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.IPOfficeDto;
import com.ipms.paramcommand.service.IPOfficeService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ip-office")
@Tag(name = "ip-office", description = "This endpoints contains IP Office APIs")
public class IPOfficeController {
    private final IPOfficeService service;

    @GetMapping
    public BaseResponse<List<IPOfficeDto>> getAll() {
        return BaseResponse.<List<IPOfficeDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAll())
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<IPOfficeDto> getById(@PathVariable Long id) {
        return BaseResponse.<IPOfficeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

    @GetMapping("/code/{code}")
    public BaseResponse<IPOfficeDto> getByCode(@PathVariable String code) {
        return BaseResponse.<IPOfficeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByCode(code))
                .build();
    }
}
