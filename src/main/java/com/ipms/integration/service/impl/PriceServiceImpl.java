package com.ipms.integration.service.impl;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.integration.client.JdePriceClient;
import com.ipms.integration.dto.PriceDto;
import com.ipms.integration.dto.PriceRequestParam;
import com.ipms.integration.dto.PriceResponse;
import com.ipms.integration.service.PriceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class PriceServiceImpl implements PriceService {
    private final JdePriceClient jdePriceClient;
    private final ProducerService producerService;

    @Value("${kafka.jde-price-topic}")
    private String jdePriceTopic;

    @Value("${jde.main-companies}")
    private List<String> mainCompanies;

    @Override
    public List<PriceDto> fetchPrices(LocalDate fromUpdateDate, LocalDate toUpdateDate) {
        List<PriceDto> priceDtos = new ArrayList<>();
        mainCompanies.forEach(mainCompany -> {
            var priceRequestParam = PriceRequestParam.builder()
                    .mainCompany(mainCompany)
                    .fromUpdateDate(fromUpdateDate.toString())
                    .toUpdateDate(toUpdateDate.toString())
                    .toDate("2050-12-31")
                    .build();
            log.info("Fetch price {}", priceRequestParam);
            var priceResponse = jdePriceClient.fetchPrices(priceRequestParam);
            log.info("Fetch price response {}", priceResponse);
            if (hasPrice(priceResponse)) {
                priceDtos.addAll(priceResponse.getPriceLists());
            }
        });
        sendPricesEvent(priceDtos);
        return priceDtos;
    }

    private void sendPricesEvent(List<PriceDto> priceDtos) {
        if (!priceDtos.isEmpty()) {
            var priceResponse = PriceResponse.builder()
                    .priceLists(priceDtos)
                    .build();
            producerService.send(jdePriceTopic, ObjectUtils.toJson(priceResponse));
        }
    }

    private boolean hasPrice(PriceResponse priceResponse) {
        return "0".equals(priceResponse.getResponseInformatin().getErrorCode());
    }
}
