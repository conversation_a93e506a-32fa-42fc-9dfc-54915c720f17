package com.ipms.activity.service;

import com.ipms.activity.dto.SuspendedProductDto;
import com.ipms.activity.mapper.SuspendedProductMapper;
import com.ipms.activity.mapper.SuspendedProductMapperImpl;
import com.ipms.activity.model.SuspendedProduct;
import com.ipms.activity.repository.SuspendedProductRepository;
import com.ipms.activity.service.impl.SuspendedProductServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
public class SuspendedProductServiceTest {
    @MockBean
    private SuspendedProductService service;

    @Mock
    private SuspendedProductRepository repository;


    private final SuspendedProductMapper mapper = new SuspendedProductMapperImpl();

    @Before
    public void setUp() {
        service = new SuspendedProductServiceImpl(repository, mapper);
    }

    @Test
    public void givenSuspendedProductDto_whenSave_thenReturnSuspendedProductDto() {
        ArgumentCaptor<SuspendedProduct> savedProductCaptor = ArgumentCaptor.forClass(SuspendedProduct.class);
        Mockito.when(repository.save(savedProductCaptor.capture()))
                .thenReturn(getSuspendedProduct());

        assertEquals(getSavedSuspendedProductDto(),service.save(getSuspendedProductDto()));
    }

    @Test
    public void givenId_whenGetById_thenReturnSuspendedProductDto() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(getSuspendedProduct()));
        assertEquals(getSavedSuspendedProductDto(), service.getById(1L));
    }

    @Test
    public void givenActivityIdAndPageAndSize_whenGetAllByActivity_thenReturnSuspendedProductPageDto() {
        Page<SuspendedProduct> page = new PageImpl<>(List.of(getSuspendedProduct()), Pageable.ofSize(1), 1);
        Mockito.when(repository.findAllByActivityId(Mockito.anyLong(),Mockito.any(Pageable.class)))
                .thenReturn(page);
        assertThat(service.getAllByActivity(1L,2,3).getTotalElements())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenDelete_thenReturnSuspendedProductAsDeletedTrue() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(getSuspendedProduct()));

        ArgumentCaptor<SuspendedProduct> savedProductCaptor = ArgumentCaptor.forClass(SuspendedProduct.class);

        Mockito.when(repository.save(savedProductCaptor.capture()))
                .thenAnswer(invocationOnMock -> invocationOnMock.getArgument(0));
        service.delete(1L);
        Assertions.assertTrue(savedProductCaptor.getValue().isDeleted());
    }

    private SuspendedProductDto getSuspendedProductDto() {
        return SuspendedProductDto.builder()
                .number(123)
                .type("type-1")
                .customsRecordalMatterId(1L)
                .activityId(1L)
                .build();
    }

    private SuspendedProductDto getSavedSuspendedProductDto() {
        return SuspendedProductDto.builder()
                .id(1L)
                .number(123)
                .type("type-1")
                .customsRecordalMatterId(1L)
                .activityId(1L)
                .build();
    }

    private SuspendedProduct getSuspendedProduct() {
        return SuspendedProduct.builder()
                .id(1L)
                .version(1L)
                .isDeleted(Boolean.FALSE)
                .number(123)
                .type("type-1")
                .customsRecordalMatterId(1L)
                .activityId(1L)
                .build();
    }

}
