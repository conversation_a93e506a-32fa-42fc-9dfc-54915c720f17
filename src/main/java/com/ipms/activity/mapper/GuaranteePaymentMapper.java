package com.ipms.activity.mapper;

import com.ipms.activity.dto.GuaranteePaymentDto;
import com.ipms.activity.model.GuaranteePayment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface GuaranteePaymentMapper {
    GuaranteePayment toGuaranteePayment(GuaranteePaymentDto dto);

    @Mapping(source = "guarantee.id", target = "guaranteeId")
    GuaranteePaymentDto toGuaranteePaymentDto(GuaranteePayment guaranteePayment);
}
