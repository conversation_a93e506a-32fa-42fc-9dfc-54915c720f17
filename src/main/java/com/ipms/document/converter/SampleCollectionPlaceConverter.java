package com.ipms.document.converter;

import com.ipms.document.enums.SampleCollectionPlace;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class SampleCollectionPlaceConverter implements AttributeConverter<SampleCollectionPlace, String> {

    @Override
    public String convertToDatabaseColumn(SampleCollectionPlace collectionPlace) {
        return collectionPlace == null ? null : collectionPlace.getValue();
    }

    @Override
    public SampleCollectionPlace convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : SampleCollectionPlace.getEnum(s);
    }
}
