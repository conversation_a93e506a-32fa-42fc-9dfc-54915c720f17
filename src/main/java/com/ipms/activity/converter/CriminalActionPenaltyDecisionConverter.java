package com.ipms.activity.converter;

import com.ipms.activity.enums.CriminalActionPenaltyDecision;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class CriminalActionPenaltyDecisionConverter implements AttributeConverter<CriminalActionPenaltyDecision, String> {

    @Override
    public String convertToDatabaseColumn(CriminalActionPenaltyDecision deliveryType) {
        return deliveryType == null ? null : deliveryType.getValue();
    }

    @Override
    public CriminalActionPenaltyDecision convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : CriminalActionPenaltyDecision.getEnum(s);
    }
}
