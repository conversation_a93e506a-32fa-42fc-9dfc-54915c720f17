package com.ipms.activity.controller;

import com.ipms.activity.dto.CustomsSuspensionsSenderDto;
import com.ipms.activity.service.CustomsSuspensionsSenderService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/customs-suspensions/senders")
public class CustomsSuspensionsSendersController {
    private final CustomsSuspensionsSenderService service;

    @GetMapping("/search/{name}")
    public BaseResponse<List<CustomsSuspensionsSenderDto>> search(@PathVariable @NotEmpty @Size(min = 3) String name) {
        var senders = service.findAllByName(name);
        return BaseResponse.<List<CustomsSuspensionsSenderDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(senders)
                .build();
    }
}
