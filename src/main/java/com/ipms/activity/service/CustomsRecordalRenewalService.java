package com.ipms.activity.service;

import com.ipms.activity.dto.CustomsRecordalRenewalDto;
import com.ipms.activity.dto.CustomsRecordalRenewalPageDto;

public interface CustomsRecordalRenewalService {
    CustomsRecordalRenewalDto save(CustomsRecordalRenewalDto renewalDto);
    CustomsRecordalRenewalDto update(CustomsRecordalRenewalDto renewalDto, Long id);
    void delete(Long id, Long version);
    CustomsRecordalRenewalDto getById(Long id);
    CustomsRecordalRenewalPageDto getAllByActivityId(Long activityId, int page, int size);
}
