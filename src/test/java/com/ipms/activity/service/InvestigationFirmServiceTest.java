package com.ipms.activity.service;

import com.ipms.activity.dto.InvestigationFirmDto;
import com.ipms.activity.exception.InvestigationFirmException;
import com.ipms.activity.mapper.InvestigationFirmMapper;
import com.ipms.activity.mapper.InvestigationFirmMapperImpl;
import com.ipms.activity.model.InvestigationFirm;
import com.ipms.activity.model.UseInvestigation;
import com.ipms.activity.repository.InvestigationFirmRepository;
import com.ipms.activity.service.impl.InvestigationFirmServiceImpl;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static org.assertj.core.api.AssertionsForClassTypes.*;

@RunWith(SpringRunner.class)
public class InvestigationFirmServiceTest {
    @MockBean
    private InvestigationFirmService service;

    @Mock
    private InvestigationFirmRepository repository;


    private final InvestigationFirmMapper mapper = new InvestigationFirmMapperImpl();

    @Before
    public void setUp() {
        service = new InvestigationFirmServiceImpl(repository, mapper);
    }

    @Test
    public void whenUpdate_thenReturnInvestigationFirmDto() {
        var dto = InvestigationFirmDto.builder()
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(new ArrayList(Arrays.asList("<EMAIL>")))
                .phones(new ArrayList(Arrays.asList("123456")))
                .faxes(new ArrayList(Arrays.asList("456789")))
                .build();

        var firm = InvestigationFirm.builder()
                .id(1L)
                .name("Updated Investigation Firm")
                .version(1L)
                .emails(new ArrayList(Arrays.asList("<EMAIL>")))
                .phones(new ArrayList(Arrays.asList("123456")))
                .faxes(new ArrayList(Arrays.asList("456789")))
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                        .thenReturn(Optional.of(firm));

        Mockito.when(repository.findByNameAndIdNot(Mockito.anyString(), Mockito.anyLong()))
                        .thenReturn(Optional.empty());

        Mockito.when(repository.save(Mockito.any(InvestigationFirm.class)))
                        .thenReturn(firm);

        assertThat(service.update(dto, 1L).getId())
                .isEqualTo(firm.getId());
    }

    @Test
    public void givenNotExist_whenUpdate_thenThrowInvestigationFirmException() {
        var dto = InvestigationFirmDto.builder()
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());

        Throwable thrown = catchThrowable(() -> service.update(dto, 1L));
        assertThat(thrown).isInstanceOf(InvestigationFirmException.class);
    }

    @Test
    public void givenSameName_whenUpdate_thenThrowInvestigationFirmException() {
        var dto = InvestigationFirmDto.builder()
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();

        var firm = InvestigationFirm.builder()
                .id(1L)
                .name("Updated Investigation Firm")
                .version(1L)
                .emails(new ArrayList(Arrays.asList("<EMAIL>")))
                .phones(new ArrayList(Arrays.asList("123456")))
                .faxes(new ArrayList(Arrays.asList("456789")))
                .build();

        Mockito.when(repository.findByNameAndIdNot(Mockito.anyString(), Mockito.anyLong()))
                .thenReturn(Optional.of(firm));

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));

        Throwable thrown = catchThrowable(() -> service.update(dto, 1L));
        assertThat(thrown).isInstanceOf(InvestigationFirmException.class);
    }

    @Test
    public void whenSaveList_thenReturnInvestigationFirmList() {
        var dto1 = InvestigationFirmDto.builder()
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        var firm1 = InvestigationFirm.builder()
                .id(30L)
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        Mockito.when(repository.save(Mockito.any(InvestigationFirm.class)))
                .thenReturn(firm1);

        var dto2 = InvestigationFirmDto.builder()
                .id(2L)
                .name("Updated Investigation Firm 2")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        var firm2 = InvestigationFirm.builder()
                .id(2L)
                .name("Updated Investigation Firm 2")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        Mockito.when(repository.findById(2L))
                .thenReturn(Optional.of(firm2));

        Assertions.assertThat(service.saveList(List.of(dto1, dto2)))
                .hasSize(2)
                .extracting(InvestigationFirm::getId)
                .containsExactlyInAnyOrder(2L, 30L);
    }

    @Test
    public void whenGetByName_thenReturnInvestigationFirmDtoList() {
        var firm1 = InvestigationFirm.builder()
                .id(1L)
                .name("Updated Investigation Firm 1")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        var firm2 = InvestigationFirm.builder()
                .id(2L)
                .name("Updated Investigation Firm 2")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        Mockito.when(repository.findTop10ByNameContainsIgnoreCaseOrderByName(Mockito.anyString()))
                .thenReturn(List.of(firm1,firm2));
        Assertions.assertThat(service.getByName("firm"))
                .hasSize(2)
                .extracting(InvestigationFirmDto::getId)
                .containsExactlyInAnyOrder(1L, 2L);
    }

    @Test
    public void whenGetById_thenReturnInvestigationFirmDto() {
        var firm = InvestigationFirm.builder()
                .id(1L)
                .name("Updated Investigation Firm 1")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                        .thenReturn(Optional.of(firm));

        assertThat(service.getById(1L).getId()).isEqualTo(1L);
    }

    @Test
    public void whenDelete_thenDoesNotThrowAnyException() {
        var useInvestigation = UseInvestigation.builder()
                .id(1L)
                .build();
        var firm = InvestigationFirm.builder()
                .id(1L)
                .name("Updated Investigation Firm 1")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .useInvestigations(Set.of(useInvestigation))
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));

        assertThatCode(() -> service.delete(1L)).doesNotThrowAnyException();
    }

    @Test
    public void givenUnused_whenDelete_thenDoesNotThrowAnyException() {
        var firm = InvestigationFirm.builder()
                .id(1L)
                .name("Updated Investigation Firm 1")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .useInvestigations(Set.of())
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));

        assertThatCode(() -> service.delete(1L)).doesNotThrowAnyException();
    }
}
