package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.activity.dto.InvestigationFirmDto;
import com.ipms.activity.service.InvestigationFirmService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class InvestigationFirmControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private InvestigationFirmService service;

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void whenGetById_thenReturnBaseResponse() throws Exception {
        Long id = 1L;
        InvestigationFirmDto investigationFirmDto = new InvestigationFirmDto();
        investigationFirmDto.setId(id);
        investigationFirmDto.setName("Investigation Firm");

        Mockito.when(service.getById(id)).thenReturn(investigationFirmDto);

        mvc.perform(get("/investigation-firm/{id}", id)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdate_thenReturnBaseResponse() throws Exception {
        Long id = 1L;
        var dto = InvestigationFirmDto.builder()
                .id(id)
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();

        Mockito.when(service.update(Mockito.any(InvestigationFirmDto.class), Mockito.anyLong()))
                .thenReturn(dto);

        mvc.perform(put("/investigation-firm/{id}", id)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(dto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong());
        mvc.perform(delete("/investigation-firm/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByName_thenReturnBaseResponse() throws Exception {
        String name = "Investigation Firm";

        var dto = InvestigationFirmDto.builder()
                .id(1L)
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();

        Mockito.when(service.getByName(name)).thenReturn(List.of(dto));

        mvc.perform(get("/investigation-firm/name/?name=Investigation%Firm")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
