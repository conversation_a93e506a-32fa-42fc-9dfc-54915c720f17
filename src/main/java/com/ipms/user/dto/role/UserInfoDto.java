package com.ipms.user.dto.role;

import lombok.*;
import org.keycloak.representations.idm.authorization.Permission;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoDto {
    private String firstName;
    private String lastName;
    private String username;
    private String email;
    private String website;
    private String nickName;
    private String expenseCode;
    private String expenseCode2;
    @Singular
    private List<String> roles = new ArrayList<>();
    private List<Permission> permissions;
}
