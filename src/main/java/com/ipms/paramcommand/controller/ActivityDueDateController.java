package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.ActivityDueDateDto;
import com.ipms.paramcommand.dto.ActivityDueDatePageDto;
import com.ipms.paramcommand.service.ActivityDueDateService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RequiredArgsConstructor
@RestController
@RequestMapping("activity-due-date")
@Tag(name = "activity-due-date", description = "This endpoints contains activity-due-date APIs")
public class ActivityDueDateController {
    private final ActivityDueDateService service;

    @PostMapping
    public BaseResponse<ActivityDueDateDto> save(@Valid @RequestBody ActivityDueDateDto dto) {
        return BaseResponse.<ActivityDueDateDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/{typeId}/{page}/{size}")
    public BaseResponse<ActivityDueDatePageDto> getAll(@PathVariable Long typeId,
                                                      @PathVariable int page,
                                                      @PathVariable int size){
        var pageDto= service.getAll(typeId, page, size);
        return BaseResponse.<ActivityDueDatePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(pageDto)
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id){
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/by-status/{matterType}/{activity}/{status}")
    public BaseResponse<ActivityDueDateDto> getLatestByMatterTypeActivityAndStatus(@PathVariable String matterType,
                                                                                   @PathVariable String activity,
                                                                                   @PathVariable String status){
        var pageDto= service.getLatestByMatterTypeActivityAndStatus(matterType, activity, status);
        return BaseResponse.<ActivityDueDateDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(pageDto)
                .build();
    }
}

