eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:release-ipms-executor}.release-ipms-executor
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

spring:
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-release/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-release
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk

cron:
  mail:
    default-folder: 0 * * ? * *
    ipms-folder: 0 * * ? * *
    ipms-document-folder: 0 * * ? * *
    rollback-all: 0,10,20,30,40,50 * * * * *
    retry-all: 0 0 * ? * *
  transfer:
    send-all: 0 */10 * ? * *
  billing:
    billingProcess: 0 0 1 ? * *
    billingProcessByBillingAmount: 0 30 1 ? * *
    billingProcessByStatus: 0 0 2 ? * *
    autoDeleteUnapprovedPayments: 0 45 20 * * *
  timesheet:
    approveBySystem: 0 0 9,20 * * *
  integration:
    fetch-exchange-rates: 0 0 * * * *
    send-billing-order: 0 */10 * ? * *
    update-external-order-number: 0 */10 * ? * *
    fetch-invoices: 0 */10 * ? * *
    fetch-prices: 0 0 1 * * *

jde:
  main-company: "00004"
  minus-day-invoice: 7
  minus-day-price: 1