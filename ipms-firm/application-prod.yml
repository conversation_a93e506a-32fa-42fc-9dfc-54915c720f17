eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:prod-ipms-firm}.prod-ipms-firm
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

spring:
  datasource:
    url: *********************************************************************************
    username: ${db_username}
    password: ${db_password}
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: ${keycloak_ipms_backend_client_secret}
        provider:
          keycloak:
            token-uri: http://keycloak.keycloak/auth/realms/ipms-production/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-production
  auth-server-url: http://keycloak.keycloak/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: ${keycloak_ipms_backend_client_secret}

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  firm-associated-topic: firmAssociatedTopic

azure:
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=ipmsprodstorage;AccountKey=${azure_storage_account_key};
    container-name: ipmsprodsa
    sas-host-url: https://ipmsprodstorage.blob.core.windows.net/ipmsprodsa/