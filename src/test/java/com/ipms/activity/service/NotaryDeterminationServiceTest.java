package com.ipms.activity.service;

import com.ipms.activity.dto.NotaryDeterminationDto;
import com.ipms.activity.mapper.NotaryDeterminationMapper;
import com.ipms.activity.model.NotaryDetermination;
import com.ipms.activity.repository.NotaryDeterminationRepository;
import com.ipms.activity.service.impl.NotaryDeterminationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
class NotaryDeterminationServiceTest {

    @Mock
    private NotaryDeterminationRepository repository;

    @Mock
    private NotaryDeterminationMapper mapper;

    @MockBean
    private NotaryDeterminationService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        service = new NotaryDeterminationServiceImpl(repository, mapper);
    }

    @Test
    void getByActivityId_ValidActivityId_ReturnsList() {
        Long activityId = 1L;
        List<NotaryDetermination> determinationList = new ArrayList<>();
        determinationList.add(new NotaryDetermination());
        when(repository.getAllByActivity_Id(activityId)).thenReturn(determinationList);

        List<NotaryDeterminationDto> result = service.getByActivityId(activityId);

        assertEquals(1, result.size());
    }

    @Test
    void getByActivityId_InvalidActivityId_ReturnsEmptyList() {
        Long activityId = 1L;
        when(repository.getAllByActivity_Id(activityId)).thenReturn(new ArrayList<>());

        List<NotaryDeterminationDto> result = service.getByActivityId(activityId);

        assertEquals(0, result.size());
    }

    @Test
    void getById_ExistingId_ReturnsDto() {
        Long id = 1L;
        NotaryDetermination determination = new NotaryDetermination();
        NotaryDeterminationDto expectedDto = new NotaryDeterminationDto();
        when(repository.findById(id)).thenReturn(Optional.of(determination));
        when(mapper.toNotaryDeterminationDto(determination)).thenReturn(expectedDto);

        NotaryDeterminationDto result = service.getById(id);

        assertEquals(expectedDto, result);
    }

    @Test
    void getById_NonExistingId_ReturnsNull() {
        Long id = 1L;
        when(repository.findById(id)).thenReturn(Optional.empty());

        NotaryDeterminationDto result = service.getById(id);

        assertEquals(null, result);
    }
}