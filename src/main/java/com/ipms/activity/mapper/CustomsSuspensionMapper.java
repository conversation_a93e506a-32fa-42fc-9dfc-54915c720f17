package com.ipms.activity.mapper;

import com.ipms.activity.dto.CustomsSuspensionDto;
import com.ipms.activity.model.CustomsSuspension;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", uses = {CustomsSuspensionShippingMapper.class,
        CustomsSuspensionReceiverMapper.class, CustomsSuspensionSenderMapper.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomsSuspensionMapper {
    CustomsSuspensionDto toDto(CustomsSuspension customsSuspension);
    CustomsSuspension toCustomsSuspension(CustomsSuspensionDto dto);
    CustomsSuspension toCustomsSuspensionFromDto(CustomsSuspensionDto dto, @MappingTarget CustomsSuspension customsSuspension);
}
