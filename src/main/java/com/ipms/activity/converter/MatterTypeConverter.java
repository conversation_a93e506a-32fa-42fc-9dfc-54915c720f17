package com.ipms.activity.converter;

import com.ipms.activity.enums.MatterType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class MatterTypeConverter implements AttributeConverter<MatterType, String> {
    @Override
    public String convertToDatabaseColumn(MatterType matterType) {
        return matterType == null ? null : matterType.getValue();
    }

    @Override
    public MatterType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : MatterType.getEnum(s);
    }
}