package com.ipms.activity.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "customs_suspensions_shipping")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class CustomsSuspensionsShipping extends VersionedEntity {

    @ManyToMany(mappedBy = "shippings")
    private List<CustomsSuspension> customsSuspensions;

    private String name;
}
