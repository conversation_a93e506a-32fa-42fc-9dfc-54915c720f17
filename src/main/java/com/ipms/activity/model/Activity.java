package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.BillingStatus;
import com.ipms.activity.enums.RiskImpact;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "activity")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Activity extends VersionedEntity {

    @Column
    private ActivityType type;

    @Column
    private ActivityStatus status;

    @Column
    private LocalDate instructionDate;

    @Column
    private Long firmId;

    @Column
    private Long matterId;

    @Column
    private BigDecimal includedCharge;

    @Column
    private String agentReference;

    @Column
    private Long billingAccountId;

    @Column
    @Enumerated(value = EnumType.STRING)
    private RiskImpact riskImpact;

    @Column
    @Enumerated(value = EnumType.STRING)
    private BillingStatus billingStatus;

    @Column
    private LocalDateTime processStartDate;

    @Column
    private LocalDate billingPeriodEnds;

    @ElementCollection
    @CollectionTable(name = "activity_issues", joinColumns = @JoinColumn(name = "activity_id", referencedColumnName = "id"))
    @Column(name = "issue")
    private List<Long> issues;

    @ElementCollection
    @CollectionTable(name = "activity_documents", joinColumns = @JoinColumn(name = "activity_id", referencedColumnName = "id"))
    @Column(name = "document")
    private List<Long> documents;

    @ElementCollection
    @CollectionTable(name = "activity_evidences", joinColumns = @JoinColumn(name = "activity_id", referencedColumnName = "id"))
    @Column(name = "evidence")
    private List<Long> evidences;

    @ElementCollection
    @CollectionTable(name = "activity_samples", joinColumns = @JoinColumn(name = "activity_id", referencedColumnName = "id"))
    @Column(name = "sample")
    private List<Long> samples;

    @ElementCollection
    @CollectionTable(name = "activity_quotation",
            joinColumns = @JoinColumn(name = "activity_id", referencedColumnName = "id"))
    @Column(name = "quotation_id")
    private List<Long> quotations;

    @OneToMany(mappedBy = "activity", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<TSICPackage> tsicPackages;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private CDLetter cdLetter;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private UseInvestigation useInvestigation;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private CourtAction courtAction;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private DeterminationAction determinationAction;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private PreliminaryInjunction preliminaryInjunction;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private CriminalAction criminalAction;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private CustomsSuspension customsSuspension;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private CustomsRecordal customsRecordal;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private NotaryDetermination notaryDetermination;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private OppositionOut oppositionOut;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private Registration registration;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private OppositionIn oppositionIn;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private Objection objection;

    @OneToOne(mappedBy = "activity", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    @NotAudited
    private Renewal renewal;

    @Column
    private Long localAgent;

    @Column
    private LocalDate statusDueDate;
}
