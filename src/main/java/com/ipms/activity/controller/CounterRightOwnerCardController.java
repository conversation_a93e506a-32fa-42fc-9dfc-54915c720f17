package com.ipms.activity.controller;


import com.ipms.activity.dto.CounterRightOwnerCardDto;
import com.ipms.activity.service.CounterRightOwnerCardService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/counter-right-owner-card")
public class CounterRightOwnerCardController {
    private final CounterRightOwnerCardService service;

    @PostMapping
    public BaseResponse<CounterRightOwnerCardDto> save(@RequestBody @Valid CounterRightOwnerCardDto dto) {
        return BaseResponse.<CounterRightOwnerCardDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PostMapping("/bulk")
    public BaseResponse<List<CounterRightOwnerCardDto>> saveBulk(@RequestBody @Valid List<CounterRightOwnerCardDto> dtos) {
        return BaseResponse.<List<CounterRightOwnerCardDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.saveBulk(dtos))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<CounterRightOwnerCardDto> update(@PathVariable Long id, @RequestBody @Valid CounterRightOwnerCardDto dto) {
        return BaseResponse.<CounterRightOwnerCardDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(id, dto))
                .build();
    }

    @GetMapping("/by-activity/{activityId}")
    public BaseResponse<List<CounterRightOwnerCardDto>> getAllByActivityId(@PathVariable Long activityId) {
        return BaseResponse.<List<CounterRightOwnerCardDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByActivityId(activityId))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

}
