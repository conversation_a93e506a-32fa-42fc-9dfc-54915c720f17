package com.ipms.user.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
public class AccessTokenRequest {

    @NotBlank(message = "{error.code.user.username.not_blank}")
    @Schema(description = "This field is required to get access token.")
    private String username;

    @NotBlank(message = "{error.code.user.password.not_blank}")
    @Schema(description = "This field is required to get access token.")
    private String password;

    private String otp;
}
