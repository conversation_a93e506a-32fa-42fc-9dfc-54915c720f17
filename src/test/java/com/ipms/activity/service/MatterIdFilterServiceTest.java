package com.ipms.activity.service;

import com.ipms.activity.dto.ActivityFilterRequest;
import com.ipms.activity.dto.MatterIdAndActivityStatus;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.OverTimeChoice;
import com.ipms.activity.model.Activity;
import com.ipms.activity.repository.ActivityRepository;
import com.ipms.activity.specification.ActivitySpecification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MatterIdFilterServiceTest {

    @Mock
    private ActivityRepository repository;

    private MatterIdFilterService service;

    @BeforeEach
    void setUp() {
        service = new MatterIdFilterService(repository);
    }

    @Test
    void given_MatterIdsOnly_when_FilteringMatters_then_ReturnsActiveMattersOnly() {
        List<Long> matterIds = List.of(1L, 2L, 3L);
        ActivityFilterRequest filterRequest = ActivityFilterRequest.builder()
                .matterIds(matterIds)
                .types(Collections.emptyList())
                .statuses(Collections.emptyList())
                .build();

        List<MatterIdAndActivityStatus> matterActivities = List.of(
                MatterIdAndActivityStatus.builder().matterId(1L).id(10L).status(ActivityStatus.INSTRUCTION).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(2L).id(20L).status(ActivityStatus.COMPLETED).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(3L).id(30L).status(ActivityStatus.ACCEPTED).type(ActivityType.COURT_ACTION).build()
        );

        when(repository.findAllByMatterIdIn(matterIds)).thenReturn(matterActivities);

        List<Long> result = service.filterMatterIdsByTypesAndStatuses(filterRequest);

        assertThat(result)
            .hasSize(2)
            .contains(1L, 3L)
            .doesNotContain(2L);
        verify(repository).findAllByMatterIdIn(matterIds);
        verify(repository, never()).findAll(any(ActivitySpecification.class));
    }

    @Test
    void given_TypesAndStatusesFilters_when_FilteringMatters_then_ReturnsMatchingActiveMatters() {
        List<Long> matterIds = List.of(1L, 2L, 3L);
        List<String> types = List.of(ActivityType.COURT_ACTION.name());
        List<String> statuses = List.of(ActivityStatus.INSTRUCTION.name(), ActivityStatus.ACCEPTED.name());
        
        ActivityFilterRequest filterRequest = ActivityFilterRequest.builder()
                .matterIds(matterIds)
                .types(types)
                .statuses(statuses)
                .build();

        List<MatterIdAndActivityStatus> allMatterActivities = List.of(
                MatterIdAndActivityStatus.builder().matterId(1L).id(10L).status(ActivityStatus.INSTRUCTION).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(2L).id(20L).status(ActivityStatus.COMPLETED).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(3L).id(30L).status(ActivityStatus.ACCEPTED).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(4L).id(40L).status(ActivityStatus.INSTRUCTION).type(ActivityType.LEGAL_ADVICE).build()
        );

        when(repository.findAllByMatterIdIn(matterIds)).thenReturn(allMatterActivities);

        List<Long> result = service.filterMatterIdsByTypesAndStatuses(filterRequest);

        assertThat(result)
            .hasSize(2)
            .contains(1L, 3L)
            .doesNotContain(2L, 4L);
        verify(repository).findAllByMatterIdIn(matterIds);
        verify(repository, never()).findAll(any(ActivitySpecification.class));
    }

    @Test
    void given_OvertimeChoiceYes_when_FilteringMatters_then_ReturnsMattersWithOvertime() {
        List<Long> matterIds = List.of(1L, 2L, 3L);
        ActivityFilterRequest filterRequest = ActivityFilterRequest.builder()
                .matterIds(matterIds)
                .types(Collections.emptyList())
                .statuses(Collections.emptyList())
                .overTimeChoice(OverTimeChoice.YES)
                .build();

        List<MatterIdAndActivityStatus> matterActivities = List.of(
                MatterIdAndActivityStatus.builder().matterId(1L).id(10L).status(ActivityStatus.INSTRUCTION).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(2L).id(20L).status(ActivityStatus.INSTRUCTION).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(3L).id(30L).status(ActivityStatus.ACCEPTED).type(ActivityType.COURT_ACTION).build()
        );

        List<Activity> activitiesWithOvertime = List.of(
                Activity.builder().matterId(1L).build(),
                Activity.builder().matterId(3L).build()
        );

        when(repository.findAllByMatterIdIn(matterIds)).thenReturn(matterActivities);
        when(repository.findAll(any(ActivitySpecification.class))).thenReturn(activitiesWithOvertime);

        List<Long> result = service.filterMatterIdsByTypesAndStatuses(filterRequest);

        assertThat(result)
            .hasSize(2)
            .contains(1L, 3L)
            .doesNotContain(2L);
        
        ArgumentCaptor<ActivitySpecification> specCaptor = ArgumentCaptor.forClass(ActivitySpecification.class);
        verify(repository).findAll(specCaptor.capture());
        
        ActivitySpecification capturedSpec = specCaptor.getValue();
        assertThat(capturedSpec).isNotNull();
    }

    @Test
    void given_OvertimeChoiceNo_when_FilteringMatters_then_ReturnsMattersWithoutOvertime() {
        List<Long> matterIds = List.of(1L, 2L, 3L);
        ActivityFilterRequest filterRequest = ActivityFilterRequest.builder()
                .matterIds(matterIds)
                .types(Collections.emptyList())
                .statuses(Collections.emptyList())
                .overTimeChoice(OverTimeChoice.NO)
                .build();

        List<MatterIdAndActivityStatus> matterActivities = List.of(
                MatterIdAndActivityStatus.builder().matterId(1L).id(10L).status(ActivityStatus.INSTRUCTION).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(2L).id(20L).status(ActivityStatus.INSTRUCTION).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(3L).id(30L).status(ActivityStatus.ACCEPTED).type(ActivityType.COURT_ACTION).build()
        );

        List<Activity> activitiesWithoutOvertime = List.of(
                Activity.builder().matterId(2L).build()
        );

        when(repository.findAllByMatterIdIn(matterIds)).thenReturn(matterActivities);
        when(repository.findAll(any(ActivitySpecification.class))).thenReturn(activitiesWithoutOvertime);

        List<Long> result = service.filterMatterIdsByTypesAndStatuses(filterRequest);

        assertThat(result)
            .hasSize(1)
            .contains(2L)
            .doesNotContain(1L, 3L);
        
        ArgumentCaptor<ActivitySpecification> specCaptor = ArgumentCaptor.forClass(ActivitySpecification.class);
        verify(repository).findAll(specCaptor.capture());
        
        ActivitySpecification capturedSpec = specCaptor.getValue();
        assertThat(capturedSpec).isNotNull();
    }

    @Test
    void given_MixOfActiveAndInactiveMatters_when_FilteringMatters_then_ExcludesMattersWithOnlyInactiveStatuses() {
        List<Long> matterIds = List.of(1L, 2L, 3L);
        ActivityFilterRequest filterRequest = ActivityFilterRequest.builder()
                .matterIds(matterIds)
                .types(Collections.emptyList())
                .statuses(Collections.emptyList())
                .build();

        List<MatterIdAndActivityStatus> matterActivities = List.of(
                MatterIdAndActivityStatus.builder().matterId(1L).id(10L).status(ActivityStatus.COMPLETED).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(1L).id(11L).status(ActivityStatus.ABANDONMENT).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(2L).id(20L).status(ActivityStatus.INSTRUCTION).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(2L).id(21L).status(ActivityStatus.COMPLETED).type(ActivityType.COURT_ACTION).build(),
                MatterIdAndActivityStatus.builder().matterId(3L).id(30L).status(ActivityStatus.ACCEPTED).type(ActivityType.COURT_ACTION).build()
        );

        when(repository.findAllByMatterIdIn(matterIds)).thenReturn(matterActivities);

        List<Long> result = service.filterMatterIdsByTypesAndStatuses(filterRequest);

        assertThat(result)
            .hasSize(2)
            .contains(2L, 3L)
            .doesNotContain(1L);
        verify(repository).findAllByMatterIdIn(matterIds);
        verify(repository, never()).findAll(any(ActivitySpecification.class));
    }

    @Test
    void given_EmptyMatterIdsList_when_FilteringMatters_then_ReturnsEmptyList() {
        ActivityFilterRequest filterRequest = ActivityFilterRequest.builder()
                .matterIds(Collections.emptyList())
                .types(Collections.emptyList())
                .statuses(Collections.emptyList())
                .build();

        when(repository.findAllByMatterIdIn(Collections.emptyList())).thenReturn(Collections.emptyList());

        List<Long> result = service.filterMatterIdsByTypesAndStatuses(filterRequest);

        assertThat(result)
            .isEmpty();
        verify(repository).findAllByMatterIdIn(Collections.emptyList());
        verify(repository, never()).findAll(any(ActivitySpecification.class));
    }

    @Test
    void given_NullOvertimeChoice_when_FilteringMatters_then_IgnoresOvertimeFilter() {
        List<Long> matterIds = List.of(1L, 2L, 3L);
        ActivityFilterRequest filterRequest = ActivityFilterRequest.builder()
                .matterIds(matterIds)
                .types(Collections.emptyList())
                .statuses(Collections.emptyList())
                .overTimeChoice(null)
                .build();

        List<MatterIdAndActivityStatus> matterActivities = List.of(
                MatterIdAndActivityStatus.builder().matterId(1L).id(10L).status(ActivityStatus.INSTRUCTION).type(ActivityType.LEGAL_ADVICE).build(),
                MatterIdAndActivityStatus.builder().matterId(2L).id(20L).status(ActivityStatus.COMPLETED).type(ActivityType.LEGAL_ADVICE).build(),
                MatterIdAndActivityStatus.builder().matterId(3L).id(30L).status(ActivityStatus.ACCEPTED).type(ActivityType.LEGAL_ADVICE).build()
        );

        when(repository.findAllByMatterIdIn(matterIds)).thenReturn(matterActivities);

        List<Long> result = service.filterMatterIdsByTypesAndStatuses(filterRequest);

        assertThat(result)
            .hasSize(2)
            .contains(1L, 3L)
            .doesNotContain(2L);
        verify(repository).findAllByMatterIdIn(matterIds);
        verify(repository, never()).findAll(any(ActivitySpecification.class));
    }
}