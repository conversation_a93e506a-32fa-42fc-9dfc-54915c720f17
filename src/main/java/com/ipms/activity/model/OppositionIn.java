package com.ipms.activity.model;

import com.ipms.activity.enums.AdverseApplicationStatus;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "opposition_in")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class OppositionIn extends BulletinRelatedActivity {
    @Column
    private LocalDate applicationDate;

    @Column
    private LocalDate registrationDate;

    @Column
    private AdverseApplicationStatus adverseApplicationStatus;
}
