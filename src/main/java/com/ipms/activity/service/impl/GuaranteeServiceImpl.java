package com.ipms.activity.service.impl;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.dto.GuaranteeDto;
import com.ipms.activity.enums.GuaranteeType;
import com.ipms.activity.enums.PaymentStatus;
import com.ipms.activity.mapper.GuaranteeMapper;
import com.ipms.activity.model.Guarantee;
import com.ipms.activity.repository.GuaranteeRepository;
import com.ipms.activity.service.AttachmentService;
import com.ipms.activity.service.GuaranteeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class GuaranteeServiceImpl implements GuaranteeService {

    private final GuaranteeRepository repository;
    private final GuaranteeMapper mapper;
    private final AttachmentService attachmentService;

    @Override
    public GuaranteeDto save(GuaranteeDto dto) {
        var guarantee = mapper.toGuarantee(dto);
        guarantee.setPaymentStatus(PaymentStatus.PAYMENT_IS_WAITING);
        var saved = repository.save(guarantee);
        return mapper.toGuaranteeDto(saved);
    }

    @Override
    public Guarantee getEntityById(Long id) {
        return repository.findById(id)
                .orElseThrow(EntityNotFoundException::new);
    }

    @Override
    public GuaranteeDto getById(Long id) {
        var guarantee = repository.findById(id).orElseThrow(EntityNotFoundException::new);
        var guaranteeDto = mapper.toGuaranteeDto(guarantee);
        return setUpdatedSignedUrls(guaranteeDto);
    }

    @Override
    public List<GuaranteeDto> getAllByPreliminaryInjunctionId(Long id) {
        List<Guarantee> guarantees = repository.findAllByPreliminaryInjunctionId(id);
        List<GuaranteeDto> dtos = new ArrayList<>();
        guarantees.forEach(guarantee -> dtos.add(mapper.toGuaranteeDto(guarantee)));
        dtos.forEach(this::setUpdatedSignedUrls);
        return dtos;
    }

    @Override
    public GuaranteeDto update(GuaranteeDto dto) {
        var currentGuarantee = getEntityById(dto.getId());
        var guarantee = mapper.toGuaranteeFromDto(dto, currentGuarantee);
        var updated = repository.save(setPartyProvidingGuaranteeDueToGuaranteeType(currentGuarantee, guarantee));
        return mapper.toGuaranteeDto(updated);
    }

    @Override
    public void delete(Long id) {
        var guarantee = getEntityById(id)
                .toBuilder()
                .isDeleted(true)
                .build();
        repository.save(guarantee);
    }

    private List<AttachmentDto> setSignedUrls(List<AttachmentDto> dtos){
        dtos.forEach(dto -> dto.setFileSignedUrl(attachmentService.getSignedUrl(dto)));
        return dtos;
    }

    private GuaranteeDto setUpdatedSignedUrls(GuaranteeDto dto){
        dto.setCashierReceiptAttachments(setSignedUrls(dto.getCashierReceiptAttachments()));
        dto.setPiMinutesAttachments(setSignedUrls(dto.getPiMinutesAttachments()));
        dto.setLetterOfGuaranteeAttachments(setSignedUrls(dto.getLetterOfGuaranteeAttachments()));
        dto.setProformaDebitLetterAttachments(setSignedUrls(dto.getProformaDebitLetterAttachments()));
        dto.setReceiptAttachments(setSignedUrls(dto.getReceiptAttachments()));
        return dto;
    }

    private Guarantee setPartyProvidingGuaranteeDueToGuaranteeType(Guarantee currentGuarantee,
                                                                   Guarantee guaranteeFromDto){
        if (!guaranteeFromDto.getGuaranteeType().equals(GuaranteeType.CASH)) {
            guaranteeFromDto.setPartyProvidingGuarantee(currentGuarantee.getPartyProvidingGuarantee());
        }
        return guaranteeFromDto;
    }
}
