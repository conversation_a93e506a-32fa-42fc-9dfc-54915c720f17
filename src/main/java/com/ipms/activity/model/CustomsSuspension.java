package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ipms.activity.enums.CustomsSuspensionsOperationType;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "customs_suspension")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class CustomsSuspension extends VersionedEntity {

    @Column
    private Long customsId;

    @Column
    private String decisionNumber;

    @Column
    private LocalDate notificationDate;

    @OneToOne(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinColumn(name = "activity_id", referencedColumnName = "id")
    @JsonBackReference
    @NotAudited
    private Activity activity;

    @Column
    private Boolean isSmuggling;

    @Column
    private CustomsSuspensionsOperationType operationType;

    @Column
    private String countryOfReciever;

    @Column
    private String countryOfSender;

    @Column
    private String countryOfTransit;

    @ManyToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinTable(
            name = "suspensions_sender",
            joinColumns = @JoinColumn(name = "custom_suspension_id"),
            inverseJoinColumns = @JoinColumn(name = "sender_id"))
    private List<CustomsSuspensionsSender> senders;

    @ManyToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinTable(
            name = "suspensions_receiver",
            joinColumns = @JoinColumn(name = "custom_suspension_id"),
            inverseJoinColumns = @JoinColumn(name = "receiver_id"))
    private List<CustomsSuspensionsReceiver> receivers;

    @ManyToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinTable(
            name = "suspensions_shipping",
            joinColumns = @JoinColumn(name = "custom_suspension_id"),
            inverseJoinColumns = @JoinColumn(name = "shipping_id"))
    private List<CustomsSuspensionsShipping> shippings;
}
