package com.ipms.document.converter;

import com.ipms.document.enums.DocumentType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class DocumentTypeConverter  implements AttributeConverter<DocumentType, String> {
    @Override
    public String convertToDatabaseColumn(DocumentType documentType) {
        return documentType == null ? null : documentType.getValue();
    }

    @Override
    public DocumentType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : DocumentType.getEnum(s);
    }
}
