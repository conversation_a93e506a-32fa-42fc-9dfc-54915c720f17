package com.ipms.document.converter;

import com.ipms.document.enums.MediaType;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class MediaTypeConverter implements AttributeConverter<MediaType, String> {

    @Override
    public String convertToDatabaseColumn(MediaType mediaType) {
        return mediaType == null ? null : mediaType.getValue();
    }

    @Override
    public MediaType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : MediaType.getEnum(s);
    }
}
