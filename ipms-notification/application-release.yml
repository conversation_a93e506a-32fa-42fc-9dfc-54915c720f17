eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:release-ipms-notification}.release-ipms-notification
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

kafka:
  server: kafka.kafka.svc.cluster.local:9092
  notification-topic: notificationReleaseTopic
  data-transfer-topic: dataTransferReleaseTopic

spring:
  datasource:
    url: ************************************************************************************
    username: ipms_release_user
    password: zwk9w4TpjzG7TVX9
  jpa:
    hibernate:
      ddl-auto: update
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk
        provider:
          keycloak:
            token-uri: http://keycloak.test/auth/realms/ipms-release/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-release
  auth-server-url: http://keycloak.test/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: L3jWA2DMxfvug33PtLfI6YDrlSbbdyTk