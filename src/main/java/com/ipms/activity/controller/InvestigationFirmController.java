package com.ipms.activity.controller;

import com.ipms.activity.dto.InvestigationFirmDto;
import com.ipms.activity.service.InvestigationFirmService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("investigation-firm")
public class InvestigationFirmController {
    private final InvestigationFirmService service;

    @GetMapping("/{id}")
    public BaseResponse<InvestigationFirmDto> getById(@PathVariable("id") Long id) {
        var dto = service.getById(id);
        return BaseResponse.<InvestigationFirmDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(dto)
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<InvestigationFirmDto> update(@PathVariable Long id,
                                                     @RequestBody @Valid InvestigationFirmDto investigationFirmDto) {
        var dto = service.update(investigationFirmDto, id);
        return BaseResponse.<InvestigationFirmDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(dto)
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/name")
    public BaseResponse<List<InvestigationFirmDto>> getByName(@RequestParam(name = "name") String name) {
        var firms = service.getByName(name);
        return BaseResponse.<List<InvestigationFirmDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(firms)
                .build();
    }

}
