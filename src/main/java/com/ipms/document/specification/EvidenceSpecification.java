package com.ipms.document.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.document.enums.EvidenceType;
import com.ipms.document.enums.MediaType;
import com.ipms.document.model.Evidence;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.*;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class EvidenceSpecification extends AbstractSpecification<Evidence> {

    private final List<String> evidenceTypes;
    private final List<String> mediaTypes;
    private final Long firmId;

    @Override
    public Predicate toPredicate(Root<Evidence> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        Join<Evidence, Long> firmsJoin = root.join("firms");
        Optional.ofNullable(evidenceTypes)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("evidenceType").in(evidenceTypes.stream().map(EvidenceType::valueOf).toList()))));
        Optional.ofNullable(mediaTypes)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("mediaType").in(mediaTypes.stream().map(MediaType::valueOf).toList()))));
        Optional.ofNullable(firmId)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(firmsJoin.in(value))));
        query.distinct(Boolean.TRUE);
        return predicate;
    }
}
