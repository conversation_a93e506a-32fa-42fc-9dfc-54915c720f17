package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.*;
import com.ipms.paramcommand.service.CustomsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("customs")
@Tag(name = "customs", description = "This endpoints contains execution office APIs")
public class CustomsController {
    private final CustomsService service;

    @PostMapping
    public BaseResponse<CustomsDto> save(@RequestBody CustomsDto customsDto) {
        return BaseResponse.<CustomsDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(customsDto))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping
    public BaseResponse<List<CustomsDto>> getAll() {
        return BaseResponse.<List<CustomsDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAll())
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<CustomsPageDto> findAll(CustomsFilterRequest filterRequest,
                                                @PathVariable int page,
                                                @PathVariable int size) {
        return BaseResponse.<CustomsPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.findAll(filterRequest, page, size))
                .build();
    }
}
