package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum InvestigationFirmResponseCode implements Code {

    NOT_FOUND(1000, "error.code.investigation_firm.not_found"),
    ALREADY_EXIST(1001, "error.code.investigation_firm.already_exist");

    private final Integer code;
    private final String messageKey;

    InvestigationFirmResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
