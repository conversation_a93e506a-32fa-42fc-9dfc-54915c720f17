package com.ipms.activity.specification;

import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.model.ComponentCourt;
import com.ipms.activity.model.SearchActivity;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class ComponentCourtSpecification extends AbstractSpecification<ComponentCourt> {
    private List<LocalDate> filingDates;
    private List<String> docketNos;
    private List<String> decisionNumbers;
    @Override
    public Predicate toPredicate(Root<ComponentCourt> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(docketNos)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("docketNo").in(value))));


        Optional.ofNullable(decisionNumbers)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("decisionNumber").in(value))));

        Optional.ofNullable(filingDates)
                .ifPresent(value -> {
                    if (value.size() == 2) {
                        predicate.getExpressions().add(cb.between(root.get("filingDate"), value.get(0), value.get(1)));
                    }
                });

        return predicate;
    }
}
