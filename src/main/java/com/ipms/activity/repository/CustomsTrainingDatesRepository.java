package com.ipms.activity.repository;

import com.ipms.activity.dto.ActivityId;
import com.ipms.activity.model.CustomsTrainingDate;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface CustomsTrainingDatesRepository extends PagingAndSortingRepository<CustomsTrainingDate, Long>, JpaSpecificationExecutor<CustomsTrainingDate> {

    List<ActivityId> findByRegionalCustoms(Long customsId);

}
