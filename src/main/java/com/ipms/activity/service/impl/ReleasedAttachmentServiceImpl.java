package com.ipms.activity.service.impl;

import com.ipms.activity.dto.AttachmentDto;
import com.ipms.activity.dto.PIAttachmentDto;
import com.ipms.activity.exception.AttachmentNotFoundException;
import com.ipms.activity.mapper.AttachmentMapper;
import com.ipms.activity.model.PreliminaryInjunction;
import com.ipms.activity.model.ReleasedAttachment;
import com.ipms.activity.repository.ReleasedAttachmentRepository;
import com.ipms.activity.service.ReleasedAttachmentService;
import com.ipms.activity.util.ActivityUtils;
import com.ipms.config.storage.service.StorageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class ReleasedAttachmentServiceImpl extends AbstractAttachmentService implements ReleasedAttachmentService {
    private final ReleasedAttachmentRepository repository;

    public ReleasedAttachmentServiceImpl(StorageService storageService, AttachmentMapper mapper, ReleasedAttachmentRepository repository) {
        super(storageService, mapper);
        this.repository = repository;
    }

    @Override
    public String getSignedUrl(Long id) {
        var attachmentDto = getById(id);
        return getSignedUrl(attachmentDto);
    }

    public AttachmentDto getById(Long id) {
        return mapper.toAttachmentDto(getAttachmentById(id));
    }

    private ReleasedAttachment getAttachmentById(Long id) {
        return repository.findById(id)
                .orElseThrow(AttachmentNotFoundException::new);
    }

    @Override
    public AttachmentDto save(PIAttachmentDto piAttachmentDto) {
        piAttachmentDto.setFileUniqueName(ActivityUtils.prepareAttachmentName(piAttachmentDto.getFileName()));
        piAttachmentDto.setFileSignedUrl(getSignedUrl(piAttachmentDto));
        var attachment =  mapper.toReleasedAttachment(piAttachmentDto);
        var save = repository.save(attachment);
        return mapper.toAttachmentDto(save, piAttachmentDto.getFileSignedUrl());
    }

    @Override
    public void delete(Long id) {
        var attachment = getAttachmentById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(attachment);
    }

    @Override
    public void saveAttachments(List<AttachmentDto> updatedAttachments, PreliminaryInjunction preliminaryInjunction) {
        if (updatedAttachments != null) {
            var existingAttachments = preliminaryInjunction.getReleasedAttachments();
            deleteAttachments(updatedAttachments, existingAttachments);
            updatedAttachments.forEach(attachmentDto -> {
                if (attachmentDto.getId() != null) {
                    updateExistingAttachment(preliminaryInjunction, attachmentDto);
                } else {
                    var attachment = mapper.toReleasedAttachment(attachmentDto);
                    attachment.setPreliminaryInjunction(preliminaryInjunction);
                    existingAttachments.add(attachment);
                }
            });
        }
    }

    @Override
    public void setFileSignedUrls(List<AttachmentDto> attachmentDtos) {
        attachmentDtos.forEach(this::setSignedUrl);
    }

    public void updateExistingAttachment(PreliminaryInjunction preliminaryInjunction,
                                         AttachmentDto attachmentDto) {
        var existingAttachment = preliminaryInjunction.getReleasedAttachments().stream()
                .filter(attachment -> attachment.getId().equals(attachmentDto.getId()))
                .findFirst()
                .orElse(repository.findById(attachmentDto.getId()).orElseThrow(AttachmentNotFoundException::new));
        existingAttachment.setPreliminaryInjunction(preliminaryInjunction);
        var attachment = mapper.toReleasedAttachmentFromDto(attachmentDto, existingAttachment);
        preliminaryInjunction.getReleasedAttachments().add(existingAttachment);
        repository.save(attachment);
    }

    private void deleteAttachments(List<AttachmentDto> attachmentDto,
                                   List<ReleasedAttachment> existingAttachments) {
        var attachmentToBeDeleted = getAttachmentsToBeDeleted(attachmentDto, existingAttachments);
        attachmentToBeDeleted.forEach(attachment -> repository.save(attachment.toBuilder().isDeleted(Boolean.TRUE).build()));
        existingAttachments.removeAll(attachmentToBeDeleted);
    }

    private List<ReleasedAttachment> getAttachmentsToBeDeleted(List<AttachmentDto> updatedAttachments, List<ReleasedAttachment> existingAttachment) {
        return existingAttachment.stream()
                .filter(attachment -> updatedAttachments.stream()
                        .filter(attachmentDto -> attachmentDto.getId() != null)
                        .noneMatch(attachmentDto -> Objects.equals(attachmentDto.getId(), attachment.getId())))
                .toList();
    }
}
