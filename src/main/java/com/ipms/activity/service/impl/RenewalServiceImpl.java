package com.ipms.activity.service.impl;

import com.ipms.activity.client.MatterClient;
import com.ipms.activity.client.ParamCommandClient;
import com.ipms.activity.dto.MatterDto;
import com.ipms.activity.dto.PeriodicOperationItemDto;
import com.ipms.activity.dto.trademark.MarkDto;
import com.ipms.activity.dto.trademark.RegistrationDto;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.PeriodicOperationBaseDateType;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.Renewal;
import com.ipms.activity.service.RenewalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class RenewalServiceImpl implements RenewalService {

    private final MatterClient matterClient;
    private final ParamCommandClient paramCommandClient;

    @Override
    public LocalDate getRenewalDate(Activity activity) {
        var matterDto = getMatterDto(activity);
        var periodicOperations = getPeriodicOperations(matterDto);

        if (periodicOperations.isEmpty()) {
            return null;
        }

        var selectedOperation = findValidPeriodicOperation(periodicOperations, matterDto);
        return calculateRenewalDate(selectedOperation, matterDto);
    }

    private MatterDto getMatterDto(Activity activity) {
        return matterClient.getById(activity.getMatterId()).getPayload();
    }

    private List<PeriodicOperationItemDto> getPeriodicOperations(MatterDto matterDto) {
        var countryId = matterDto.getMark().getCountryId();
        var ipOfficeId = matterDto.getMark().getIpOfficeId();

        var response = (ipOfficeId == null) ?
                paramCommandClient.getPeriodicOperationByCountry(countryId) :
                paramCommandClient.getPeriodicOperationByIpOffice(ipOfficeId);

        return response.getPeriodicOperations();
    }

    private PeriodicOperationItemDto findValidPeriodicOperation(List<PeriodicOperationItemDto> operations, MatterDto matterDto) {
        if (operations.size() == 1) {
            return operations.get(0);
        }

        return selectFromMultipleOperations(operations, matterDto);
    }

    private PeriodicOperationItemDto selectFromMultipleOperations(List<PeriodicOperationItemDto> operations, MatterDto matterDto) {
        var validOperation = findValidOperationWithConditionDate(operations, matterDto);

        return validOperation != null ? validOperation : findOperationWithoutConditionDate(operations);
    }

    private PeriodicOperationItemDto findValidOperationWithConditionDate(List<PeriodicOperationItemDto> operations, MatterDto matterDto) {
        return operations.stream()
                .filter(this::hasConditionDate)
                .filter(op -> isOperationValid(op, matterDto))
                .max(Comparator.comparing(PeriodicOperationItemDto::getConditionOfValidityDate))
                .orElse(null);
    }

    private PeriodicOperationItemDto findOperationWithoutConditionDate(List<PeriodicOperationItemDto> operations) {
        return operations.stream()
                .filter(op -> !hasConditionDate(op))
                .findFirst()
                .orElse(null);
    }

    private boolean hasConditionDate(PeriodicOperationItemDto operation) {
        return operation.getConditionOfValidityDate() != null;
    }

    private boolean isOperationValid(PeriodicOperationItemDto operation, MatterDto matterDto) {
        var baseDate = calculateBaseDate(operation, matterDto);
        return baseDate != null && baseDate.isAfter(operation.getConditionOfValidityDate());
    }

    private LocalDate calculateRenewalDate(PeriodicOperationItemDto operation, MatterDto matterDto) {
        if (operation == null) return null;

        var baseDate = calculateBaseDate(operation, matterDto);
        if (baseDate == null) return null;

        return switch (operation.getPeriod()) {
            case YEAR -> baseDate.plusYears(operation.getValue());
            case MONTH -> baseDate.plusMonths(operation.getValue());
        };
    }

    private LocalDate calculateBaseDate(PeriodicOperationItemDto operation, MatterDto matterDto) {
        var applicationDate = getApplicationDate(matterDto);
        return extractBaseDate(operation.getBaseDate(), applicationDate);
    }

    private LocalDate getApplicationDate(MatterDto matterDto) {
        return Optional.ofNullable(matterDto.getMark())
                .map(MarkDto::getRegistration)
                .map(RegistrationDto::getApplicationDate)
                .orElse(null);
    }

    private LocalDate extractBaseDate(PeriodicOperationBaseDateType baseDateType, LocalDate applicationDate) {
        return switch (baseDateType) {
            case APPLICATION_DATE -> applicationDate;
        };
    }

    @Override
    public Activity createRenewalActivityFromRegistrationActivity(Activity registrationActivity) {
        return Activity.builder()
                .type(ActivityType.RENEWAL)
                .status(ActivityStatus.PENDING)
                .matterId(registrationActivity.getMatterId())
                .firmId(registrationActivity.getFirmId())
                .billingAccountId(registrationActivity.getBillingAccountId())
                .agentReference(registrationActivity.getAgentReference())
                .localAgent(registrationActivity.getLocalAgent())
                .renewal(Renewal.builder()
                        .bulletinNumber(registrationActivity.getRegistration().getBulletinNumber())
                        .bulletinPublicationDate(registrationActivity.getRegistration().getBulletinPublicationDate())
                        .decisionDate(registrationActivity.getRegistration().getDecisionDate())
                        .notificationDate(registrationActivity.getRegistration().getNotificationDate())
                        .renewalDate(getRenewalDate(registrationActivity))
                        .build())
                .build();
    }
}