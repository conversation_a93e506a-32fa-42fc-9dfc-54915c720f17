package com.ipms.activity.converter;

import com.ipms.activity.enums.ActivityType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class ActivityTypeConverter implements AttributeConverter<ActivityType, String> {
    @Override
    public String convertToDatabaseColumn(ActivityType activityType) {
        return activityType == null ? null : activityType.getValue();
    }

    @Override
    public ActivityType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : ActivityType.getEnum(s);
    }
}
