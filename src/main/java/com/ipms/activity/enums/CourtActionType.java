package com.ipms.activity.enums;

import java.util.Arrays;

public enum CourtActionType {

    NONINFRINGEMENT_ACTION_FILED_BY_PARTY("01"),
    RECEIVABLE_ARISING_FROM_THE_CONTRACT_RELATED_TO_INTELLECTUAL_AND_ARTISTIC_WORK("02"),
    GEOGRAPHICAL_INDICATIONS_CANCELLATION_OF_ADMINISTRATIVE_DECISION_ON_GEOGRAPHICAL_INDICATION("03"),
    GEOGRAPHICAL_INDICATIONS_ARISING_FROM_THE_INVALIDITY_OF_THE_GEOGRAPHICAL_INDICATION("04"),
    GEOGRAPHIC<PERSON>_INDICATIONS_ARISING_FROM_INFRINGEMENT_OF_RIGHTS_ARISING_FROM_GEOGRAPHICAL_INDICATIONS("05"),
    GEOGRAPHICAL_INDICATIONS_WITH_REQUEST_OF_MATERIAL_COMPENSATION("06"),
    GEOGRAPHICAL_INDICATIONS_INVALIDITY_CLAIMS_FILED_BY_PUBLIC_AUTHORITIES("07"),
    GEOGRAPHICAL_INDICATIONS_CLAIMS_ARISING_FROM_CONTRACTUAL_RIGHTS("08"),
    GEOGRAPHICAL_INDICATIONS_WITH_REQUEST_FOR_ELIMINATION_OF_INFRINGEMENT("09"),
    GEOGRAPHICAL_INDICATIONS_WITH_REQUEST_FOR_PREVENTION_OF_INFRINGEMENT("10"),
    GEOGRAPHICAL_INDICATIONS_WITH_REQUEST_OF_DETERMINATION_OF_THE_INFRINGEMENT("11"),
    INDUSTRIAL_DESIGN_REQUEST_FOR_DETERMINATION_OF_EVIDENCE("12"),
    INDUSTRIAL_DESIGN_ARISING_FROM_THE_INFRINGMENT_OF_THE_INDUSTRIAL_DESIGN_RIGHTS("13"),
    INDUSTRIAL_DESIGN_ARISING_FROM_THE_INVALIDITY_OF_THE_INDUSTRIAL_DESIGN_RIGHTS("14"),
    INDUSTRIAL_DESIGN_CANCELLATION_OF_ADMINISTRATIVE_DECISION_ON_THE_INDUSTRIAL_DESIGN("15"),
    INDUSTRIAL_DESIGN_WITH_REQUEST_OF_MATERIAL_COMPENSATION("16"),
    INDUSTRIAL_DESIGN_WITH_REQUEST_OF_MORAL_COMPENSATION("17"),
    INDUSTRIAL_DESIGN_INVALIDITY_CLAIMS_FILED_BY_PUBLIC_AUTHORITIES("18"),
    INDUSTRIAL_DESIGN_CLAIMS_ARISING_FROM_CONTRACTUAL_RIGHTS("19"),
    INDUSTRIAL_DESIGN_BASED_ON_THE_ALLEGED_USURPATION_OF_DESIGN("20"),
    INDUSTRIAL_DESIGN_CLAIM_FOR_COMPENSATION_FOR_REPUTATIONAL_DAMAGES_OF_THE_DESIGN("21"),
    INDUSTRIAL_DESIGN_WITH_REQUEST_FOR_ELIMINATION_OF_INFRINGEMENT("22"),
    INDUSTRIAL_DESIGN_WITH_REQUEST_OF_DETERMINATION_OF_NONINFRINGEMENT("23"),
    INDUSTRIAL_DESIGN_WITH_REQUEST_FOR_PREVENTION_OF_INFRINGEMENT("24"),
    INDUSTRIAL_DESIGN_WITH_REQUEST_OF_DETERMINATION_OF_THE_INFRINGEMENT("25"),
    INDUSTRIAL_DESIGN_WITH_REQUEST_FOR_COMPULSORY_LICENCE_AND_ACCEPTANCE_OF_PREUSE_RIGHTS("26"),
    INTEGRATED_CIRCUIT_TOPOGRAPHIES_CANCELLATION_OF_ADMINISTRATIVE_DECISION_ON_THE_INTEGRATED_CIRCUIT_TOPOGRAPHIES("27"),
    INTEGRATED_CIRCUIT_TOPOGRAPHIES_ARISING_FROM_INFRINGEMENT_OF_INTEGRATED_CIRCUIT_TOPOGRAPHIES("28"),
    INTEGRATED_CIRCUIT_TOPOGRAPHIES_ARISING_FROM_INVALIDITY_OF_INTEGRATED_CIRCUIT_TOPOGRAPHIES("29"),
    UTILITY_MODEL_REQUEST_FOR_DETERMINATION_OF_EVIDENCE("30"),
    UTILITY_MODEL_CLAIM_FOR_COMPENSATION_FOR_REPUTATIONAL_DAMAGES_OF_THE_UTILITY_MODEL("31"),
    UTILITY_MODEL_ARISING_FROM_THE_INFRINGMENT_OF_THE_UTILITY_MODEL_RIGHT("32"),
    UTILITY_MODEL_CLAIM_OF_USURPATION_OF_UTILITY_MODEL_AND_THE_RIGHT_TO_REQUEST_UTILITY_MODEL("33"),
    UTILITY_MODEL_WITH_REQUEST_OF_MATERIAL_COMPENSATION("34"),
    UTILITY_MODEL_WITH_REQUEST_OF_MORAL_COMPENSATION("35"),
    UTILITY_MODEL_INVALIDITY_CLAIMS_FILED_BY_PUBLIC_AUTHORITIES("36"),
    UTILITY_MODEL_CLAIMS_ARISING_FROM_CONTRACTUAL_RIGHTS("37"),
    UTILITY_MODEL_WITH_REQUEST_OF_DETERMINATION_OF_NONINFRINGEMENT("38"),
    UTILITY_MODEL_WITH_REQUEST_FOR_PREVENTION_OF_INFRINGEMENT("39"),
    UTILITY_MODEL_WITH_REQUEST_OF_DETERMINATION_OF_THE_INFRINGEMENT("40"),
    UTILITY_MODEL_CANCELLATION_OF_DECISION_OF_THE_RE_EXAMINATION_AND_EVALUATION_BOARD("41"),
    UTILITY_MODEL_REQUEST_FOR_COMPULSORY_LICENCE_AND_PREUSE_RIGHT("42"),
    UTILITY_MODEL_REQUEST_FOR_DETERMINATION_OF_COMPULSORY_LICENCE_FEE("43"),
    INTELLECTUAL_AND_ARTISTIC_WORK_WITH_REQUEST_OF_MATERIAL_COMPENSATION("44"),
    INTELLECTUAL_AND_ARTISTIC_WORK_WITH_REQUEST_OF_MORAL_COMPENSATION("45"),
    INTELLECTUAL_AND_ARTISTIC_WORK_WITH_REQUEST_FOR_PREVENTION_OF_INFRINGEMENT("46"),
    INTELLECTUAL_AND_ARTISTIC_WORK_REQUEST_FOR_TRANSFER_OF_PROFIT_ACQUIRED("47"),
    REQUEST_OF_REMEDY_PREVENTION_AND_COMPENSATION("48"),
    APPOINMENT_OF_ARBITRATOR("49"),
    CHALLENGE_OF_ARBITRATOR("50"),
    RESTITUTION_CLAIM("51"),
    CANCELLATION_OF_OBJECTION_ARISING_FROM_INTELLECTUAL_AND_ARTISTIC_WORKS_CONTRACT("52"),
    TRADEMARK_WITH_REQUEST_OF_DETERMINATION_OF_EVIDENCE("53"),
    TRADEMARK_WITH_REQUEST_OF_MATERIAL_COMPENSATION("54"),
    TRADEMARK_WITH_REQUEST_OF_MORAL_COMPENSATION("55"),
    TRADEMARK_ARISING_FROM_THE_INFRINGMENT_OF_THE_TRADEMARK_RIGHTS("56"),
    TRADEMARK_ARISING_FROM_THE_INVALIDITY_OF_THE_TRADEMARK("57"),
    TRADEMARK_CANCELLATION_OF_ADMINISTRATIVE_DECISIONS_ON_TRADEMARK("58"),
    TRADEMARK_CLAIM_FOR_COMPENSATION_FOR_REPUTATIONAL_LOSS_OF_THE_TRADEMARK("59"),
    TRADEMARK_INVALIDITY_CLAIMS_FILED_BY_PUBLIC_AUTHORITIES("60"),
    TRADEMARK_CLAIMS_ARISING_FROM_CONTRACTUAL_RIGHTS("61"),
    TRADEMARK_WITH_REQUEST_FOR_DETERMINATION_OF_WELLKNOWN_TRADEMARK("62"),
    TRADEMARK_REQUEST_OF_REMEDY_FOR_INFRINGEMENT("63"),
    TRADEMARK_REQUEST_FOR_DETECTION_OF_THE_INFRINGEMENT("64"),
    NONINFRINGEMENT_ACTION_ARISING_FROM_INTELLECTUAL_AND_ARTISTIC_WORKS_CONTRACT("65"),
    PATENT_CLAIM_FOR_COMPENSATION_FOR_REPUTATIONAL_LOSS_OF_THE_INVENTION("66"),
    PATENT_REQUEST_FOR_DETERMINATION_OF_EVIDENCE("67"),
    PATENT_REQUEST_FOR_DETERMINATION_OF_LICENSE_FEE("68"),
    PATENT_WITH_REQUEST_OF_MATERIAL_COMPENSATION("69"),
    PATENT_WITH_REQUEST_OF_MORAL_COMPENSATION("70"),
    PATENT_ARISING_FROM_THE_INFRINGEMENT_OF_THE_PATENT_RIGHT("71"),
    PATENT_ARISING_FROM_THE_INVALIDITY_OF_THE_PATENT("72"),
    PATENT_CANCELLATION_OF_ADMINISTRATIVE_DECISION_ON_PATENT("73"),
    PATENT_CLAIM_OF_INFRINGEMENT_OF_THE_RIGHT_TO_REQUEST_A_PATENT_AND_USURPATION_OF_THE_PATENT("74"),
    PATENT_INVALIDITY_CLAIMS_FILED_BY_PUBLIC_AUTHORITIES("75"),
    PATENT_CLAIMS_ARISING_FROM_CONTRACTUAL_RIGHTS("76"),
    PATENT_REQUEST_OF_REMEDY_FOR_INFRINGEMENT("77"),
    PATENT_REQUEST_FOR_COMPULSORY_LICENCE_AND_PRE_USE_RIGHT("78"),
    ADAPTATION_OF_THE_CONTRACT("79"),
    RECOGNITION_AND_ENFORCEMENT("80"),
    COMPENSATION_ARISING_FROM_INTELLECTUAL_AND_ARTISTIC_WORKS_CONTRACT("81"),
    COMPENSATION_ARISING_FROM_OWNERSHIP_OF_INTELLECTUAL_AND_ARTISTIC_WORKS("82"),
    COMPENSATION_ARISING_FROM_UNFAIR_LIEN("83"),
    COMPENSATION_ARISING_FROM_UNFAIR_PRELIMINARY_INJUNCTION("84"),
    COMPENSATION_ARISING_FROM_A_CONTRACT("85"),
    RETRIAL("86"),
    NEW_PLANT_VARIETIES_CANCELLATION_OF_ADMINISTRATIVE_DECISIONS_ON_NEW_PLANT_VARIETIES("87"),
    INFRINGEMENT_OF_NEW_PLANT_VARIETIES("88"),
    NEW_PLANT_VARIETIES_ARISING_FROM_THE_INVALIDITY_OF_NEW_PLANT_VARIETIES("89");

    private final String value;


    CourtActionType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static CourtActionType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }

}
