package com.ipms.activity.controller;

import com.ipms.activity.dto.GuaranteeDto;
import com.ipms.activity.service.GuaranteeService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Positive;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("guarantees")
public class GuaranteesController {

    private final GuaranteeService service;

    @PostMapping("/")
    public BaseResponse<GuaranteeDto> save(@RequestBody @Valid GuaranteeDto dto) {
        return BaseResponse.<GuaranteeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/update")
    public BaseResponse<GuaranteeDto> update(@RequestBody @Valid GuaranteeDto dto) {
        return BaseResponse.<GuaranteeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(dto))
                .build();
    }

    @GetMapping("/get/{id}")
    public BaseResponse<GuaranteeDto> getById(@PathVariable Long id) {
        return BaseResponse.<GuaranteeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(id))
                .build();
    }

    @GetMapping("/get-all-by-pi-id/{id}")
    public BaseResponse<List<GuaranteeDto>> getAllByPreliminaryInjunctionId(@PathVariable Long id) {
        return BaseResponse.<List<GuaranteeDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByPreliminaryInjunctionId(id))
                .build();
    }

    @GetMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable @Positive Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
