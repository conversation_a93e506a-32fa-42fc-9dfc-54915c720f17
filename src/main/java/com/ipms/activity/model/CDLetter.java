package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ipms.activity.enums.CDNotificationType;
import com.ipms.activity.enums.CDPosition;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "c_d_Letter")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class CDLetter extends VersionedEntity {
    @Column
    private CDPosition ourPosition;
    @Column
    private LocalDate date;
    @Column
    private LocalDate notificationDate;
    @Column
    private CDNotificationType notificationType;
    @Column
    private String officialDocumentNumber;
    @Column(columnDefinition = "TEXT")
    private String notaryNameNumber;
    @Column(columnDefinition = "TEXT")
    private String postalTrackingNumber;

    @OneToOne(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinColumn(name = "activity_id", referencedColumnName = "id")
    @JsonBackReference
    @NotAudited
    private Activity activity;
}
