package com.ipms.paramcommand.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.paramcommand.dto.ActivityStatusDto;
import com.ipms.paramcommand.dto.ActivityStatusPageDto;
import com.ipms.paramcommand.service.ActivityStatusService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RequiredArgsConstructor
@RestController
@RequestMapping("activity-status")
@Tag(name = "activity-status", description = "This endpoints contains activity-type APIs")
public class ActivityStatusController {
    private final ActivityStatusService service;

    @PostMapping
    public BaseResponse<ActivityStatusDto> save(@Valid @RequestBody ActivityStatusDto dto) {
        return BaseResponse.<ActivityStatusDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/{typeId}/{page}/{size}")
    public BaseResponse<ActivityStatusPageDto> getAll(@PathVariable Long typeId,
                                                            @PathVariable int page,
                                                            @PathVariable int size){
        var pageDto= service.getAll(typeId, page, size);
        return BaseResponse.<ActivityStatusPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(pageDto)
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id){
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
