package com.ipms.document.service;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.storage.model.StorageFile;
import com.ipms.config.storage.service.StorageService;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.document.client.ActivityClient;
import com.ipms.document.dto.*;
import com.ipms.document.enums.DelegationPowerType;
import com.ipms.document.enums.DocumentResult;
import com.ipms.document.enums.DocumentType;
import com.ipms.document.enums.POALimited;
import com.ipms.document.exception.DocumentCannotRemoveException;
import com.ipms.document.exception.DocumentNotFoundException;
import com.ipms.document.mapper.DocumentMapper;
import com.ipms.document.mapper.DocumentMapperImpl;
import com.ipms.document.model.Attachment;
import com.ipms.document.model.Document;
import com.ipms.document.repository.DocumentRepository;
import com.ipms.document.service.impl.DocumentServiceImpl;
import com.ipms.document.specification.DocumentSpecification;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class DocumentServiceTest {
    @MockBean
    private DocumentService service;

    @Mock
    private DocumentRepository repository;

    @Mock
    private StorageService storageService;

    @Mock
    private ActivityClient activityClient;

    @Mock
    private ProducerService producerService;

    private final DocumentMapper mapper = new DocumentMapperImpl();

    @Before
    public void setUp() {
        service = new DocumentServiceImpl(repository, mapper, activityClient, storageService, producerService);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
    }

    @Test
    public void whenSave_thenReturnDocumentDtoWithSignedUrl() {
        var documentCreateDto = DocumentCreateDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .activityId(1L)
                .contentType("application/pdf")
                .fileName("example.pdf")
                .fileUniqueName("unique_name")
                .poaLimited(POALimited.FILE_BASED.name())
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER.name())
                .firms(Arrays.asList(1L, 2L, 3L))
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2023, 12, 31))
                .notificationDate(LocalDate.of(2023, 12, 28))
                .result(DocumentResult.UNFAVORABLE)
                .build();
        Document document = Document.builder()
                .type(DocumentType.AGREEMENT)
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();
        when(repository.save(Mockito.any(Document.class)))
                .thenReturn(document);
        when(storageService.generateSas(Mockito.any(StorageFile.class)))
                .thenReturn("http://test.com");
        when(activityClient.addDocument(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyBoolean()))
                .thenReturn(ActivityResponse.builder()
                        .code(1)
                        .payload(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build())
                        .build());
        assertThat(service.save(documentCreateDto).getAttachments().get(0).getFileSignedUrl())
                .isEqualTo("http://test.com");
    }

    @Test
    public void givenManual_whenSave_thenReturnDocumentDto() {
        var documentManualDto = DocumentManualDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .activityId(1L)
                .poaLimited(POALimited.FILE_BASED.name())
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER.name())
                .firms(Arrays.asList(1L, 2L, 3L))
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2023, 12, 31))
                .notificationDate(LocalDate.of(2023, 12, 28))
                .result(DocumentResult.UNFAVORABLE)
                .build();
        Document document = Document.builder()
                .id(1L)
                .type(DocumentType.AGREEMENT)
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();

        when(repository.save(Mockito.any(Document.class)))
                .thenReturn(document);
        when(activityClient.addDocument(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyBoolean()))
                .thenReturn(ActivityResponse.builder()
                        .code(1)
                        .payload(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build())
                        .build());
        assertThat(service.save(documentManualDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenManualWithExperts_whenSave_thenReturnDocumentDto() {
        var documentManualDto = generateDummyDocumentManualDto();
        documentManualDto.setExperts(Arrays.asList(1L, 2L));
        documentManualDto.setResult(DocumentResult.UNFAVORABLE);
        var document = generateDummyDocument();
        document.setExperts(Arrays.asList(1L, 2L));

        when(repository.save(Mockito.any(Document.class)))
                .thenReturn(document);
        when(activityClient.addDocument(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyBoolean()))
                .thenReturn(ActivityResponse.builder()
                        .code(1)
                        .payload(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build())
                        .build());
        assertThat(service.save(documentManualDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenCreateDto_whenSave_thenReturnDocumentDtoWithSignedUrl() {
        var documentCreateDto = generateDummyDocumentCreateDto();
        documentCreateDto.setExperts(Arrays.asList(1L, 2L));
        documentCreateDto.setResult(DocumentResult.UNFAVORABLE);
        var document = generateDummyDocumentWithAttachment();
        document.setExperts(Arrays.asList(1L, 2L));

        when(repository.save(Mockito.any(Document.class)))
                .thenReturn(document);
        when(storageService.generateSas(Mockito.any(StorageFile.class)))
                .thenReturn("http://test.com");
        when(activityClient.addDocument(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyBoolean()))
                .thenReturn(ActivityResponse.builder()
                        .code(1)
                        .payload(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build())
                        .build());
        assertThat(service.save(documentCreateDto).getAttachments().get(0).getFileSignedUrl())
                .isEqualTo("http://test.com");
    }

    @Test
    public void whenUpdate_thenReturnDocumentDto() {
        Document document = Document.builder()
                .type(DocumentType.AGREEMENT)
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(new ArrayList<>(Arrays.asList(
                        Attachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                        .thenReturn(Optional.of(document));
        Mockito.when(repository.save(Mockito.any(Document.class)))
                .thenReturn(document);
        Mockito.when(storageService.generateSas(Mockito.any(StorageFile.class)))
                .thenReturn("http://test.com");

        var documentDto = DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .poaLimited(POALimited.FILE_BASED.name())
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .result(DocumentResult.UNFAVORABLE)
                .attachments(new ArrayList<>(Arrays.asList(
                        AttachmentDto.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build())))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
        assertThat(service.update(documentDto, 1L).getAttachments().get(0).getFileSignedUrl())
                .isEqualTo("http://test.com");

    }

    @Test
    public void whenGetById_thenThrowDocumentNotFoundException() {
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(() -> service.getById(1L));
        assertThat(thrown).isInstanceOf(DocumentNotFoundException.class);
    }

    @Test
    public void whenGetByFileUniqueName_thenThrowDocumentNotFoundException() {
        when(repository.findFirstByAttachmentsFileUniqueName(Mockito.anyString()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(() -> service.getByFileUniqueName("name"));
        assertThat(thrown).isInstanceOf(DocumentNotFoundException.class);
    }

    @Test
    public void givenDocument_whenGetByFileUniqueName_thenReturnDocumentDto() {
        Document document = Document.builder()
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .type(DocumentType.AGREEMENT)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();
        when(repository.findFirstByAttachmentsFileUniqueName(Mockito.anyString()))
                .thenReturn(Optional.of(document));
        assertThat(service.getByFileUniqueName("test.text")).isNotNull();
    }

    @Test
    public void givenDocument_whenGetById_thenReturnDocumentDto() {
        Document document = Document.builder()
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .type(DocumentType.AGREEMENT)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(document));
        assertThat(service.getById(1L)).isNotNull();
    }

    @Test
    public void whenGetByFileUniqueName_thenReturnDocumentDtoList() {
        Document document = Document.builder()
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .type(DocumentType.AGREEMENT)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();
        when(repository.findByAttachmentsFileUniqueNameIn(Mockito.anyList()))
                .thenReturn(List.of(document));
        assertThat(service.getByFileUniqueName(List.of("test.text"))).isNotEmpty();
    }

    @Test
    public void whenGetByIdIn_thenReturnDocumentDtoList() {
        Document document = Document.builder()
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .type(DocumentType.AGREEMENT)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(document));
        assertThat(service.getByIdIn(List.of(1L))).isNotEmpty();
    }

    @Test
    public void givenLinkedDocument_whenDelete_thenThrowDocumentCannotRemoveException() {
        when(activityClient.checkDocumentLinked(Mockito.anyLong()))
                .thenReturn(ActivityLinkedResponse.builder()
                        .payload(List.of(ActivityResponse.Payload.builder()
                                .id(1L)
                                .build()))
                        .build());
        Throwable thrown = catchThrowable(() -> service.delete(1L, 0L));
        assertThat(thrown).isInstanceOf(DocumentCannotRemoveException.class);
    }

    @Test
    public void whenDelete_thenThrowDoesNotThrowAnyException() {
        when(activityClient.checkDocumentLinked(Mockito.anyLong()))
                .thenReturn(ActivityLinkedResponse.builder()
                        .payload(List.of())
                        .build());
        Document document = Document.builder()
                .type(DocumentType.AGREEMENT)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("aexample.txt")
                                .fileUniqueName("a123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(document));
        assertThatCode(() -> service.delete(1L, 0L))
                .doesNotThrowAnyException();
    }


    @Test
    public void whenGetAll_thenReturnDocumentDtoList() {
        Page<Document> page = new Page<>() {
            @Override
            public int getTotalPages() {
                return 1;
            }

            @Override
            public long getTotalElements() {
                return 1;
            }

            @Override
            public <U> Page<U> map(Function<? super Document, ? extends U> converter) {
                return null;
            }

            @Override
            public int getNumber() {
                return 0;
            }

            @Override
            public int getSize() {
                return 1;
            }

            @Override
            public int getNumberOfElements() {
                return 0;
            }

            @Override
            public List<Document> getContent() {
                return List.of(Document.builder()
                        .type(DocumentType.AGREEMENT)
                        .date(LocalDate.of(2023, 12, 31))
                        .expiryDate(LocalDate.of(2025, 12, 31))
                        .notificationDate(LocalDate.of(2025, 12, 1))
                        .attachments(Collections.singletonList(
                                Attachment.builder()
                                        .fileName("example.txt")
                                        .fileUniqueName("a123456")
                                        .contentType("text/plain")
                                        .build()))
                        .firms(Arrays.asList(1L, 2L, 3L))
                        .build());
            }

            @Override
            public boolean hasContent() {
                return false;
            }

            @Override
            public Sort getSort() {
                return null;
            }

            @Override
            public boolean isFirst() {
                return false;
            }

            @Override
            public boolean isLast() {
                return false;
            }

            @Override
            public boolean hasNext() {
                return false;
            }

            @Override
            public boolean hasPrevious() {
                return false;
            }

            @Override
            public Pageable nextPageable() {
                return null;
            }

            @Override
            public Pageable previousPageable() {
                return null;
            }

            @Override
            public Iterator<Document> iterator() {
                return null;
            }
        };
        when(repository.findAll(Mockito.any(DocumentSpecification.class), Mockito.any(Pageable.class)))
                .thenReturn(page);
        when(storageService.generateSas(Mockito.any(StorageFile.class)))
                .thenReturn("http://test.com");
        var request = DocumentFilterRequest.builder()
                .ids(List.of(1L, 2L))
                .types(List.of(DocumentType.AGREEMENT.name()))
                .build();
        assertThat(service.getAll(request, 0, 15).getDocuments()).isNotEmpty();
    }

    @Test
    public void whenProcessUpdatedEvent_thenDoesNotThrowAnyException() {
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        var event = DocumentEvent.builder()
                .id(1L)
                .build();
        var document = new Document();
        when(repository.findById(anyLong())).thenReturn(Optional.of(document));

        assertThatCode(() -> service.processUpdatedEvent(event))
                .doesNotThrowAnyException();
    }

    private DocumentManualDto generateDummyDocumentManualDto() {
        return DocumentManualDto.builder()
                .type(DocumentType.EXPERTS_REPORT.name())
                .activityId(1L)
                .poaLimited(POALimited.FILE_BASED.name())
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER.name())
                .firms(Arrays.asList(1L, 2L, 3L))
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2023, 12, 31))
                .notificationDate(LocalDate.of(2023, 12, 28))
                .build();
    }

    private Document generateDummyDocument() {
        return Document.builder()
                .id(1L)
                .type(DocumentType.EXPERTS_REPORT)
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();
    }

    private DocumentCreateDto generateDummyDocumentCreateDto() {
        return DocumentCreateDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .activityId(1L)
                .contentType("application/pdf")
                .fileName("example.pdf")
                .fileUniqueName("unique_name")
                .poaLimited(POALimited.FILE_BASED.name())
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER.name())
                .firms(Arrays.asList(1L, 2L, 3L))
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2023, 12, 31))
                .notificationDate(LocalDate.of(2023, 12, 28))
                .build();
    }

    private Document generateDummyDocumentWithAttachment() {
        return Document.builder()
                .type(DocumentType.AGREEMENT)
                .poaLimited(POALimited.FILE_BASED)
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER)
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .attachments(Collections.singletonList(
                        Attachment.builder()
                                .fileName("example.txt")
                                .fileUniqueName("123456")
                                .contentType("text/plain")
                                .build()))
                .firms(Arrays.asList(1L, 2L, 3L))
                .build();
    }

    private DocumentDto generateDummyDocumentDto() {
        return DocumentDto.builder()
                .type(DocumentType.AGREEMENT.name())
                .poaLimited(POALimited.FILE_BASED.name())
                .delegationPowerType(DelegationPowerType.WITHOUT_DELEGATION_POWER.name())
                .date(LocalDate.of(2023, 12, 31))
                .expiryDate(LocalDate.of(2025, 12, 31))
                .notificationDate(LocalDate.of(2025, 12, 1))
                .firms(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .version(1L)
                .updatedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedBy("testuser")
                .createdBy("testuser")
                .build();
    }
}
