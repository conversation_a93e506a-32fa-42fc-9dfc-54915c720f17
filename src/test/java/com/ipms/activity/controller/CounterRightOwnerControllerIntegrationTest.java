package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.activity.dto.CounterRightOwnerDto;
import com.ipms.activity.service.CounterRightOwnerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class CounterRightOwnerControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private CounterRightOwnerService service;

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void givenCounterRightOwnerDto_whenSave_thenReturnBaseResponse() throws Exception {
        var counterRightOwnerDto = CounterRightOwnerDto.builder().name("test").id(1L).build();
        Mockito.when(service.save(Mockito.any(CounterRightOwnerDto.class)))
                .thenReturn(counterRightOwnerDto);
        mvc.perform(post("/counter-right-owner")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(counterRightOwnerDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenName_whenGetByName_thenReturnBaseResponse() throws Exception {
        var counterRightOwnerDto = CounterRightOwnerDto.builder().name("test").id(1L).build();
        Mockito.when(service.getByName(Mockito.anyString()))
                .thenReturn(List.of(counterRightOwnerDto));
        mvc.perform(get("/counter-right-owner/test-name")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
