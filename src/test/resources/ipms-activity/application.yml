eureka:
  client:
    enabled: false
server:
  servlet:
    context-path: /api/activity
keycloak:
  realm: ipms-default
  auth-server-url: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth
  ssl-required: external
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn

spring:
  cloud:
    config:
      enabled: false
  datasource:
    url: jdbc:h2:file:~/test
    username: sa
    password:
    driverClassName: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create
    show-sql: true
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth/realms/ipms-default/protocol/openid-connect/token

kafka:
  server: localhost:29092
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  issue-linked-topic: issueLinkedTopic
  document-updated-topic: documentUpdatedTopic
  firm-associated-topic: firmAssociatedTopic
  billing-order-integration-success-topic: billingOrderIntegrationSuccessTopic
  billing-order-integration-error-topic: billingOrderIntegrationErrorTopic
  invoice-updated-topic: invoiceUpdatedTopic
  billing-approved-topic: billingApprovedTopic
  invoice-is-ready-topic: invoiceIsReadyTopic
  risk-impact-update-topic: riskImpactUpdateTopic
  billing-issue-closed-topic: billingIssueClosedTopic
  close-issue-topic: closeIssueTopic
  matter-update-topic: matterUpdateTopic
  timesheet-changed-topic: timesheetChangedTopic
  matter-application-date-changed-topic: matterApplicationDateChangedTopic

deris:
  url: https://online.deris.com.tr

azure:
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=ipmstestsa;AccountKey=****************************************************************************************;
    container-name: ipmstestcontainer
    sas-host-url: https://ipmstestsa.blob.core.windows.net/ipmstestcontainer/

param:
  auto-billing-time: AUTO_BILLING_TIME
  auto-billing-month: AUTO_BILLING_MONTH
  before-billing-day: 4