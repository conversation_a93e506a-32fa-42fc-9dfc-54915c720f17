package com.ipms.activity.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "component_court")
@Entity
@Where(clause = "is_deleted='0'")
public class ComponentCourt extends VersionedEntity {
    @Column
    private Long activityId;

    @Column
    private Long courtId;

    @Column
    private LocalDate filingDate;

    @Column
    private String docketNo;

    @Column
    private LocalDate decisionNotificationDate;

    @Column
    private String decisionNumber;

    @Column
    private String judge;
}