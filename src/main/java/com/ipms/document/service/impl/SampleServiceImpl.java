package com.ipms.document.service.impl;

import com.ipms.config.storage.service.StorageService;
import com.ipms.document.client.ActivityClient;
import com.ipms.document.dto.*;
import com.ipms.document.exception.SampleCannotRemoveException;
import com.ipms.document.exception.SampleNotFoundException;
import com.ipms.document.mapper.SampleMapper;
import com.ipms.document.model.Sample;
import com.ipms.document.repository.SampleRepository;
import com.ipms.document.service.SampleService;
import com.ipms.document.specification.SampleSpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class SampleServiceImpl implements SampleService {

    private final SampleRepository repository;
    private final SampleMapper mapper;
    private final ActivityClient activityClient;
    private final StorageService storageService;

    @Override
    public SampleDto save(SampleManualDto documentManualDto) {
        var sample = mapper.toSample(documentManualDto);
        var saved = repository.save(sample);
        activityClient.addSample(documentManualDto.getActivityId(), saved.getId());
        return mapper.toDto(saved);
    }

    @Override
    public SampleDto update(SampleDto sampleDto, Long id) {
        var document = mapper.toSampleFromDto(sampleDto, getSampleById(id).toBuilder().build());
        var saved = repository.save(document);
        return toSampleDto(saved);
    }

    @Override
    public void delete(Long id, Long version) {
        activityClient.checkSampleLinked(id)
                .linked()
                .ifPresent(payload -> {throw new SampleCannotRemoveException();});
        var sample = getSampleById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .version(version)
                .build();
        repository.save(sample);
    }

    @Override
    public Sample getSampleById(Long id) {
        return repository.findById(id)
                .orElseThrow(SampleNotFoundException::new);
    }

    @Override
    public SampleDto getById(Long id) {
        var sample = getSampleById(id);
        return toSampleDto(sample);
    }

    @Override
    public List<SampleDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids)
                .stream()
                .map(this::toSampleDto)
                .toList();
    }

    @Override
    public SamplePageDto getAll(SampleFilterRequest filterRequest, int page, int size) {
        var samplePage = repository.findAll(SampleSpecification.builder()
                .ids(filterRequest.getIds())
                .firmId(filterRequest.getFirmId())
                .sampleTypes(filterRequest.getSampleTypes())
                .collectionPlaces(filterRequest.getCollectionPlaces())
                .build(), PageRequest.of(page, size));
        return SamplePageDto.builder()
                .totalElements(samplePage.getTotalElements())
                .totalPages(samplePage.getTotalPages())
                .samples(samplePage.getContent()
                        .stream()
                        .map(this::toSampleDto)
                        .toList())
                .build();
    }

    private SampleDto toSampleDto(Sample sample) {
        var sampleDto = mapper.toDto(sample);
        sampleDto.getAttachments().forEach(attachmentDto -> {
            var url = storageService.generateSas(mapper.toStorageFile(attachmentDto));
            attachmentDto.setFileSignedUrl(url);
        });
        sampleDto.setHasMailAttachment(hasMailAttachment(sampleDto.getAttachments()));
        return sampleDto;
    }

    private boolean hasMailAttachment(List<SampleAttachmentDto> attachments) {
        return attachments
                .stream()
                .anyMatch(attachmentDto -> attachmentDto.getFileUniqueName().startsWith("a"));
    }
}
