package com.ipms.activity.service.impl;

import com.ipms.activity.dto.CounterPartyDto;
import com.ipms.activity.mapper.CounterPartyMapper;
import com.ipms.activity.model.CounterParty;
import com.ipms.activity.repository.CounterPartyRepository;
import com.ipms.activity.service.CounterPartyService;
import com.ipms.activity.service.CounterRightOwnerService;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Service
public class CounterPartyServiceImpl implements CounterPartyService {
    private final CounterRightOwnerService counterRightOwnerService;
    private final CounterPartyRepository repository;
    private final CounterPartyMapper mapper;
    private final ProducerService producerService;

    @Override
    public CounterPartyDto save(CounterPartyDto dto) {
        var counterParty = mapper.toCounterParty(dto);
        dontAddCounterRightOwnerIfExist(counterParty);
        setCounterPartyForSuspect(counterParty);
        var saved = repository.save(counterParty);
        CounterPartyDto counterPartyDto = mapper.toCounterPartyDto(saved);
        sendTransferEvent(saved.getActivityId(), TransferType.UPDATE);
        return counterPartyDto;
    }

    private void dontAddCounterRightOwnerIfExist(CounterParty counterParty) {
        var counterRightOwnerId = counterParty.getCounterRightOwner().getId();
        if (Objects.nonNull(counterRightOwnerId)) {
            var counterRightOwner = counterRightOwnerService.getCounterRightOwnerById(counterRightOwnerId);
            counterParty.setCounterRightOwner(counterRightOwner);
        }
    }

    @Override
    public List<CounterPartyDto> saveBulk(List<CounterPartyDto> dtos) {
        if (Objects.nonNull(dtos)) {
            return dtos.stream()
                    .map(this::save)
                    .toList();
        }
        return Collections.emptyList();
    }

    @Override
    public CounterPartyDto update(Long id, CounterPartyDto dto) {
        var existing = getCounterPartyById(id);
        var counterParty = mapper.toCounterParty(dto, existing);
        setCounterPartyForSuspect(counterParty);
        var saved = repository.save(counterParty);
        sendTransferEvent(saved.getActivityId(), TransferType.UPDATE);
        return mapper.toCounterPartyDto(saved);
    }

    private void setCounterPartyForSuspect(CounterParty counterParty) {
        var suspect = counterParty.getSuspect();
        if (Objects.nonNull(suspect)) {
            suspect.setCounterParty(counterParty);
        }
    }

    @Override
    public List<CounterPartyDto> getAllByActivityId(Long activityId) {
        return repository.findAllByActivityId(activityId).stream()
                .map(mapper::toCounterPartyDto)
                .toList();
    }

    @Override
    public void delete(Long id, Long version) {
        CounterParty counterParty = getCounterPartyById(id).toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(counterParty);
        counterRightOwnerService.delete(counterParty.getCounterRightOwner().getId());
        sendTransferEvent(counterParty.getActivityId(), TransferType.UPDATE);
    }

    @Override
    public CounterPartyDto getById(Long id) {
        return mapper.toCounterPartyDto(getCounterPartyById(id));
    }

    @Override
    public CounterParty getCounterPartyById(Long id) {
        return repository.findById(id).orElseThrow();
    }

    private void sendTransferEvent(Long id, TransferType transferType) {
        producerService.sendTransfer(TransferEvent.builder()
                .type(transferType)
                .domainObjectId(id)
                .domain(Domain.IPMS_ACTIVITY)
                .build());
    }
}
