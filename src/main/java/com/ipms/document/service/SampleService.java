package com.ipms.document.service;

import com.ipms.document.dto.*;
import com.ipms.document.model.Sample;

import java.util.List;

public interface SampleService {
    SampleDto save(SampleManualDto documentManualDto);
    SampleDto update(SampleDto sampleDto, Long id);
    void delete(Long id, Long version);
    Sample getSampleById(Long id);
    SampleDto getById(Long id);
    List<SampleDto> getByIdIn(List<Long> ids);
    SamplePageDto getAll(SampleFilterRequest filterRequest, int page, int size);
}
