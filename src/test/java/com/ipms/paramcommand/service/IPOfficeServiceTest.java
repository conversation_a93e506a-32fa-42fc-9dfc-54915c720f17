package com.ipms.paramcommand.service;

import com.ipms.paramcommand.dto.IPOfficeDto;
import com.ipms.paramcommand.exception.IPOfficeNotFoundException;
import com.ipms.paramcommand.mapper.IPOfficeMapper;
import com.ipms.paramcommand.model.IPOffice;
import com.ipms.paramcommand.repository.IPOfficeRepository;
import com.ipms.paramcommand.service.impl.IPOfficeServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class IPOfficeServiceTest {
    @Mock
    private IPOfficeRepository repository;

    @Mock
    private IPOfficeMapper mapper;

    @MockBean
    private IPOfficeService service;

    @Before
    public void setUp() {
        service = new IPOfficeServiceImpl(repository, mapper);
    }

    @Test
    public void getAll_ShouldReturnListOfIPOfficeDtos() {
        List<IPOffice> ipOffices = List.of(
                IPOffice.builder().id(1L).nameTr("Office1").build(),
                IPOffice.builder().id(2L).nameTr("Office2").build()
        );

        List<IPOfficeDto> expectedDtos = List.of(
                IPOfficeDto.builder().id(1L).nameTr("Office1").build(),
                IPOfficeDto.builder().id(2L).nameTr("Office2").build()
        );

        when(repository.findAll()).thenReturn(ipOffices);
        when(mapper.toDtoList(ipOffices)).thenReturn(expectedDtos);

        List<IPOfficeDto> result = service.getAll();

        assertEquals(expectedDtos, result);
    }

    @Test
    public void getByCode_ShouldReturnIPOfficeDto_WhenCodeExists() {
        var code = "US";
        var ipOffice = IPOffice.builder()
                .id(1L)
                .code(code)
                .nameTr("Amerika Birleşik Devletleri")
                .build();

        var expectedDto = IPOfficeDto.builder()
                .id(1L)
                .code(code)
                .nameTr("Amerika Birleşik Devletleri")
                .build();

        when(repository.findByCode(code)).thenReturn(Optional.of(ipOffice));
        when(mapper.toDto(ipOffice)).thenReturn(expectedDto);

        var result = service.getByCode(code);

        assertEquals(expectedDto, result);
    }

    @Test
    public void getById_ShouldReturnIPOfficeDto_WhenIdExists() {
        var id = 1L;
        var ipOffice = IPOffice.builder()
                .id(id)
                .code("US")
                .nameTr("Amerika Birleşik Devletleri")
                .build();

        var expectedDto = IPOfficeDto.builder()
                .id(id)
                .code("US")
                .nameTr("Amerika Birleşik Devletleri")
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(ipOffice));
        when(mapper.toDto(ipOffice)).thenReturn(expectedDto);

        var result = service.getById(id);

        assertEquals(expectedDto, result);
    }

    @Test(expected = IPOfficeNotFoundException.class)
    public void getById_ShouldThrowException_WhenIdNotExists() {
        var id = 999L;

        when(repository.findById(id)).thenReturn(Optional.empty());

        service.getById(id);
    }

    @Test(expected = IPOfficeNotFoundException.class)
    public void getByCode_ShouldThrowException_WhenCodeNotExists() {
        var code = "NONEXISTENT";

        when(repository.findByCode(code)).thenReturn(Optional.empty());

        service.getByCode(code);
    }
}
