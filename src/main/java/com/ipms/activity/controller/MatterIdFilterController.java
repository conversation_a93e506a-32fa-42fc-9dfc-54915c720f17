package com.ipms.activity.controller;

import com.ipms.activity.dto.ActivityFilterRequest;
import com.ipms.activity.service.MatterIdFilterService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
public class MatterIdFilterController {

    private final MatterIdFilterService service;

    @GetMapping("/matter-ids/by-type-and-status")
    public BaseResponse<List<Long>> getMatterIdsByTypesAndStatuses(ActivityFilterRequest filterRequest) {
        return BaseResponse.<List<Long>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.filterMatterIdsByTypesAndStatuses(filterRequest))
                .build();
    }

}
